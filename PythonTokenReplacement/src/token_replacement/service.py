"""Core token replacement service"""

import os
import re
import configparser
import shutil
from collections import deque
from datetime import datetime
from typing import Dict, Optional, List
from .exceptions import ConfigFileNotFoundError, TemplateFileNotFoundError, TokenNotFoundError

class TokenReplacementService:
    """Service to replace parameterized tokens in template files
    
    Supports:
    - Token replacement using &token; syntax
    - Conditional processing with #if/#else/#endif directives
    - Environment variable overrides
    - Line commenting for missing tokens
    - Automatic backup functionality
    """
    
    # Pattern to match &token; expressions only (matching Java implementation)
    TOKEN_PATTERN = re.compile(r'&([^&;]+);')
    
    def __init__(self, service_name: str = "ai-service", env_overrides: Optional[Dict[str, str]] = None):
        self.service_name = service_name
        self.config_data: Dict[str, str] = {}
        self.env_overrides: Dict[str, str] = env_overrides or {}
        
    
    def load_config(self, config_file_path: str) -> None:
        """Load configuration from .env.ini file"""
        if not os.path.exists(config_file_path):
            raise ConfigFileNotFoundError(f"Configuration file not found: {config_file_path}")
        
        config = configparser.ConfigParser()
        config.read(config_file_path)
        
        # Flatten all sections into a single dictionary
        for section_name in config.sections():
            for key, value in config[section_name].items():
                self.config_data[key] = value
                
        print(f"Loaded {len(self.config_data)} configuration parameters")
    
    def add_config_value(self, key: str, value: str) -> None:
        """Add a single configuration value"""
        self.config_data[key] = value
        print(f"Added config value: {key} = {value}")
    
    def add_environment_override(self, name: str, value: str) -> None:
        """Add environment variable override for conditional processing"""
        self.env_overrides[name] = value
        print(f"Added environment override: {name} = {value}")
    
    def get_environment_value(self, name: str) -> Optional[str]:
        """Get environment variable value, checking overrides first"""
        # Check overrides first
        if name in self.env_overrides:
            return self.env_overrides[name]
        # Fall back to system environment
        return os.environ.get(name)
    
    def replace_tokens(self, template_content: str) -> str:
        """Replace parameterized tokens in template content
        
        Supports:
        - &token; syntax for token replacement
        - #if name=value ... #endif conditional directives
        - #else blocks within conditional directives
        - Line commenting for missing tokens
        
        Args:
            template_content: The template content to process
            
        Returns:
            Processed content with tokens replaced and conditionals evaluated
        """
        if not template_content:
            return template_content
        
        lines = template_content.split('\n')
        result = []
        
        # Stack to track conditional processing state
        if_stack = deque()  # Stack of boolean values for nested #if conditions
        else_stack = deque()  # Track if we're in an #else block
        current_active = True
        in_else_block = False
        
        # Patterns for conditional directives
        if_pattern = re.compile(r'#if\s+([a-zA-Z0-9_]+)=(.*)$')
        else_pattern = re.compile(r'#else\b')
        endif_pattern = re.compile(r'#endif\b')
        
        for line_num, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Check for conditional directives
            if_match = if_pattern.match(stripped)
            else_match = else_pattern.match(stripped)
            endif_match = endif_pattern.match(stripped)
            
            if if_match:
                # Process #if directive
                var_name = if_match.group(1)
                expected_value = if_match.group(2)
                env_value = self.get_environment_value(var_name)
                
                condition = env_value is not None and env_value == expected_value
                
                # Push current state to stack
                if_stack.append(current_active)
                else_stack.append(in_else_block)
                
                # Update current state
                current_active = current_active and condition
                in_else_block = False
                
                continue  # Don't include the directive in output
                
            elif else_match:
                # Process #else directive
                if not if_stack:
                    print(f"Warning: #else without matching #if at line {line_num}")
                elif in_else_block:
                    print(f"Warning: duplicate #else at line {line_num}")
                else:
                    in_else_block = True
                    # Flip the condition: if we were active, now we're not (and vice versa)
                    # but only if the parent context is active
                    parent_active = if_stack[-1] if if_stack else True
                    current_active = parent_active and not current_active
                
                continue  # Don't include the directive in output
                
            elif endif_match:
                # Process #endif directive
                if not if_stack:
                    print(f"Warning: #endif without matching #if at line {line_num}")
                else:
                    current_active = if_stack.pop()
                    in_else_block = else_stack.pop()
                
                continue  # Don't include the directive in output
            
            # Process regular content lines
            if current_active:
                processed_line = self._process_line(line)
                result.append(processed_line)
        
        # Check for unclosed #if directives
        if if_stack:
            print(f"Warning: {len(if_stack)} unclosed #if directive(s) at end of file")
        
        return '\n'.join(result)
    
    def _process_line(self, line: str) -> str:
        """Process a single line, replacing tokens and commenting out lines with missing tokens"""
        if not line or not line.strip():
            return line
        
        # Check if the line contains any tokens
        token_matches = list(self.TOKEN_PATTERN.finditer(line))
        if not token_matches:
            # No tokens in this line, return as is
            return line
        
        # Check if all tokens in this line have values
        all_tokens_have_values = True
        for match in token_matches:
            token_name = match.group(1)
            if token_name not in self.config_data or not self.config_data[token_name]:
                all_tokens_have_values = False
                break
        
        # If any token is missing a value, comment out the entire line
        if not all_tokens_have_values:
            stripped = line.strip()
            if stripped.startswith('#'):
                # Already commented, return as is
                return line
            else:
                # Comment out the line
                missing_tokens = [match.group(1) for match in token_matches 
                                if match.group(1) not in self.config_data or not self.config_data[match.group(1)]]
                print(f"Warning: Commenting out line with missing tokens {missing_tokens}: {stripped}")
                return f"# {line}"
        
        # All tokens have values, proceed with replacement
        def replace_token(match):
            token_name = match.group(1)
            replacement = self.config_data.get(token_name, '')
            print(f"Substituting token: &{token_name}; with value: {replacement}")
            return replacement
        
        return self.TOKEN_PATTERN.sub(replace_token, line)
    
    def create_backup(self, file_path: str) -> Optional[str]:
        """Create a timestamped backup of an existing file"""
        if not os.path.exists(file_path):
            return None
        
        # Create backup directory
        file_dir = os.path.dirname(file_path)
        backup_dir = os.path.join(file_dir, 'backup')
        os.makedirs(backup_dir, exist_ok=True)
        
        # Generate timestamp in yyyymmddhhmmss format
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        file_name = os.path.basename(file_path)
        name, ext = os.path.splitext(file_name)
        backup_name = f"{name}-{timestamp}{ext}"
        backup_path = os.path.join(backup_dir, backup_name)
        
        # Copy file to backup location
        shutil.copy2(file_path, backup_path)
        print(f"Backed up existing file: {file_path} -> {backup_path}")
        
        return backup_path
    
    def process_template(self, template_path: str, output_path: str, create_backup: bool = True) -> None:
        """Process template file and create output file
        
        Args:
            template_path: Path to the template file
            output_path: Path where the processed file should be saved
            create_backup: Whether to backup existing output file
        """
        if not os.path.exists(template_path):
            raise TemplateFileNotFoundError(f"Template file not found: {template_path}")
        
        # Create backup of existing output file if requested
        if create_backup and os.path.exists(output_path):
            self.create_backup(output_path)
        
        # Read template content
        with open(template_path, 'r', encoding='utf-8') as file:
            template_content = file.read()
        
        # Replace tokens
        processed_content = self.replace_tokens(template_content)
        
        # Ensure output directory exists
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # Write processed content
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write(processed_content)
        
        print(f"Successfully created {output_path}")
    
    def process_directory(self, directory_path: str, file_extension: str = '.template') -> List[str]:
        """Process all template files in a directory
        
        Args:
            directory_path: Directory to search for template files
            file_extension: Extension of template files to process
            
        Returns:
            List of output files created
        """
        if not os.path.exists(directory_path):
            raise FileNotFoundError(f"Directory not found: {directory_path}")
        
        output_files = []
        template_files = []
        
        # Find all template files recursively
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if file.endswith(file_extension):
                    template_files.append(os.path.join(root, file))
        
        if not template_files:
            print(f"No template files found with extension '{file_extension}' in {directory_path}")
            return output_files
        
        print(f"Found {len(template_files)} template file(s) to process")
        
        # Process each template file
        for template_path in template_files:
            try:
                # Determine output path by removing the template extension
                if template_path.endswith(file_extension):
                    output_path = template_path[:-len(file_extension)]
                else:
                    output_path = template_path + '.processed'
                
                self.process_template(template_path, output_path)
                output_files.append(output_path)
                
            except Exception as e:
                print(f"Error processing {template_path}: {e}")
        
        return output_files
    
    def get_config_summary(self) -> Dict[str, str]:
        """Get summary of loaded configuration"""
        return self.config_data.copy()
    
    def has_config_value(self, key: str) -> bool:
        """Check if a configuration key has a defined value"""
        return key in self.config_data and bool(self.config_data[key])
    
    def get_config_value(self, key: str) -> Optional[str]:
        """Get the value for a specific configuration key"""
        return self.config_data.get(key)
    
    def clear_config(self) -> None:
        """Clear all configuration values and reload defaults"""
        self.config_data.clear()
        print("Cleared all configuration values")