#!/usr/bin/env python3
"""
<PERSON>ript to generate an outline using ProposalOutlineService

This script demonstrates how to use the ProposalOutlineService to generate
a detailed outline for a proposal based on the provided parameters.
"""

import asyncio
import json
from typing import Dict, Any
from services.proposal.outline import ProposalOutlineService
from services.proposal.utilities import ProposalUtilities
from loguru import logger

async def generate_outline_example():
    """
    Generate an outline using the provided information
    """
    
    # Configuration parameters
    opportunity_id = "vSe1unlCj9"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    source = "custom"
    
    # Read table of contents from file
    try:
        table_of_contents_data = ProposalUtilities.read_json_from_file("table-of-contents.json")
        table_of_contents = table_of_contents_data.get("table_of_contents", [])
        logger.info(f"Loaded table of contents with {len(table_of_contents)} sections")
    except FileNotFoundError:
        logger.error("table-of-contents.json file not found!")
        return
    except Exception as e:
        logger.error(f"Error reading table-of-contents.json: {e}")
        return
    
    # Initialize the ProposalOutlineService
    logger.info("Initializing ProposalOutlineService...")
    outline_service = ProposalOutlineService(
        embedding_api_url="http://ai.kontratar.com:5000",
        llm_api_url="http://ai.kontratar.com:11434"
    )
    
    try:
        # Generate the outline
        logger.info("Generating outline...")
        result = await outline_service.generate_outline(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            table_of_contents=table_of_contents
        )
        
        # Save the result to a file
        output_filename = f"generated_outline_{opportunity_id}_{tenant_id}.json"
        ProposalUtilities.save_json_to_file(result, output_filename)
        logger.info(f"Outline generated successfully and saved to {output_filename}")
        
        # Print summary
        outlines = result.get("outlines", [])
        logger.info(f"Generated outline contains {len(outlines)} main sections")
        
        for i, outline in enumerate(outlines, 1):
            title = outline.get("title", "Unknown")
            subsections = outline.get("subsections", [])
            logger.info(f"  Section {i}: {title} ({len(subsections)} subsections)")
        
        return result
        
    except Exception as e:
        logger.error(f"Error generating outline: {e}")
        raise

async def main():
    """
    Main function to run the outline generation script
    """
    logger.info("Starting outline generation script...")
    
    try:
        result = await generate_outline_example()
        if result:
            logger.info("Outline generation completed successfully!")
        else:
            logger.error("Outline generation failed!")
    except Exception as e:
        logger.error(f"Script execution failed: {e}")
        raise

if __name__ == "__main__":
    # Run the script
    asyncio.run(main())
