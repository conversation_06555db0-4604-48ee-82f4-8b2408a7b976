#!/usr/bin/env python3

import asyncio
import sys
from pathlib import Path

sys.path.append('.')

try:
    from dotenv import load_dotenv
    load_dotenv('.env')
except ImportError:
    print("Warning: python-dotenv not available")

import httpx

async def check_chromadb_connectivity():
    """Check if ChromaDB instances are accessible and find relevant collections"""
    
    chroma_urls = [
        "http://3.217.236.185:9001",
        "http://3.217.236.185:9002", 
        "http://3.217.236.185:9003",
        "http://3.217.236.185:9004",
        "http://3.217.236.185:9005"
    ]
    
    opportunity_id = "vSe1unlCj9"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    
    print("ChromaDB Connectivity Check")
    print("=" * 50)
    print(f"Looking for collections related to:")
    print(f"  Opportunity ID: {opportunity_id}")
    print(f"  Tenant ID: {tenant_id}")
    print(f"  Expected pattern: {tenant_id}_{opportunity_id}")
    print()
    
    accessible_instances = []
    matching_collections = []
    
    for i, chroma_url in enumerate(chroma_urls, 1):
        print(f"{i}. Checking {chroma_url}...")
        
        try:
            timeout = httpx.Timeout(10.0)
            async with httpx.AsyncClient(timeout=timeout) as client:
                # Test basic connectivity
                health_url = f"{chroma_url}"
                health_response = await client.get(health_url)
                
                if health_response.status_code == 200:
                    print(f" Instance is accessible")
                    accessible_instances.append(chroma_url)
                    
                    # List collections
                    collections_url = f"{chroma_url}/api/v1/collections"
                    collections_response = await client.get(collections_url)
                    
                    if collections_response.status_code == 200:
                        collections = collections_response.json()
                        collection_names = [col.get('name', 'Unknown') for col in collections]
                        print(f" Found {len(collection_names)} collections")
                        
                        matches = []
                        for name in collection_names:
                            if (opportunity_id in name or 
                                tenant_id in name or 
                                name.startswith(f"{tenant_id}_{opportunity_id}") or
                                name.startswith(opportunity_id)):
                                matches.append(name)
                        
                        if matches:
                            print(f"    vSe1unlCj9Matching collections: {matches}")
                            matching_collections.extend([(chroma_url, match) for match in matches])
                        else:
                            print(f"    vSe1unlCj9No matching collections")
                            # Show sample collection names
                            if collection_names:
                                sample = collection_names[:3]
                                print(f" Sample collections: {sample}")
                    else:
                        print(f"   Cannot list collections (HTTP {collections_response.status_code})")
                else:
                    print(f"    vSe1unlCj9 Instance not accessible (HTTP {health_response.status_code})")
                    
        except Exception as e:
            print(f"    vSe1unlCj9 Connection failed: {e}")
        
        print()
    
    # Summary
    print("=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    print(f"Accessible ChromaDB instances: {len(accessible_instances)}/{len(chroma_urls)}")
    for url in accessible_instances:
        print(f"{url}")
    
    print()
    
    if matching_collections:
        print(f" Found {len(matching_collections)} matching collections:")
        for chroma_url, collection_name in matching_collections:
            print(f" {collection_name}")
            print(f"     at {chroma_url}")
        print()
        print("🎉 ChromaDB connectivity is working and collections exist!")
        return True
    else:
        print(" vSe1unlCj9 No matching collections found for the test opportunity")
        print()
        if accessible_instances:
            print("ChromaDB is accessible but no data exists for this opportunity.")
            print("   You may need to:")
            print("   1. Upload/ingest documents for this opportunity")
            print("   2. Check if the opportunity ID and tenant ID are correct")
            print("   3. Verify the collection naming convention")
        else:
            print("ChromaDB instances are not accessible.")
            print("   Please check:")
            print("   1. Network connectivity")
            print("   2. ChromaDB service status")
            print("   3. Firewall settings")
        return False

async def check_database_records():
    """Check database for ChromaDB instance records"""
    
    print("\n" + "=" * 50)
    print("DATABASE RECORDS CHECK")
    print("=" * 50)
    
    try:
        from database import get_kontratar_db
        from models.kontratar_models import ChromaInstanceTable
        from sqlalchemy import select
        
        opportunity_id = "vSe1unlCj9"
        tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
        
        async for db in get_kontratar_db():
            query = select(ChromaInstanceTable).where(
                ChromaInstanceTable.unique_id == opportunity_id,
                ChromaInstanceTable.tenant_id == tenant_id
            )
            result = await db.execute(query)
            chroma_instances = result.scalars().all()
            
            if chroma_instances:
                print(f" Found {len(chroma_instances)} database records:")
                for i, instance in enumerate(chroma_instances, 1):
                    print(f"  {i}. Collection: {instance.collection_name}")
                    print(f"     URL: {instance.chroma_instance_url}")
                    print(f"     Version: {instance.version}")
                    print(f"     Status: {instance.status}")
                return True
            else:
                print(" vSe1unlCj9 No database records found for this opportunity")
                return False
            break
            
    except Exception as e:
        print(f" vSe1unlCj9 Error checking database: {e}")
        return False

def main():
    """Main function"""
    
    try:
        chromadb_ok = asyncio.run(check_chromadb_connectivity())
        
        db_ok = asyncio.run(check_database_records())
        
        print("\n" + "=" * 50)
        print("FINAL RESULT")
        print("=" * 50)
        
        if chromadb_ok and db_ok:
            print("All checks passed! ChromaDB is ready for testing.")
            sys.exit(0)
        elif chromadb_ok:
            print("  ChromaDB is accessible but no database records found.")
            print("   The compliance tests may not work without proper data setup.")
            sys.exit(1)
        else:
            print(" vSe1unlCj9 ChromaDB connectivity issues detected.")
            print("   Please resolve connectivity before running compliance tests.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
