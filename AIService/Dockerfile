# ---- Builder Stage ----
    FROM python:3.11-slim AS builder

    # Set environment variables
    ENV PYTHONDONTWRITEBYTECODE=1 \
        PYTHONUNBUFFERED=1 \
        PYTHONHASHSEED=random \
        PIP_NO_CACHE_DIR=1 \
        PIP_DISABLE_PIP_VERSION_CHECK=1 \
        PIP_DEFAULT_TIMEOUT=100 \
        DEBIAN_FRONTEND=noninteractive
    
    # Install build dependencies
    RUN apt-get update && \
        apt-get install -y --no-install-recommends \
        build-essential \
        pkg-config \
        libpq-dev && \
        apt-get clean && \
        rm -rf /var/lib/apt/lists/* \
        && apt-get purge -y --auto-remove
    
    # Create virtual user for build
    RUN useradd -m builder
    
    ENV HOME=/home/<USER>
    
    # Install Python dependencies
    WORKDIR /build
    COPY requirements.txt .
    RUN pip install --user -r requirements.txt
    
    # ---- Runtime Stage ----
    FROM python:3.11-slim
    
    # Build-time arguments (can be overridden at build time)
    ARG APP_USER=kontratar
    ARG APP_UID=1000
    ARG APP_GID=1000
    ARG APP_PORT=3011
    ARG APP_HOST=0.0.0.0

    ARG DEBUG
    ARG KONTRATAR_DB_HOST
    ARG KONTRATAR_DB_PORT
    ARG KONTRATAR_DB_NAME
    ARG KONTRATAR_DB_USER
    ARG KONTRATAR_DB_PASSWORD
    ARG CUSTOMER_DB_HOST
    ARG CUSTOMER_DB_PORT
    ARG CUSTOMER_DB_NAME
    ARG CUSTOMER_DB_USER
    ARG CUSTOMER_DB_PASSWORD

    ARG CHROMADB_PORT_1
    ARG CHROMADB_PORT_2
    ARG CHROMADB_PORT_3
    ARG CHROMADB_PORT_4
    ARG CHROMADB_PORT_5
    ARG CHROMADB_PROTOCOL

    ARG CHROMADB_SERVER_NAME
    ARG GOOGLE_CLIENT_ID
    ARG GOOGLE_CLIENT_SECRET
    ARG LANGCHAIN_API_KEY
    ARG LANGCHAIN_PROJECT
    ARG LANGCHAIN_TRACING_V2
    ARG SCHEDULER_ENABLE_ON_STARTUP
    ARG SCHEDULER_INTERVAL_SECONDS
    ARG GEMINI_API_KEY
    ARG LLM_PROVIDER
    ARG LLM_MODEL
    ARG LLM_URL

    # Set environment variables (can be overridden at runtime)
    ENV DEBIAN_FRONTEND=noninteractive \
        APP_USER=${APP_USER} \
        APP_UID=${APP_UID} \
        APP_GID=${APP_GID} \
        APP_PORT=${APP_PORT} \
        APP_HOST=${APP_HOST} \
        DEBUG=${DEBUG} \
        KONTRATAR_DB_HOST=${KONTRATAR_DB_HOST} \
        KONTRATAR_DB_PORT=${KONTRATAR_DB_PORT} \
        KONTRATAR_DB_NAME=${KONTRATAR_DB_NAME} \
        KONTRATAR_DB_USER=${KONTRATAR_DB_USER} \
        KONTRATAR_DB_PASSWORD=${KONTRATAR_DB_PASSWORD} \
        CUSTOMER_DB_HOST=${CUSTOMER_DB_HOST} \
        CUSTOMER_DB_PORT=${CUSTOMER_DB_PORT} \
        CUSTOMER_DB_NAME=${CUSTOMER_DB_NAME} \
        CUSTOMER_DB_USER=${CUSTOMER_DB_USER} \
        CUSTOMER_DB_PASSWORD=${CUSTOMER_DB_PASSWORD} \
        CHROMADB_PORT_1=${CHROMADB_PORT_1} \
        CHROMADB_PORT_2=${CHROMADB_PORT_2} \
        CHROMADB_PORT_3=${CHROMADB_PORT_3} \
        CHROMADB_PORT_4=${CHROMADB_PORT_4} \
        CHROMADB_PORT_5=${CHROMADB_PORT_5} \
        CHROMADB_PROTOCOL=${CHROMADB_PROTOCOL} \
        CHROMADB_SERVER_NAME=${CHROMADB_SERVER_NAME} \
        GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID} \
        GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET} \
        SCHEDULER_ENABLE_ON_STARTUP=${SCHEDULER_ENABLE_ON_STARTUP} \
        SCHEDULER_INTERVAL_SECONDS=${SCHEDULER_INTERVAL_SECONDS} \
        LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY} \
        LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT} \
        LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2} \
        GEMINI_API_KEY=${GEMINI_API_KEY} \
        LLM_PROVIDER=${LLM_PROVIDER} \
        LLM_MODEL=${LLM_MODEL} \
        LLM_URL=${LLM_URL} \
        PATH="/home/<USER>/.local/bin:$PATH" \
        PYTHONPATH="/app"
    
    # Install runtime dependencies only
    RUN apt-get update && \
        apt-get install -y --no-install-recommends \
        libpq5 \
        curl && \
        apt-get clean && \
        rm -rf /var/lib/apt/lists/* \
        && apt-get purge -y --auto-remove
    
    # Create application user structure using build args
    RUN groupadd --gid ${APP_GID} ${APP_USER} && \
        useradd --uid ${APP_UID} --gid ${APP_USER} --shell /bin/bash --create-home ${APP_USER} && \
        mkdir -p /app /logs && \
        chown -R ${APP_USER}:${APP_USER} /app /logs /home/<USER>
    
    # Copy installed packages from builder
    COPY --from=builder --chown=${APP_USER}:${APP_USER} /home/<USER>/.local /home/<USER>/.local
    
    # Set working directory to filesystem root so PYTHONPATH can resolve top-level package
    WORKDIR /
    
    # Copy application code into /AIService so 'AIService' is importable as a package
    COPY --chown=${APP_USER}:${APP_USER} . /AIService
    
    # Configure logging
    RUN mkdir -p /logs && \
        ln -sf /dev/stdout /logs/AIService.log && \
        chown ${APP_USER}:${APP_USER} /logs/AIService.log
    
    # Switch to non-root user
    USER ${APP_USER}
    
    # Expose the port the app runs on (can be overridden)
    EXPOSE ${APP_PORT}
    
    # Health check to ensure the service is running
    HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
        CMD curl -f http://${APP_HOST}:${APP_PORT}/health || exit 1
    
    # Ensure Python can import the top-level 'AIService' package path
    ENV PYTHONPATH="/AIService:/home/<USER>/.local/lib/python3.11/site-packages"
    
    # Use exec form to ensure proper signal handling
    CMD ["python3", "AIService/start.py"]
