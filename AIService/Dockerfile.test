# ---- Builder Stage ----
FROM python:3.11-slim AS builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=100 \
    DEBIAN_FRONTEND=noninteractive

# Install build dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    pkg-config \
    libpq-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* \
    && apt-get purge -y --auto-remove

# Create virtual user for build
RUN useradd -m builder

ENV HOME=/home/<USER>

# Install Python dependencies
WORKDIR /build
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

# ---- Application Builder Stage ----
FROM python:3.11-slim AS app-builder

# Copy source code and create application bundle
WORKDIR /src
COPY . .

# Create a minimal application bundle with only necessary files
RUN mkdir -p /app-bundle && \
    # Copy only essential Python files (excluding __pycache__, tests, etc.)
    find . -name "*.py" -not -path "./__pycache__/*" -not -path "./tests/*" | \
    xargs -I {} cp --parents {} /app-bundle/ && \
    # Copy configuration files
    cp -r config*.py /app-bundle/ 2>/dev/null || true && \
    cp -r *.json /app-bundle/ 2>/dev/null || true && \
    cp -r *.txt /app-bundle/ 2>/dev/null || true && \
    # Copy essential directories (services, models, controllers, utils)
    cp -r services/ /app-bundle/ 2>/dev/null || true && \
    cp -r models/ /app-bundle/ 2>/dev/null || true && \
    cp -r controllers/ /app-bundle/ 2>/dev/null || true && \
    cp -r utils/ /app-bundle/ 2>/dev/null || true && \
    # Remove any __pycache__ directories
    find /app-bundle -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true

# ---- Runtime Stage ----
FROM python:3.11-slim

# Build-time arguments (can be overridden at build time)
ARG APP_USER=kontratar
ARG APP_UID=1000
ARG APP_GID=1000
ARG APP_PORT=3011
ARG APP_HOST=0.0.0.0
ARG LANGCHAIN_TRACING_V2
ARG LANGCHAIN_API_KEY
ARG LANGCHAIN_PROJECT
ARG CHROMADB_PORT_1
ARG CHROMADB_PORT_2
ARG CHROMADB_PORT_3
ARG CHROMADB_PORT_4
ARG CHROMADB_PORT_5
ARG CHROMADB_PROTOCOL
ARG CHROMADB_SERVER_DOMAIN

# Set environment variables (can be overridden at runtime)
ENV DEBIAN_FRONTEND=noninteractive \
    APP_USER=${APP_USER} \
    APP_UID=${APP_UID} \
    APP_GID=${APP_GID} \
    APP_PORT=${APP_PORT} \
    APP_HOST=${APP_HOST} \
    LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2} \
    LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY} \
    LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT} \
    CHROMADB_PORT_1=${CHROMADB_PORT_1} \
    CHROMADB_PORT_2=${CHROMADB_PORT_2} \
    CHROMADB_PORT_3=${CHROMADB_PORT_3} \
    CHROMADB_PORT_4=${CHROMADB_PORT_4} \
    CHROMADB_PORT_5=${CHROMADB_PORT_5} \
    CHROMADB_PROTOCOL=${CHROMADB_PROTOCOL} \
    CHROMADB_SERVER_NAME=${CHROMADB_SERVER_NAME} \
    PATH="/home/<USER>/.local/bin:$PATH" \
    PYTHONPATH="/app"

# Install runtime dependencies only
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libpq5 \
    curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* \
    && apt-get purge -y --auto-remove

# Create application user structure using build args
RUN groupadd --gid ${APP_GID} ${APP_USER} && \
    useradd --uid ${APP_UID} --gid ${APP_USER} --shell /bin/bash --create-home ${APP_USER} && \
    mkdir -p /app /app/logs /app/data && \
    chown -R root:root /app && \
    chmod -R 755 /app && \
    chown -R ${APP_USER}:${APP_USER} /app/logs /app/data /home/<USER>
    chmod -R 755 /app/logs /app/data

# Copy installed packages from builder
COPY --from=builder --chown=root:root /home/<USER>/.local /opt/python-packages
ENV PATH="/opt/python-packages/bin:$PATH" \
    PYTHONPATH="/opt/python-packages/lib/python3.11/site-packages:/app"

# Copy minimal application bundle (no source code access for exec users)
COPY --from=app-builder --chown=root:root /app-bundle /app
RUN chmod -R 755 /app

# Set working directory
WORKDIR /app

# Create a minimal startup script in user's home (not in /app)
RUN echo '#!/bin/bash' > /home/<USER>/run_app.sh && \
    echo 'cd /app' >> /home/<USER>/run_app.sh && \
    echo 'exec python3 start.py' >> /home/<USER>/run_app.sh && \
    chown ${APP_USER}:${APP_USER} /home/<USER>/run_app.sh && \
    chmod 750 /home/<USER>/run_app.sh

# Configure proper logging - create log file and link to stdout for Docker logs
RUN touch /app/logs/AIService.log && \
    chown ${APP_USER}:${APP_USER} /app/logs/AIService.log && \
    chmod 644 /app/logs/AIService.log

# Switch to non-root user
USER ${APP_USER}

# Expose the port the app runs on (can be overridden)
EXPOSE ${APP_PORT}

# Health check to ensure the service is running
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://${APP_HOST}:${APP_PORT}/health || exit 1

# Use exec form to ensure proper signal handling
CMD ["/home/<USER>/run_app.sh"]