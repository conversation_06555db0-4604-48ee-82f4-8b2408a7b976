"""
Test module for the proposal context search endpoint.

This module tests the web search functionality that gathers organizational context
to enhance proposal generation by understanding the posting organization's perspective.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import HTTPException

# Import the main app and dependencies
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import app
from routers.feedback import ProposalContextSearchRequest, ProposalContextSearchResponse


class TestProposalContextSearch:
    """Test class for proposal context search functionality."""
    
    def setup_method(self):
        """Set up test client and common test data."""
        self.client = TestClient(app)
        self.test_request = {
            "opportunity_id": "test-opportunity-123",
            "tenant_id": "test-tenant-456",
            "source": "custom",
            "organization_name": "Department of Defense",
            "additional_context": "Focus on cybersecurity initiatives"
        }
        
    @patch('routers.feedback.WebSearchService')
    @patch('routers.feedback.ProposalOutlineService')
    def test_search_proposal_context_success(self, mock_outline_service, mock_web_search_service):
        """Test successful proposal context search."""
        # Mock web search service
        mock_web_search_instance = MagicMock()
        mock_web_search_service.return_value = mock_web_search_instance
        mock_web_search_instance.is_available.return_value = True
        
        # Mock search results
        async def mock_search_stream(*args, **kwargs):
            yield "The Department of Defense is focused on modernizing cybersecurity infrastructure."
            yield " Recent initiatives include zero-trust architecture implementation."
            yield " Key priorities include threat detection and response capabilities."
            yield " Recommended approach: emphasize proven security frameworks and compliance."
        
        mock_web_search_instance.search_and_stream = mock_search_stream
        
        # Mock outline service
        mock_outline_instance = MagicMock()
        mock_outline_service.return_value = mock_outline_instance
        
        # Mock opportunity record
        mock_opportunity = MagicMock()
        mock_opportunity.title = "Cybersecurity Enhancement Services"
        mock_opportunity.description = "Seeking advanced cybersecurity solutions"
        mock_opportunity.office = "Department of Defense"
        
        async def mock_get_opportunity(*args, **kwargs):
            return mock_opportunity
        
        mock_outline_instance.get_opportunity = mock_get_opportunity
        
        # Make the request
        response = self.client.post("/proposals/search-context", json=self.test_request)
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert data["opportunity_id"] == self.test_request["opportunity_id"]
        assert data["tenant_id"] == self.test_request["tenant_id"]
        assert "Department of Defense" in data["organization_context"]
        assert "cybersecurity" in data["organization_context"].lower()
        assert len(data["key_insights"]) > 0
        assert data["recommended_approach"]
        assert "Conducted comprehensive research" in data["search_summary"]
        
    @patch('routers.feedback.WebSearchService')
    def test_search_proposal_context_web_search_unavailable(self, mock_web_search_service):
        """Test when web search service is unavailable."""
        # Mock web search service as unavailable
        mock_web_search_instance = MagicMock()
        mock_web_search_service.return_value = mock_web_search_instance
        mock_web_search_instance.is_available.return_value = False
        
        # Make the request
        response = self.client.post("/proposals/search-context", json=self.test_request)
        
        # Assertions
        assert response.status_code == 503
        assert "Web search service is not available" in response.json()["detail"]
        
    @patch('routers.feedback.WebSearchService')
    @patch('routers.feedback.ProposalOutlineService')
    def test_search_proposal_context_opportunity_not_found(self, mock_outline_service, mock_web_search_service):
        """Test when opportunity cannot be found."""
        # Mock web search service as available
        mock_web_search_instance = MagicMock()
        mock_web_search_service.return_value = mock_web_search_instance
        mock_web_search_instance.is_available.return_value = True
        
        # Mock outline service to raise exception
        mock_outline_instance = MagicMock()
        mock_outline_service.return_value = mock_outline_instance
        
        async def mock_get_opportunity_error(*args, **kwargs):
            raise Exception("Opportunity not found")
        
        mock_outline_instance.get_opportunity = mock_get_opportunity_error
        
        # Make the request
        response = self.client.post("/proposals/search-context", json=self.test_request)
        
        # Assertions
        assert response.status_code == 404
        assert "Opportunity not found" in response.json()["detail"]
        
    @patch('routers.feedback.WebSearchService')
    @patch('routers.feedback.ProposalOutlineService')
    def test_search_proposal_context_no_search_results(self, mock_outline_service, mock_web_search_service):
        """Test when web search returns no results."""
        # Mock web search service
        mock_web_search_instance = MagicMock()
        mock_web_search_service.return_value = mock_web_search_instance
        mock_web_search_instance.is_available.return_value = True
        
        # Mock empty search results
        async def mock_empty_search_stream(*args, **kwargs):
            return
            yield  # This will never execute
        
        mock_web_search_instance.search_and_stream = mock_empty_search_stream
        
        # Mock outline service
        mock_outline_instance = MagicMock()
        mock_outline_service.return_value = mock_outline_instance
        
        mock_opportunity = MagicMock()
        mock_opportunity.title = "Test Opportunity"
        mock_opportunity.office = "Test Agency"
        
        async def mock_get_opportunity(*args, **kwargs):
            return mock_opportunity
        
        mock_outline_instance.get_opportunity = mock_get_opportunity
        
        # Make the request
        response = self.client.post("/proposals/search-context", json=self.test_request)
        
        # Assertions
        assert response.status_code == 500
        assert "Web search returned no results" in response.json()["detail"]
        
    def test_search_proposal_context_invalid_request(self):
        """Test with invalid request data."""
        invalid_request = {
            "opportunity_id": "",  # Empty opportunity ID
            "tenant_id": "test-tenant",
            "source": "invalid-source"
        }
        
        response = self.client.post("/proposals/search-context", json=invalid_request)
        
        # Should return validation error
        assert response.status_code == 422
        
    @patch('routers.feedback.WebSearchService')
    @patch('routers.feedback.ProposalOutlineService')
    def test_search_proposal_context_fallback_organization_extraction(self, mock_outline_service, mock_web_search_service):
        """Test organization name extraction from opportunity when not provided."""
        # Mock web search service
        mock_web_search_instance = MagicMock()
        mock_web_search_service.return_value = mock_web_search_instance
        mock_web_search_instance.is_available.return_value = True
        
        # Mock search results
        async def mock_search_stream(*args, **kwargs):
            yield "Test organization context information."
        
        mock_web_search_instance.search_and_stream = mock_search_stream
        
        # Mock outline service
        mock_outline_instance = MagicMock()
        mock_outline_service.return_value = mock_outline_instance
        
        # Mock opportunity record with agency field
        mock_opportunity = MagicMock()
        mock_opportunity.title = "Test Opportunity"
        mock_opportunity.description = "Test description"
        mock_opportunity.office = None
        mock_opportunity.agency = "Test Agency"
        mock_opportunity.department = None
        
        async def mock_get_opportunity(*args, **kwargs):
            return mock_opportunity
        
        mock_outline_instance.get_opportunity = mock_get_opportunity
        
        # Request without organization_name
        request_without_org = {
            "opportunity_id": "test-opportunity-123",
            "tenant_id": "test-tenant-456",
            "source": "custom"
        }
        
        # Make the request
        response = self.client.post("/proposals/search-context", json=request_without_org)
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert "Test Agency" in data["search_summary"]


if __name__ == "__main__":
    pytest.main([__file__])
