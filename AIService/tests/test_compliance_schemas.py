import pytest
import json
from services.proposal.compliance_schemas import (
    StructureComplianceSchema,
    ContentComplianceResponse,
    ComplianceValidator,
    VolumeTitle,
    StructureSection,
    StructureVolume,
    ContentCompliance
)
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))


class TestStructureComplianceSchema:
    """Test cases for structure compliance schema validation"""
    
    def test_valid_structure_compliance(self):
        """Test validation of valid structure compliance data"""
        valid_data = {
            "structure": [
                {
                    "volume_title": "Volume I - Technical Capability",
                    "total_page_limit": 20,
                    "content": [
                        {
                            "section_name": "Technical Approach",
                            "page_limit": 8
                        },
                        {
                            "section_name": "Management Plan",
                            "page_limit": 6
                        },
                        {
                            "section_name": "Past Performance",
                            "page_limit": 6
                        }
                    ]
                }
            ]
        }
        
        schema = StructureComplianceSchema(**valid_data)
        assert len(schema.structure) == 1
        assert schema.structure[0].volume_title == "Volume I - Technical Capability"
        assert schema.structure[0].total_page_limit == 20
        assert len(schema.structure[0].content) == 3
    
    def test_page_limit_validation(self):
        """Test that section page limits don't exceed volume limit"""
        invalid_data = {
            "structure": [
                {
                    "volume_title": "Volume I",
                    "total_page_limit": 10,
                    "content": [
                        {
                            "section_name": "Section 1",
                            "page_limit": 8
                        },
                        {
                            "section_name": "Section 2",
                            "page_limit": 5  # Total: 13 > 10
                        }
                    ]
                }
            ]
        }
        
        with pytest.raises(ValueError, match="exceed volume limit"):
            StructureComplianceSchema(**invalid_data)
    
    def test_zero_page_limit_validation(self):
        """Test that zero page limits are not allowed"""
        invalid_data = {
            "structure": [
                {
                    "volume_title": "Volume I",
                    "content": [
                        {
                            "section_name": "Section 1",
                            "page_limit": 0  # Invalid
                        }
                    ]
                }
            ]
        }
        
        with pytest.raises(ValueError):
            StructureComplianceSchema(**invalid_data)
    
    def test_empty_section_name_validation(self):
        """Test that empty section names are not allowed"""
        invalid_data = {
            "structure": [
                {
                    "volume_title": "Volume I",
                    "content": [
                        {
                            "section_name": "",  # Invalid
                            "page_limit": 5
                        }
                    ]
                }
            ]
        }
        
        with pytest.raises(ValueError):
            StructureComplianceSchema(**invalid_data)
    
    def test_duplicate_volume_titles(self):
        """Test that duplicate volume titles are not allowed"""
        invalid_data = {
            "structure": [
                {
                    "volume_title": "Volume I",
                    "content": [
                        {
                            "section_name": "Section 1",
                            "page_limit": 5
                        }
                    ]
                },
                {
                    "volume_title": "Volume I",  # Duplicate
                    "content": [
                        {
                            "section_name": "Section 2",
                            "page_limit": 5
                        }
                    ]
                }
            ]
        }
        
        with pytest.raises(ValueError, match="Duplicate volume titles"):
            StructureComplianceSchema(**invalid_data)


class TestContentComplianceSchema:
    """Test cases for content compliance schema validation"""
    
    def test_valid_content_compliance(self):
        """Test validation of valid content compliance data"""
        valid_data = {
            "content_compliance": [
                {
                    "volume_title": "Volume I",
                    "content": "Technical approach demonstrating understanding of requirements.",
                    "page_limit": 20,
                    "evaluation_criteria": ["Technical understanding"],
                    "mandatory_sections": ["Technical Approach"]
                }
            ]
        }
        
        schema = ContentComplianceResponse(**valid_data)
        assert len(schema.content_compliance) == 1
        assert schema.content_compliance[0].volume_title == VolumeTitle.VOLUME_I
        assert schema.content_compliance[0].page_limit == 20
    
    def test_placeholder_content_validation(self):
        """Test that placeholder content is rejected"""
        invalid_data = {
            "content_compliance": [
                {
                    "volume_title": "Volume I",
                    "content": "Please [insert technical approach here]",  # Contains placeholder
                    "page_limit": 20
                }
            ]
        }
        
        with pytest.raises(ValueError, match="placeholder text"):
            ContentComplianceResponse(**invalid_data)
    
    def test_empty_content_validation(self):
        """Test that empty content is not allowed"""
        invalid_data = {
            "content_compliance": [
                {
                    "volume_title": "Volume I",
                    "content": "",  # Empty content
                    "page_limit": 20
                }
            ]
        }
        
        with pytest.raises(ValueError):
            ContentComplianceResponse(**invalid_data)
    
    def test_invalid_volume_title(self):
        """Test that invalid volume titles are rejected"""
        invalid_data = {
            "content_compliance": [
                {
                    "volume_title": "Volume VI",  # Not in enum
                    "content": "Valid content description",
                    "page_limit": 20
                }
            ]
        }
        
        with pytest.raises(ValueError):
            ContentComplianceResponse(**invalid_data)


class TestComplianceValidator:
    """Test cases for compliance validator utility"""
    
    def test_validate_structure_compliance_success(self):
        """Test successful structure compliance validation"""
        valid_data = ComplianceValidator.get_structure_schema_example()
        result = ComplianceValidator.validate_structure_compliance(valid_data)
        
        assert result["is_valid"] is True
        assert result["validated_data"] is not None
        assert result["errors"] is None
    
    def test_validate_structure_compliance_failure(self):
        """Test failed structure compliance validation"""
        invalid_data = {
            "structure": [
                {
                    "volume_title": "",  # Invalid
                    "content": []  # Invalid
                }
            ]
        }
        result = ComplianceValidator.validate_structure_compliance(invalid_data)
        
        assert result["is_valid"] is False
        assert result["validated_data"] is None
        assert result["errors"] is not None
    
    def test_validate_content_compliance_success(self):
        """Test successful content compliance validation"""
        valid_data = ComplianceValidator.get_content_schema_example()
        result = ComplianceValidator.validate_content_compliance(valid_data)
        
        assert result["is_valid"] is True
        assert result["validated_data"] is not None
        assert result["errors"] is None
    
    def test_validate_content_compliance_failure(self):
        """Test failed content compliance validation"""
        invalid_data = {
            "content_compliance": [
                {
                    "volume_title": "Volume I",
                    "content": "[insert content here]"  # Contains placeholder
                }
            ]
        }
        result = ComplianceValidator.validate_content_compliance(invalid_data)
        
        assert result["is_valid"] is False
        assert result["validated_data"] is None
        assert result["errors"] is not None
    
    def test_schema_examples_are_valid(self):
        """Test that provided schema examples are valid"""
        structure_example = ComplianceValidator.get_structure_schema_example()
        content_example = ComplianceValidator.get_content_schema_example()
        
        # Should not raise exceptions
        StructureComplianceSchema(**structure_example)
        ContentComplianceResponse(**content_example)


if __name__ == "__main__":
    # Run basic tests
    print("Running compliance schema tests...")
    
    # Test structure compliance
    try:
        validator = ComplianceValidator()
        structure_example = validator.get_structure_schema_example()
        structure_result = validator.validate_structure_compliance(structure_example)
        print(f"Structure compliance validation: {structure_result['is_valid']}")
        
        content_example = validator.get_content_schema_example()
        content_result = validator.validate_content_compliance(content_example)
        print(f"Content compliance validation: {content_result['is_valid']}")
        
        print("All tests passed!")
    except Exception as e:
        print(f"Test failed: {e}")
