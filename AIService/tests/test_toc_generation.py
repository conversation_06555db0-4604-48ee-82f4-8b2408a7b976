import asyncio
import json
import os
from datetime import datetime
from services.proposal.structure_compliance import StructureComplianceService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.outline import ProposalOutlineService
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))


class TestTOCGeneration:
    """Generate TOC for each volume separately"""

    def __init__(self):
        self.structure_service = StructureComplianceService()
        self.content_service = ContentComplianceService()
        self.outline_service = ProposalOutlineService()

    def save_results_to_file(self, data, filename, output_dir="test_outputs"):
        """Save results to file"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filepath = os.path.join(output_dir, f"{filename}_{timestamp}.json")

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"Results saved to: {filepath}")
        return filepath

    async def generate_compliance_data(self, opportunity_id, tenant_id, source):
        """Generate compliance data"""
        print("Generating Structure Compliance...")
        structure_result = await self.structure_service.generate_structure_compliance(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source
        )

        print("Generating Content Compliance...")
        content_result = await self.content_service.generate_content_compliance(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            is_rfp=True
        )

        return structure_result, content_result

    def split_compliance_by_volume(self, structure_result, content_result):
        """Split compliance data by volume"""
        volumes = {}

        # Extract structure data
        structure_data = structure_result.get("structured_data", {}) if structure_result.get("is_valid") else {}
        content_data = content_result.get("structured_data", {}) if content_result.get("is_valid") else {}

        # Split by volumes from structure data
        if structure_data and "structure" in structure_data:
            for volume in structure_data["structure"]:
                volume_title = volume.get("volume_title", "")
                volumes[volume_title] = {
                    "structure": volume,
                    "content": None
                }

        # Match content compliance to volumes
        if content_data and "content_compliance" in content_data:
            for compliance in content_data["content_compliance"]:
                volume_title = compliance.get("volume_title", "")
                # Match volume titles
                for vol_key in volumes.keys():
                    if volume_title.lower() in vol_key.lower() or vol_key.lower() in volume_title.lower():
                        volumes[vol_key]["content"] = compliance
                        break

        return volumes

    async def generate_toc_for_volume(self, opportunity_id, tenant_id, source, volume_data):
        """Generate TOC for a single volume"""
        volume_title = volume_data.get("structure", {}).get("volume_title", "Unknown Volume")
        print(f"Generating TOC for {volume_title}...")

        # Prepare volume-specific data
        volume_structure = json.dumps({"structure": [volume_data["structure"]]}, indent=2)
        volume_content = json.dumps({"content_compliance": [volume_data["content"]]}, indent=2) if volume_data["content"] else "{}"

        # Generate TOC for this volume
        toc_result = await self.outline_service.generate_table_of_contents(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            volume_information=volume_structure,
            content_compliance=volume_content,
            is_rfp=True
        )

        return toc_result

    async def run_toc_test(self, opportunity_id, tenant_id, source):
        """Generate TOC for each volume separately"""
        print("=" * 60)
        print("TABLE OF CONTENTS GENERATION BY VOLUME")
        print("=" * 60)
        print(f"Opportunity ID: {opportunity_id}")
        print(f"Tenant ID: {tenant_id}")
        print(f"Source: {source}")
        print("-" * 60)

        try:
            # Step 1: Generate compliance data
            structure_result, content_result = await self.generate_compliance_data(
                opportunity_id, tenant_id, source
            )

            # Save compliance data
            compliance_data = {
                "structure_compliance": structure_result,
                "content_compliance": content_result
            }
            self.save_results_to_file(compliance_data, f"compliance_data_{opportunity_id}")

            # Step 2: Split compliance by volume
            volumes = self.split_compliance_by_volume(structure_result, content_result)
            print(f"Found {len(volumes)} volumes: {list(volumes.keys())}")

            # Step 3: Generate TOC for each volume
            all_toc_results = {}

            for volume_title, volume_data in volumes.items():
                print(f"\nGenerating TOC for: {volume_title}")

                toc_result = await self.generate_toc_for_volume(
                    opportunity_id, tenant_id, source, volume_data
                )

                all_toc_results[volume_title] = toc_result

                # Save individual volume TOC
                volume_filename = volume_title.replace(" ", "_").replace(":", "").replace("-", "_")
                self.save_results_to_file(
                    toc_result,
                    f"toc_{volume_filename}_{opportunity_id}"
                )

                print(f"✓ TOC generated for {volume_title}")

            # Save all TOC results
            final_results = {
                "all_toc_results": all_toc_results,
                "volumes_processed": list(volumes.keys()),
                "compliance_data": compliance_data
            }

            self.save_results_to_file(final_results, f"all_toc_results_{opportunity_id}")

            print("\n" + "=" * 60)
            print("RESULTS SUMMARY")
            print("=" * 60)
            print(f"✓ Volumes processed: {len(volumes)}")
            print(f"✓ TOC files generated: {len(all_toc_results)}")
            print("✓ All results saved to test_outputs/ directory")
            print("=" * 60)

            return final_results

        except Exception as e:
            print(f"✗ Test failed with error: {e}")
            return None


async def main():
    """Main test runner"""
    test_instance = TestTOCGeneration()

    # Real data from the user
    test_data = {
        "opportunity_id": " vSe1unlCj9",
        "tenant_id": "8d9e9729-f7bd-44a0-9cf1-777f532a2db2",
        "source": "custom"
    }

    await test_instance.run_toc_test(
        opportunity_id=test_data["opportunity_id"].strip(),
        tenant_id=test_data["tenant_id"],
        source=test_data["source"]
    )


if __name__ == "__main__":
    asyncio.run(main())
