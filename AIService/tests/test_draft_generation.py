import asyncio
import json
import os
from datetime import datetime
from services.proposal.structure_compliance import StructureComplianceService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.outline import ProposalOutlineService
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parents[1]))


class TestDraftGeneration:
    """Test complete pipeline: compliance → TOC → outline → draft for each volume"""
    
    def __init__(self):
        self.structure_service = StructureComplianceService()
        self.content_service = ContentComplianceService()
        self.outline_service = ProposalOutlineService()
        self.draft_service = self.outline_service
    
    def save_results_to_file(self, data, filename, output_dir="test_outputs"):
        """Save results to file"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filepath = os.path.join(output_dir, f"{filename}_{timestamp}.json")
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"Results saved to: {filepath}")
        return filepath

    async def generate_compliance_data(self, opportunity_id, tenant_id, source):
        """Step 1: Generate compliance data"""
        print("Step 1: Generating Compliance Data...")
        
        structure_result = await self.structure_service.generate_structure_compliance(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source
        )
        
        content_result = await self.content_service.generate_content_compliance(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            is_rfp=True
        )
        
        return structure_result, content_result

    def split_compliance_by_volume(self, structure_result, content_result):
        """Split compliance data by volume"""
        volumes = {}
        
        # Extract structure data
        structure_data = structure_result.get("structured_data", {}) if structure_result.get("is_valid") else {}
        content_data = content_result.get("structured_data", {}) if content_result.get("is_valid") else {}
        
        # Split by volumes from structure data
        if structure_data and "structure" in structure_data:
            for volume in structure_data["structure"]:
                volume_title = volume.get("volume_title", "")
                volumes[volume_title] = {
                    "structure": volume,
                    "content": None
                }
        
        # Match content compliance to volumes
        if content_data and "content_compliance" in content_data:
            for compliance in content_data["content_compliance"]:
                volume_title = compliance.get("volume_title", "")
                # Match volume titles
                for vol_key in volumes.keys():
                    if volume_title.lower() in vol_key.lower() or vol_key.lower() in volume_title.lower():
                        volumes[vol_key]["content"] = compliance
                        break
        
        return volumes

    async def generate_toc_for_volume(self, opportunity_id, tenant_id, source, volume_data):
        """Step 2: Generate TOC for a single volume"""
        volume_title = volume_data.get("structure", {}).get("volume_title", "Unknown Volume")
        print(f"Step 2: Generating TOC for {volume_title}...")
        
        # Prepare volume-specific data
        volume_structure = json.dumps({"structure": [volume_data["structure"]]}, indent=2)
        volume_content = json.dumps({"content_compliance": [volume_data["content"]]}, indent=2) if volume_data["content"] else "{}"
        
        # Generate TOC for this volume
        toc_result = await self.outline_service.generate_table_of_contents(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            volume_information=volume_structure,
            content_compliance=volume_content,
            is_rfp=True
        )
        
        return toc_result

    def extract_toc_from_result(self, toc_result):
        """Extract TOC JSON from the result"""
        if not toc_result or "content" not in toc_result:
            return None
        
        content = toc_result["content"]
        
        try:
            # Look for JSON in the content
            import re
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
        except:
            pass
        
        return None

    async def generate_outline_for_volume(self, opportunity_id, tenant_id, source, volume_data, toc_data):
        """Step 3: Generate outline for a single volume using TOC"""
        volume_title = volume_data.get("structure", {}).get("volume_title", "Unknown Volume")
        print(f"Step 3: Generating Outline for {volume_title}...")
        
        # Extract table of contents list from toc_data
        if toc_data and "table_of_contents" in toc_data:
            toc_list = toc_data["table_of_contents"]
        else:
            print("⚠ No valid TOC data found, using empty list")
            toc_list = []
        
        # Generate outline for this volume
        outline_result = await self.outline_service.generate_outline(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            table_of_contents=toc_list,
            is_rfp=True
        )
        
        return outline_result

    def extract_outline_from_result(self, outline_result):
        """Extract outline data from the result"""
        if not outline_result or "outlines" not in outline_result:
            return None

        return outline_result["outlines"]

    def extract_draft_from_result(self, draft_result):
        """Extract draft data from the result"""
        if not draft_result or "draft" not in draft_result:
            return None

        return draft_result["draft"]

    async def generate_draft_for_volume(self, opportunity_id, tenant_id, source, volume_data, toc_data):
        """Step 4: Generate draft for a single volume using TOC"""
        volume_title = volume_data.get("structure", {}).get("volume_title", "Unknown Volume")
        print(f"Step 4: Generating Draft for {volume_title}...")

        if not toc_data or "table_of_contents" not in toc_data:
            print("⚠ No valid TOC data found, cannot generate draft")
            return None

        # Extract table of contents list from toc_data
        toc_list = toc_data["table_of_contents"]

        # Generate draft for this volume using the correct method signature
        draft_result = await self.draft_service.generate_draft(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            client_short_name="TestClient",  # Default for testing
            tenant_metadata="{}",  # Empty metadata for testing
            table_of_contents=toc_list
        )

        return draft_result

    async def run_complete_draft_test(self, opportunity_id, tenant_id, source):
        """Run complete draft generation pipeline"""
        print("=" * 80)
        print("COMPLETE DRAFT GENERATION PIPELINE TEST")
        print("=" * 80)
        print(f"Opportunity ID: {opportunity_id}")
        print(f"Tenant ID: {tenant_id}")
        print(f"Source: {source}")
        print("-" * 80)
        
        try:
            # Step 1: Generate compliance data
            structure_result, content_result = await self.generate_compliance_data(
                opportunity_id, tenant_id, source
            )
            
            # Save compliance data
            compliance_data = {
                "structure_compliance": structure_result,
                "content_compliance": content_result
            }
            self.save_results_to_file(compliance_data, f"draft_pipeline_compliance_{opportunity_id}")
            
            print(f"✓ Structure compliance: {'VALID' if structure_result.get('is_valid') else 'INVALID'}")
            print(f"✓ Content compliance: {'VALID' if content_result.get('is_valid') else 'INVALID'}")
            
            # Split compliance by volume
            volumes = self.split_compliance_by_volume(structure_result, content_result)
            print(f"✓ Found {len(volumes)} volumes: {list(volumes.keys())}")
            
            # Process each volume through the complete pipeline
            all_results = {}
            
            for volume_title, volume_data in volumes.items():
                print(f"\n{'='*60}")
                print(f"PROCESSING VOLUME: {volume_title}")
                print(f"{'='*60}")
                
                # Step 2: Generate TOC for this volume
                toc_result = await self.generate_toc_for_volume(
                    opportunity_id, tenant_id, source, volume_data
                )
                
                # Extract TOC JSON
                toc_data = self.extract_toc_from_result(toc_result)
                
                if toc_data:
                    print(f"✓ TOC generated successfully with {len(toc_data.get('table_of_contents', []))} sections")
                else:
                    print("⚠ TOC generation failed or no valid JSON found")
                
                # Step 3: Generate outline using TOC
                outline_result = await self.generate_outline_for_volume(
                    opportunity_id, tenant_id, source, volume_data, toc_data
                )
                
                # Extract outline data
                outline_data = self.extract_outline_from_result(outline_result)
                
                if outline_data:
                    print(f"✓ Outline generated successfully with {len(outline_data)} sections")
                else:
                    print("⚠ Outline generation failed")
                
                # Step 4: Generate draft using TOC
                draft_result = await self.generate_draft_for_volume(
                    opportunity_id, tenant_id, source, volume_data, toc_data
                )
                
                # Extract draft data
                draft_data = self.extract_draft_from_result(draft_result)

                if draft_data:
                    print(f"✓ Draft generated successfully with {len(draft_data)} sections")
                else:
                    print("⚠ Draft generation failed")
                
                # Store results for this volume
                volume_filename = volume_title.replace(" ", "_").replace(":", "").replace("-", "_")
                
                volume_results = {
                    "volume_title": volume_title,
                    "compliance_data": volume_data,
                    "toc_result": toc_result,
                    "toc_extracted": toc_data,
                    "outline_result": outline_result,
                    "outline_extracted": outline_data,
                    "draft_result": draft_result,
                    "draft_extracted": draft_data
                }
                
                all_results[volume_title] = volume_results
                
                # Save individual volume results
                self.save_results_to_file(
                    volume_results, 
                    f"draft_pipeline_{volume_filename}_{opportunity_id}"
                )
            
            # Save complete pipeline results
            final_results = {
                "pipeline_summary": {
                    "opportunity_id": opportunity_id,
                    "volumes_processed": list(volumes.keys()),
                    "total_volumes": len(volumes),
                    "compliance_valid": {
                        "structure": structure_result.get('is_valid', False),
                        "content": content_result.get('is_valid', False)
                    }
                },
                "volume_results": all_results,
                "compliance_data": compliance_data
            }
            
            self.save_results_to_file(final_results, f"draft_pipeline_complete_{opportunity_id}")
            
            # Print final summary
            print(f"\n{'='*80}")
            print("DRAFT PIPELINE RESULTS SUMMARY")
            print(f"{'='*80}")
            print(f"✓ Total volumes processed: {len(volumes)}")
            print(f"✓ Compliance generation: {'SUCCESS' if structure_result or content_result else 'FAILED'}")
            print(f"✓ TOC generation: {'SUCCESS' if any(v.get('toc_result') for v in all_results.values()) else 'FAILED'}")
            print(f"✓ Outline generation: {'SUCCESS' if any(v.get('outline_result') for v in all_results.values()) else 'FAILED'}")
            print(f"✓ Draft generation: {'SUCCESS' if any(v.get('draft_result') for v in all_results.values()) else 'FAILED'}")
            
            for volume_title, results in all_results.items():
                toc_success = "✓" if results.get('toc_extracted') else "⚠"
                outline_success = "✓" if results.get('outline_extracted') else "⚠"
                draft_success = "✓" if results.get('draft_result') else "⚠"
                print(f"  {volume_title}: TOC {toc_success} | Outline {outline_success} | Draft {draft_success}")
            
            print(f"{'='*80}")
            print("All draft pipeline results saved to test_outputs/ directory")
            print(f"{'='*80}")
            
            return final_results
            
        except Exception as e:
            print(f"✗ Draft pipeline failed with error: {e}")
            import traceback
            traceback.print_exc()
            return None


async def main():
    """Main test runner"""
    test_instance = TestDraftGeneration()
    
    # Real data from the user
    test_data = {
        "opportunity_id": "vSe1unlCj9",
        "tenant_id": "8d9e9729-f7bd-44a0-9cf1-777f532a2db2",
        "source": "custom"
    }
    
    await test_instance.run_complete_draft_test(
        opportunity_id=test_data["opportunity_id"],
        tenant_id=test_data["tenant_id"],
        source=test_data["source"]
    )


if __name__ == "__main__":
    asyncio.run(main())
