from datetime import datetime, date
from sqlalchemy import Column, String, DateTime, Text, Boolean, Index, Integer
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from database import Base

def _json_serialize(value):
    if isinstance(value, (datetime, date)):
        return value.isoformat()
    if isinstance(value, bytes):
        return value.decode('utf-8', errors='replace')
    if isinstance(value, (list, tuple)):
        return [_json_serialize(v) for v in value]
    if isinstance(value, dict):
        return {k: _json_serialize(v) for k, v in value.items()}
    return value

class BaseModelMixin:
    def as_dict(self):
        result = {}
        table = getattr(self, "__table__", None)
        if table is not None:
            for column in table.columns:
                value = getattr(self, column.name)
                result[column.name] = _json_serialize(value)
        return result
class ChatThread(Base, BaseModelMixin):
    """
    Chat thread model for managing conversation threads
    """
    __tablename__ = "chat_threads"
    __table_args__ = (
        Index("idx_chat_threads_tenant_opp", "tenant_id", "opportunity_id"),
        Index("idx_chat_threads_created_date", "created_date"),
        Index("idx_chat_threads_last_activity", "last_activity_date"),
        {"schema": "opportunity"},
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(String(255), nullable=False)
    title = Column(String(500), nullable=False)
    opportunity_id = Column(String(255), nullable=True)
    source = Column(String(100), nullable=True)
    created_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    last_activity_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    is_archived = Column(Boolean, default=False, nullable=False)
    message_count = Column(Integer, default=0, nullable=False)
    summary = Column(Text, nullable=True)
    thread_metadata = Column(JSONB, nullable=True)


class ChatMessage(Base, BaseModelMixin):
    """
    Individual chat message model
    """
    __tablename__ = "chat_messages"
    __table_args__ = (
        Index("idx_chat_messages_thread_id", "thread_id"),
        Index("idx_chat_messages_created_date", "created_date"),
        Index("idx_chat_messages_role", "role"),
        {"schema": "opportunity"},
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    thread_id = Column(UUID(as_uuid=True), nullable=False)
    role = Column(String(50), nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    created_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    token_count = Column(Integer, nullable=True)
    context_chunks = Column(JSONB, nullable=True)
    message_metadata = Column(JSONB, nullable=True)






