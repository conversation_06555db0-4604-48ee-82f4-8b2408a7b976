import os
from typing import Optional,List

from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    # Will move these to an env file
    # Kontratar Database (kontratar_main and kontratar_global schemas)
    kontratar_db_host: str = "govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com"
    kontratar_db_port: int = 5432
    kontratar_db_name: str = "postgres"
    kontratar_db_user: str = "alpha"
    kontratar_db_password: str = "5t3r2i66123"
    
    # Customer Database (opportunity schema)
    customer_db_host: str = "govcon-1.cwjbmcfdk9qo.us-east-1.rds.amazonaws.com"
    customer_db_port: int = 5432
    customer_db_name: str = "postgres"
    customer_db_user: str = "alpha"
    customer_db_password: str = "5t3r2i66123"

    ai_embedding_server_name: str = ""
    ai_embedding_server_port: str = ""
    ai_embedding_server_protocol: str = ""
    ai_image_server_name: str = ""
    ai_image_server_port: str = ""
    ai_image_server_protocol: str = ""
    ai_llm_server_name: str = ""
    ai_llm_server_port: str = ""
    ai_llm_server_protocol: str = ""
    api_gateway_port: str = ""
    app_public_hostname: str = ""
    auth_port: str = ""
    auth_secret: str = ""
    auth_trust_host: str = ""
    auth_url: str = ""
    award_repository_port: str = ""
    base_port: str = ""
    chromadb_port_1: str = ""
    chromadb_port_2: str = ""
    chromadb_port_3: str = ""
    chromadb_port_4: str = ""
    chromadb_port_5: str = ""
    chromadb_protocol: str = ""
    chromadb_server_name: str = ""
    client_id: str = ""
    controller_host: str = ""
    controller_port: str = ""
    database_url: str = ""
    db_kontratar_host: str = ""
    db_kontratar_name: str = ""
    db_kontratar_password: str = ""
    db_kontratar_port: str = ""
    db_kontratar_user: str = ""
    discord_webhook_url: str = ""
    domain: str = ""
    elk_pwd: str = ""
    elk_url: str = ""
    elk_username: str = ""
    eureka_hostname: str = ""
    eureka_port: str = ""
    elasticsearchserverhttpprotocol: str = ""
    elasticsearchservername: str = ""
    elasticsearchserverpassword: str = ""
    elasticsearchserverport: str = ""
    elasticsearchserverusername: str = ""
    frontend_port: str = ""
    google_auth_client_id: str = ""
    google_auth_client_secret: str = ""
    informational_service_port: str = ""
    next_public_api_url: str = ""
    next_public_app_mode: str = ""
    next_public_auth_url: str = ""
    next_public_github_token: str = ""
    next_public_is_url: str = ""
    
    
    # Application Configuration
    app_host: str = "0.0.0.0"
    app_port: int = 8000
    debug: bool = True

    # LangSmith Configuration
    langchain_api_key: str = os.getenv("LANGCHAIN_API_KEY") or ""
    langchain_project: str = os.getenv("LANGCHAIN_PROJECT") or ""
    langchain_tracing_v2: str = os.getenv("LANGCHAIN_TRACING_V2") or ""
    
    # Scheduler Configuration
    scheduler_interval_seconds: int = 60
    
    # ChromaDB Configuration
    chromadb_protocol: str = Field("http", env="CHROMADB_PROTOCOL")
    chromadb_server_name: str = Field("localhost", env="CHROMADB_SERVER_NAME")
    chromadb_port_1: int = Field(0, env="CHROMADB_PORT_1")
    chromadb_port_2: int = Field(0, env="CHROMADB_PORT_2")
    chromadb_port_3: int = Field(0, env="CHROMADB_PORT_3")
    chromadb_port_4: int = Field(0, env="CHROMADB_PORT_4")
    chromadb_port_5: int = Field(0, env="CHROMADB_PORT_5")

    @property
    def chromadb_ports(self) -> list[int]:
        return [
            port for port in [
            self.chromadb_port_1,
            self.chromadb_port_2,
            self.chromadb_port_3,
            self.chromadb_port_4,
            self.chromadb_port_5,
        ] if port
    ]
    
    
    @property
    def chromadb_instance_urls(self) -> list[str]:
        return [
            f"{self.chromadb_protocol}://{self.chromadb_server_name}:{port}"
            for port in self.chromadb_ports
        ]
        
    
    
    class Config:
        extra = "ignore"
        env_file = ".env"
        case_sensitive = False
        extra = "allow"


settings = Settings()
print("Loaded ChromaDB ports:", settings.chromadb_ports)
print("Loaded ChromaDB instance URLs:", settings.chromadb_instance_urls)
print("loaded langchain_api_key:", settings.langchain_api_key)


def get_kontratar_db_url() -> str:
    """Get the database URL for kontratar database"""
    return f"postgresql+asyncpg://{settings.kontratar_db_user}:{settings.kontratar_db_password}@{settings.kontratar_db_host}:{settings.kontratar_db_port}/{settings.kontratar_db_name}"


def get_customer_db_url() -> str:
    """Get the database URL for customer database"""
    return f"postgresql+asyncpg://{settings.customer_db_user}:{settings.customer_db_password}@{settings.customer_db_host}:{settings.customer_db_port}/{settings.customer_db_name}" 