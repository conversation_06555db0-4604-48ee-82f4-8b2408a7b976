import asyncio
from controllers.customer.tenant_controller import Tenant<PERSON>ontroller
from services.proposal.utilities import ProposalUtilities
from services.proposal.outline import ProposalOutlineService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.structure_compliance import StructureComplianceService
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from database import get_customer_db
import json

async def main():

    # opportunity_id = "iRiYNgd8RC"
    # opportunity_id = "GCP603BfMN"
    opportunity_id = "vSe1unlCj9"
    
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    client = "adeptengineeringsolutions"
    source = "custom"

    outline_service = ProposalOutlineService()
    content_compliance_service = ContentComplianceService()
    structure_compliance_service = StructureComplianceService()
    custom_controller = CustomOpportunitiesController
    tenant_controller = TenantController

    # Initialize tenant_metadata
    tenant_metadata = ""

    async for db in get_customer_db():
        # Get tenant metadata
        tenant = await tenant_controller.get_by_tenant_id(db, tenant_id)
        tenant_metadata = f"{tenant}"
        break

    # Generate content compliance and structure compliance
    print("Generating content compliance...")
    content_compliance_result = await content_compliance_service.generate_content_compliance(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        is_rfp=True
    )
    content_compliance = content_compliance_result.get("content", "")

    print("Generating structure compliance...")
    structure_compliance_result = await structure_compliance_service.generate_structure_compliance(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source
    )
    structure_compliance = structure_compliance_result.get("content", "")

    # Generate table of contents dynamically
    print("Generating table of contents...")
    toc_result = await outline_service.generate_table_of_contents(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        volume_information=structure_compliance,
        content_compliance=content_compliance,
        is_rfp=True  # Assuming RFP for this example
    )

    # Extract table of contents from the result
    toc_content = toc_result.get("content", "")
    if toc_content:
        try:
            table_of_contents_data = ProposalUtilities.extract_json_from_brackets(toc_content)
            table_of_contents = table_of_contents_data.get("table_of_contents", []) if table_of_contents_data else []
        except Exception as e:
            print(f"Error parsing table of contents: {e}")
            table_of_contents = []
    else:
        table_of_contents = []

    print(f"Generating proposal draft with {len(table_of_contents)} sections...")
    proposal_draft = await outline_service.generate_draft(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        table_of_contents=table_of_contents,
        tenant_metadata=tenant_metadata,
        client_short_name=client
    )

    print("Saving proposal draft to file...")
    ProposalUtilities.save_json_to_file(proposal_draft, "proposal-draft-3.json")

    # Save the generated table of contents to file for reference
    print("Saving generated table of contents to file...")
    toc_data = {"table_of_contents": table_of_contents}
    ProposalUtilities.save_json_to_file(toc_data, "table-of-contents-generated.json")

    # Update the database with the generated table of contents and draft
    print("Updating database with generated content...")
    async for db in get_customer_db():
        await custom_controller.update_by_opportunity_id(db, opportunity_id, {
            "toc_text": json.dumps(table_of_contents),
            "draft": json.dumps(proposal_draft.get("draft", []))
        })
        break

    print("✅ Pipeline completed successfully!")
    print(f"   - Generated table of contents with {len(table_of_contents)} sections")
    print(f"   - Generated proposal draft with {len(proposal_draft.get('draft', []))} sections")
    print("   - Updated database with generated content")
    print("   - Saved files: proposal-draft-3.json, table-of-contents-generated.json")
    
    


if __name__ == "__main__":
    asyncio.run(main())