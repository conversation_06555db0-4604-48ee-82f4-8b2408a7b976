from typing import Any, Dict, Optional
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.kontratar_models import OppsTable

class OppsController:
    """Controller for OppsTable operations"""

    @staticmethod
    async def update_by_opportunity_id(
        db: AsyncSession,
        opportunity_id: str,
        update_fields: Dict[str, Any]
    ) -> Optional[OppsTable]:
        """
        Update fields of an OppsTable record by opportunity_id.
        Only allows updating valid fields.
        Returns the updated record or None if not found.
        """
        valid_columns = {col.name for col in OppsTable.__table__.columns}
        safe_fields = {k: v for k, v in update_fields.items() if k in valid_columns and k != "id"}

        if not safe_fields:
            logger.warning("No valid fields to update for OppsTable.")
            return None

        try:
            query = select(OppsTable).where(OppsTable.notice_id == opportunity_id)
            result = await db.execute(query)
            record = result.scalar_one_or_none()
            if not record:
                logger.warning(f"No OppsTable record found for opportunity_id={opportunity_id}")
                return None

            for field, value in safe_fields.items():
                setattr(record, field, value)

            await db.commit()
            await db.refresh(record)
            logger.info(f"Updated OppsTable record with opportunity_id={opportunity_id}")
            return record
        except Exception as e:
            logger.error(f"Error updating OppsTable record: {e}")
            await db.rollback()
            return None
        
    @staticmethod
    async def get_all_proposal_outlines(
        db: AsyncSession,
        opportunity_id: str
    ) -> list:
        """
        Get all proposal_outline values from OppsTable, ordered by id.
        """
        try:
            query = select(
                OppsTable.proposal_outline_1,
                OppsTable.proposal_outline_2,
                OppsTable.proposal_outline_3,
                OppsTable.proposal_outline_4,
                OppsTable.proposal_outline_5,
            ).where(OppsTable.notice_id == opportunity_id)
            result = await db.execute(query)
            rows = result.scalars().all()
            return [row if row is not None else None for row in rows]
        except Exception as e:
            logger.error(f"Error fetching all proposal_outline from OppsTable: {e}")
            return []
        
    @staticmethod
    async def get_by_opportunity_id(
        db: AsyncSession,
        opportunity_id: str
    ) -> Optional[Any]:
        """
        Get a OppsTable record by opportunity_id, returning only selected fields.
        """
        try:
            query = select(
                OppsTable.title,
                OppsTable.description,
                OppsTable.posted_date,
                OppsTable.expiration_date,
                OppsTable.naics_code,
                OppsTable.opportunity_type,
                OppsTable.classification_code,
                OppsTable.point_of_contact_first_name,
                OppsTable.point_of_contact_last_name,
                OppsTable.point_of_contact_email,
                OppsTable.point_of_contact_phone,
                OppsTable.place_of_performance_city,
                OppsTable.place_of_performance_state,
                OppsTable.place_of_performance_zip,
                OppsTable.place_of_performance_country,
                OppsTable.draft,
                OppsTable.toc_text,
                OppsTable.toc_text_2,
                OppsTable.toc_text_3,
                OppsTable.toc_text_4,
                OppsTable.toc_text_5,
                OppsTable.format_compliance,
            ).where(OppsTable.notice_id == opportunity_id)
            result = await db.execute(query)
            return result.mappings().one_or_none()
        except Exception as e:
            logger.error(f"Error fetching OppsTable record by opportunity_id={opportunity_id}: {e}")
            return None