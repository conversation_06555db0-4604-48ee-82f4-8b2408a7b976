from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import SimulationQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from models.kontratar_models import OppsTable
from models.customer_models import CustomOppsTable, CustomOppsQueue as CustomerCustomOppsQueue
from models.kontratar_models import SamOpportunityQueue
import json


class SimulationQueueController:
    """Controller for simulation queue operations"""
    
    @staticmethod
    async def get_new_items(db: AsyncSession, limit: int = 10) -> List[SimulationQueue]:
        """
        Get new simulation queue items with status NOT STARTED,
        skipping those whose opportunity has no requirements generated.
        """
        try:
            logger.info("Fetching new simulation queue items with status 'NOT STARTED'")
            query = select(SimulationQueue).where(
                SimulationQueue.status == "NOT STARTED"
            ).order_by(SimulationQueue.created_at.asc()).limit(limit * 2)
            
            result = await db.execute(query)
            items = result.scalars().all()
            logger.info(f"Fetched {len(items)} candidate items from the queue")
            filtered_items = []
    
            for item in items:
                try:
                    job_instruction = json.loads(item.job_instruction)
                    opp_id = job_instruction.get("oppId")
                    source = job_instruction.get("source", "sam")
                    tenant_id = job_instruction.get("tenantId", "SYSTEM")
                    logger.debug(f"Processing queue item {item.id}: opp_id={opp_id}, source={source}, tenant_id={tenant_id}")
                except Exception as e:
                    logger.warning(f"Invalid job_instruction for simulation queue item {item.id}: {e}")
                    continue
    
                has_requirements = False
    
                if source.lower() == "sam":
                    opp_query = select(OppsTable.requirement_text).where(OppsTable.notice_id == opp_id)
                    opp_result = await db.execute(opp_query)
                    row = opp_result.first()
                    if row and row[0]:
                        has_requirements = True
                        logger.debug(f"Opportunity {opp_id} (SAM) has requirements.")
                    else:
                        logger.info(f"Opportunity {opp_id} (SAM) has NO requirements.")
                elif source.lower() == "custom":
                    opp_query = select(CustomOppsTable.requirement_text).where(CustomOppsTable.opportunity_id == opp_id)
                    opp_result = await db.execute(opp_query)
                    row = opp_result.first()
                    if row and row[0]:
                        has_requirements = True
                        logger.debug(f"Opportunity {opp_id} (CUSTOM) has requirements.")
                    else:
                        logger.info(f"Opportunity {opp_id} (CUSTOM) has NO requirements.")
    
                if has_requirements:
                    logger.info(f"Queue item {item.id} added for processing.")
                    filtered_items.append(item)
                else:
                    # Call the helper to update the source queue
                    await SimulationQueueController.ensure_source_queue_entry_for_empty_requirements(
                        db, opp_id, source, tenant_id
                    )
                    logger.info(f"Skipping simulation queue item {item.id} due to missing requirements for {source.upper()} opportunity {opp_id}")
    
                if len(filtered_items) >= limit:
                    logger.info(f"Reached processing limit of {limit} items.")
                    break
    
            logger.info(f"Returning {len(filtered_items)} items for processing.")
            return filtered_items
        except Exception as e:
            logger.error(f"Error getting new simulation queue items: {e}")
            return
        
    @staticmethod
    async def ensure_source_queue_entry_for_empty_requirements(
        db: AsyncSession,
        opps_id: str,
        source: str,
        tenant_id: str
    ) -> None:
        """
        If requirements are missing for the given opps_id:
        - For 'sam': ensure SamOpportunityQueue exists, set status to 'NEW'
        - For 'custom': ensure CustomOppsQueue exists, set status to 'COMPLETED'
        Only acts on the source queue, does nothing in ProposalOutlineQueue.
        """
        try:
            if source.lower() == "sam":
                query = select(SamOpportunityQueue).where(SamOpportunityQueue.opps_id == opps_id)
                result = await db.execute(query)
                entry = result.scalars().first()
                if entry:
                    entry.status = "NEW"
                    entry.last_updated_date = datetime.utcnow()
                    entry.created_date = datetime.utcnow()
                    db.add(entry)
                    logger.info(f"Updated SamOpportunityQueue entry for opps_id={opps_id} to status NEW")
                else:
                    entry = SamOpportunityQueue(
                        opps_id=opps_id,
                        status="NEW",
                        created_date=datetime.utcnow(),
                        updated_date=datetime.utcnow()
                    )
                    db.add(entry)
                    logger.info(f"Created SamOpportunityQueue entry for opps_id={opps_id} with status NEW")
            elif source.lower() == "custom":
                query = select(CustomerCustomOppsQueue).where(CustomerCustomOppsQueue.opps_id == opps_id)
                result = await db.execute(query)
                entry = result.scalars().first()
                if entry:
                    entry.status = "COMPLETED"
                    entry.last_updated_date = datetime.utcnow()
                    entry.created_date = datetime.utcnow()
                    db.add(entry)
                    logger.info(f"Updated CustomOppsQueue entry for opps_id={opps_id} to status COMPLETED")
                else:
                    entry = CustomerCustomOppsQueue(
                        opps_id=opps_id,
                        opps_source="CUSTOM",
                        tenant_id=tenant_id,
                        status="COMPLETED",
                        created_date=datetime.utcnow(),
                        update_date=datetime.utcnow()
                    )
                    db.add(entry)
                    logger.info(f"Created CustomOppsQueue entry for opps_id={opps_id} with status COMPLETED")
            else:
                logger.warning(f"Unknown source '{source}' for opps_id={opps_id}")
                return

            await db.commit()
            if entry:
                await db.refresh(entry)
        except Exception as e:
            logger.error(f"Error ensuring source queue entry for opps_id={opps_id}, source={source}: {e}")
            await db.rollback()
 
    @staticmethod
    async def update_status(db: AsyncSession, queue_id: int, status: str) -> bool:
        """Update simulation queue status"""
        try:
            query = update(SimulationQueue).where(
                SimulationQueue.id == queue_id
            ).values(status=status)
            
            result = await db.execute(query)
            await db.commit()
            
            logger.info(f"Updated simulation queue status for id {queue_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating simulation queue status: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def get_by_id(db: AsyncSession, queue_id: int) -> Optional[SimulationQueue]:
        """Get simulation queue item by ID"""
        try:
            query = select(SimulationQueue).where(SimulationQueue.id == queue_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting simulation queue item {queue_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_job_id(db: AsyncSession, job_id: str) -> Optional[SimulationQueue]:
        """Get simulation queue item by job ID"""
        try:
            query = select(SimulationQueue).where(SimulationQueue.job_id == job_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting simulation queue item by job_id {job_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_job_submitted_by(db: AsyncSession, job_submitted_by: int, limit: int = 100) -> List[SimulationQueue]:
        """Get simulation queue items by job submitted by user ID"""
        try:
            query = select(SimulationQueue).where(
                SimulationQueue.job_submitted_by == job_submitted_by
            ).order_by(SimulationQueue.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting simulation queue items for user {job_submitted_by}: {e}")
            return []
    
    @staticmethod
    async def get_by_status(db: AsyncSession, status: str, limit: int = 100) -> List[SimulationQueue]:
        """Get simulation queue items by status"""
        try:
            query = select(SimulationQueue).where(
                SimulationQueue.status == status
            ).order_by(SimulationQueue.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting simulation queue items with status {status}: {e}")
            return []
    
    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 100) -> List[SimulationQueue]:
        """Get all simulation queue items"""
        try:
            query = select(SimulationQueue).order_by(SimulationQueue.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all simulation queue items: {e}")
            return []
    
    @staticmethod
    async def add(
        db: AsyncSession,
        job_instruction: str,
        job_submitted_by: int,
        job_id: str,
        status: str = "NEW"
    ) -> Optional[SimulationQueue]:
        """Add new simulation queue item"""
        try:
            new_item = SimulationQueue(
                status=status,
                job_instruction=job_instruction,
                job_submitted_by=job_submitted_by,
                job_id=job_id,
                created_at=datetime.now(),
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created new simulation queue item {new_item.id}")
            return new_item
        except Exception as e:
            logger.error(f"Error creating simulation queue item: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def update(
        db: AsyncSession,
        queue_id: int,
        status: str = None,
        job_instruction: str = None
    ) -> bool:
        """Update simulation queue item"""
        try:
            item = await SimulationQueueController.get_by_id(db, queue_id)
            if not item:
                return False
            
            if status:
                item.status = status
            if job_instruction:
                item.job_instruction = job_instruction
            
            await db.commit()
            logger.info(f"Updated simulation queue item {queue_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating simulation queue item: {e}")
            await db.rollback()
            return False 