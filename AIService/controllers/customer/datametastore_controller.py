from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import DataMetastore
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession


class DataMetastoreController:
    """Controller for datametastore operations"""
    
    @staticmethod
    async def get_by_id(db: AsyncSession, record_id: int) -> Optional[DataMetastore]:
        """Get datametastore record by ID"""
        try:
            query = select(DataMetastore).where(DataMetastore.id == record_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting datametastore record {record_id}: {e}")
            return None
        
    @staticmethod
    async def get_partner_doc_by_date(db: AsyncSession, last_processed_date: Optional[datetime] = None, limit: int = 10) -> Optional[DataMetastore]:
        """Get datametastore record by record identifier with date filtering and limit"""
        try:
            # Add more detailed logging
            logger.info("Attempting to retrieve PARTNER documents")
            
            # Base query conditions
            query_conditions = [
                DataMetastore.record_type == "PARTNER",
                DataMetastore.original_document_content_type.in_([
                    'application/pdf', 
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                ])
            ]
            
            # Add date filtering if last_processed_date is provided
            if last_processed_date:
                query_conditions.append(DataMetastore.created_date > last_processed_date)
            
            # Construct the query with more explicit logging
            query = select(DataMetastore).where(
                *query_conditions
            ).order_by(DataMetastore.created_date.asc()).limit(limit)
            
            # Log the exact query details
            logger.info(f"Query details: record_type='PARTNER', content_types={['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']}, last_processed_date={last_processed_date}, limit={limit}")
            
            # Execute the query
            result = await db.execute(query)
            
            # Convert to list and log results
            records = list(result.scalars().all())
            
            logger.info(f"Found {len(records)} matching records")
            
            # Log details of each record if any exist
            for record in records:
                logger.info(f"Record details: ID={record.id}, Filename={record.original_document_file_name}, Content Type={record.original_document_content_type}")
            
            return records
        except Exception as e:
            logger.error(f"Error getting datametastore record by identifier PARTNER: {e}")
            # Include full traceback for more detailed error information
            import traceback
            logger.error(traceback.format_exc())
            return None
    
    @staticmethod
    async def get_by_tenant_id(db: AsyncSession, tenant_id: str, limit: int = 100) -> List[DataMetastore]:
        """Get datametastore records by tenant ID"""
        try:
            query = select(DataMetastore).where(
                DataMetastore.tenant_id == tenant_id
            ).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting datametastore records for tenant {tenant_id}: {e}")
            return []
    
    @staticmethod
    async def get_by_record_identifier(db: AsyncSession, record_identifier: str) -> Optional[DataMetastore]:
        """Get datametastore record by record identifier"""
        try:
            query = select(DataMetastore).where(
                DataMetastore.record_identifier == record_identifier
            )
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting datametastore record by identifier {record_identifier}: {e}")
            return None
    
    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 100) -> List[DataMetastore]:
        """Get all datametastore records"""
        try:
            query = select(DataMetastore).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting all datametastore records: {e}")
            return []
    
    @staticmethod
    async def add(
        db: AsyncSession,
        record_identifier: str,
        record_type: str,
        tenant_id: str,
        original_document_content_type: str,
        original_document_file_name: Optional[str] = None,
        original_document: Optional[bytes] = None,
        raw_text_document: Optional[str] = None,
        owner: Optional[str] = None,
        conservative_criticism: Optional[str] = None
    ) -> Optional[DataMetastore]:
        """Add new datametastore record"""
        try:
            new_record = DataMetastore(
                record_identifier=record_identifier,
                record_type=record_type,
                tenant_id=tenant_id,
                original_document_file_name=original_document_file_name,
                original_document_content_type=original_document_content_type,
                created_date=datetime.utcnow(),
                original_document=original_document,
                raw_text_document=raw_text_document,
                owner=owner,
            )
            
            db.add(new_record)
            await db.commit()
            await db.refresh(new_record)
            
            logger.info(f"Created new datametastore record {new_record.id}")
            return new_record
        except Exception as e:
            logger.error(f"Error creating datametastore record: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def update(
        db: AsyncSession,
        record_id: int,
        original_document_file_name: Optional[str] = None,
        original_document: Optional[bytes] = None,
        raw_text_document: Optional[str] = None,
        owner: Optional[str] = None
    ) -> bool:
        """Update datametastore record"""
        try:
            record = await DataMetastoreController.get_by_id(db, record_id)
            if not record:
                return False
            
            if original_document_file_name:
                setattr(record, "original_document_file_name", original_document_file_name)
            if original_document:
                setattr(record, "original_document", original_document)
            if raw_text_document:
                setattr(record, "raw_text_document", raw_text_document)
            if owner:
                setattr(record, "owner", owner)
            
            await db.commit()
            logger.info(f"Updated datametastore record {record_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating datametastore record: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def get_opportunity_documents(
        db: AsyncSession,
        opportunity_id: str,
    ) -> List[DataMetastore]:
        """Get documents for a specific opportunity by opportunity_id and record_identifier where record_type is 'OPPORTUNITY'"""
        try:
            query = select(DataMetastore).where(
                DataMetastore.record_type == "OPPORTUNITY",
                DataMetastore.record_identifier == opportunity_id
            )
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting opportunity documents for opportunity_id {opportunity_id}: {e}")
            return [] 

    @staticmethod
    async def get_client_documents(
        db: AsyncSession,
        tenant_id: str,
        client_short_name: str,
        profile_id: Optional[str] = None,
    ) -> List[DataMetastore]:
        """Get documents for a specific opportunity by opportunity_id and record_identifier where record_type is 'OPPORTUNITY'"""
        record_identifier = f"{client_short_name}" if profile_id is None else f"{client_short_name}.{profile_id}"
        try:
            query = select(DataMetastore).where(
                DataMetastore.record_type == "PARTNER",
                DataMetastore.record_identifier == record_identifier,
                DataMetastore.tenant_id == tenant_id
            )
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting client documents for tenant_id {tenant_id} and record_identifier {record_identifier}: {e}")
            return [] 

    @staticmethod
    async def get_all_client_documents(
        db: AsyncSession,
        client_short_name: str,
        tenant_id: Optional[str] = None,
        limit: int = 100
    ) -> List[DataMetastore]:
        """
        Get all documents for a client where record_identifier starts with client_short_name
        
        Args:
            db: AsyncSession - Database session
            client_short_name: str - The client short name to search for
            tenant_id: Optional[str] - Optional tenant ID filter
            limit: int - Maximum number of records to return
            
        Returns:
            List[DataMetastore]: List of datametastore records
        """
        try:
            query = select(DataMetastore).where(
                DataMetastore.record_identifier.like(f"{client_short_name}%"),
                DataMetastore.record_type == "PARTNER"
            )
            
            if tenant_id:
                query = query.where(DataMetastore.tenant_id == tenant_id)
                
            query = query.limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting all client documents for client_short_name {client_short_name}: {e}")
            return [] 

    @staticmethod
    async def get_proposal_criticisms(
        db: AsyncSession,
        opportunity_id: str,
        tenant_id: Optional[str] = None
    ) -> List[DataMetastore]:
        """
        Get all proposal criticism records for a specific opportunity
        """
        try:
            query = select(DataMetastore).where(
                DataMetastore.record_type.in_(["PROPOSAL_CRITICISM", "COMPREHENSIVE_CRITICISM"]),
                DataMetastore.record_identifier.like(f"{opportunity_id}%")
            )
            
            if tenant_id:
                query = query.where(DataMetastore.tenant_id == tenant_id)
                
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting proposal criticisms for opportunity_id {opportunity_id}: {e}")
            return []

    @staticmethod
    async def update_conservative_criticism(
        db: AsyncSession,
        record_id: int,
        conservative_criticism: str
    ) -> bool:
        """
        Update only the conservative_criticism field for a record
        """
        try:
            record = await DataMetastoreController.get_by_id(db, record_id)
            if not record:
                logger.warning(f"Record {record_id} not found for criticism update")
                return False
            
            setattr(record, "conservative_criticism", conservative_criticism)
            await db.commit()
            
            logger.info(f"Updated conservative criticism for datametastore record {record_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating conservative criticism for record {record_id}: {e}")
            await db.rollback()
            return False

    @staticmethod
    async def get_reference_documents(
        db: AsyncSession,
        opportunity_id: str
    ) -> List[DataMetastore]:
        """Get documents for a specific opportunity by opportunity_id and record_identifier where record_type is 'OPPORTUNITY'"""
        try:
            volume_ids = [f"{opportunity_id}_references_vol_{n}" for n in range(1, 6)]
            query = select(DataMetastore).where(
                DataMetastore.record_type == "PROPOSAL_REFERENCE",
                DataMetastore.record_identifier.in_(volume_ids)
            )
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting opportunity documents for opportunity_id {opportunity_id}: {e}")
            return [] 