import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from controllers.customer.datametastore_controller import DataMetastoreController
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from models.customer_models import CustomOppsTable, DataMetastore, Users, AESTenant
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from services.exports.render_markdown import MarkdownRenderer
from services.exports.render_html import HtmlRenderer
from services.exports.generat_docx import DocxGenerator
from services.exports.generate_pdf import PDFGenerator


# Constants
TEST_OPPORTUNITY_ID = "test"
TEST_USER_ID = 1
TEST_TENANT_ID = "test"
DEFAULT_COMPLIANCE_SETTINGS = {
    "font_type": "Times-Roman",
    "font_size_body": 12,
    "font_size_header": 14,
    "font_size_footer": 10,
    "margin": 50,
    "line_spacing": 1.5
}


class RfpDraftExportController:
    """Controller for RFP draft export operations"""

    @staticmethod
    async def get_cover_page_from_datametastore(
        db: AsyncSession,
        cover_page_id: int
    ) -> Optional[DataMetastore]:
        """
        Get cover page document from datametastore by ID.
        Returns cover page document or None if not found.
        """
        try:
            if cover_page_id is None:
                logger.info("No cover page ID provided, skipping cover page")
                return None

            cover_page = await DataMetastoreController.get_by_id(db, cover_page_id)
            if not cover_page:
                logger.warning(f"No cover page found for ID={cover_page_id}")
                return None

            logger.info(f"Found cover page: {cover_page.original_document_file_name}")
            return cover_page
        except Exception as e:
            logger.error(f"Error getting cover page from datametastore: {e}")
            return None

    @staticmethod
    async def get_trailing_page_from_datametastore(
        db: AsyncSession,
        trailing_page_id: int
    ) -> Optional[DataMetastore]:
        """
        Get trailing page document from datametastore by ID.
        Returns trailing page document or None if not found.
        """
        try:
            if trailing_page_id is None:
                logger.info("No trailing page ID provided, skipping trailing page")
                return None

            trailing_page = await DataMetastoreController.get_by_id(db, trailing_page_id)
            if not trailing_page:
                logger.warning(f"No trailing page found for ID={trailing_page_id}")
                return None

            logger.info(f"Found trailing page: {trailing_page.original_document_file_name}")
            return trailing_page
        except Exception as e:
            logger.error(f"Error getting trailing page from datametastore: {e}")
            return None

    @staticmethod
    async def get_draft_by_opportunity_id(
        db: AsyncSession,
        opportunity_id: str
    ) -> Optional[List[Dict[str, Any]]]:
        """
        Get draft data for a specific opportunity ID.
        For testing, if opportunity_id is "test", loads from local JSON file.
        Returns parsed draft list or empty list if not found.
        """
        try:
            # Special case for testing
            if opportunity_id.lower() == TEST_OPPORTUNITY_ID:
                # Use relative path instead of hardcoded absolute path
                test_file_path = Path(__file__).parent.parent.parent / "proposal-draft.json"
                try:
                    with open(test_file_path, 'r', encoding='utf-8') as f:
                        draft_list = json.load(f)
                    if not isinstance(draft_list, list):
                        logger.error("Test draft data is not a list")
                        return []
                    logger.info(f"Loaded test draft data from {test_file_path} with {len(draft_list)} sections")
                    return draft_list
                except FileNotFoundError:
                    logger.error(f"Test draft file not found: {test_file_path}")
                    return []
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse test draft JSON: {e}")
                    return []

            # Regular database lookup
            opportunity = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)
            if not opportunity or not opportunity.draft:
                logger.warning(f"No draft found for opportunity_id={opportunity_id}")
                return []

            # Parse the JSON draft data
            try:
                draft_list = json.loads(opportunity.draft)
                if not isinstance(draft_list, list):
                    logger.error(f"Draft data is not a list for opportunity_id={opportunity_id}")
                    return []
                return draft_list
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse draft JSON for opportunity_id={opportunity_id}: {e}")
                return []

        except Exception as e:
            logger.error(f"Error getting draft for opportunity_id={opportunity_id}: {e}")
            return []

    @staticmethod
    async def get_opportunity_details(
        db: AsyncSession,
        opportunity_id: str
    ) -> Optional[CustomOppsTable]:
        """
        Get full opportunity information for the cover page.
        For testing, if opportunity_id is "test", returns mock data.
        Returns opportunity details or None if not found.
        """
        try:
            # Special case for testing
            if opportunity_id.lower() == TEST_OPPORTUNITY_ID:
                # Create a mock opportunity object for testing
                from types import SimpleNamespace
                mock_opportunity = SimpleNamespace()
                mock_opportunity.title = "Test RFP Draft Export"
                mock_opportunity.opportunity_id = TEST_OPPORTUNITY_ID
                mock_opportunity.description = "This is a test opportunity for demonstrating RFP draft export functionality"
                mock_opportunity.agency_name = "Test Agency"
                mock_opportunity.point_of_contact_first_name = "John"
                mock_opportunity.point_of_contact_last_name = "Smith"
                mock_opportunity.point_of_contact_email = "<EMAIL>"
                mock_opportunity.point_of_contact_phone = "(*************"
                mock_opportunity.toc_text = json.dumps([
                    {"title": "Tab A - Proposal Cover/Transmittal Letter", "description": "Includes the proposal cover and transmittal letter.", "number": "1.0", "subsections": []},
                    {"title": "Tab B - Factor 1 - Staffing & Key Personnel Qualifications", "description": "Description of recruitment, hiring, retention, and development of qualified staff.", "number": "2.0", "subsections": [
                        {"number": "2.1", "title": "Resumes of Proposed Key Personnel", "description": "Resumes for proposed Key Personnel (max 2 pages each)."},
                        {"number": "2.2", "title": "Tentative/Contingent Offer Letter", "description": "If not an existing employee, include a signed tentative/contingent offer letter."}
                    ]},
                    {"title": "Tab C - Factor 2 - Management Approach", "description": "Detailed description of approach to manage and support a comprehensive program.", "number": "3.0", "subsections": []},
                    {"title": "Tab D - Factor 3 - Technical Approach", "description": "Demonstrated understanding of scope, complexity, and level of effort involved.", "number": "4.0", "subsections": [
                        {"number": "4.1", "title": "Task 1 – Program Management and Administration", "description": "Details regarding approach to Program Management and Administration."},
                        {"number": "4.2", "title": "Task 2 – Information Management", "description": "Details regarding approach to Information Management."},
                        {"number": "4.3", "title": "Task 3 – Program Compliance", "description": "Details regarding approach to Program Compliance."}
                    ]},
                    {"title": "Tab E - Factor 4 – Demonstrated Corporate Experience", "description": "Up to three examples of federal government experience.", "number": "5.0", "subsections": [
                        {"number": "5.1", "title": "Experience Example 1", "description": "Detailed description of federal government experience."},
                        {"number": "5.2", "title": "Experience Example 2", "description": "Detailed description of federal government experience."},
                        {"number": "5.3", "title": "Experience Example 3", "description": "Detailed description of federal government experience."}
                    ]}
                ])
                logger.info("Using mock opportunity details for test")
                return mock_opportunity

            opportunity = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)
            if not opportunity:
                logger.warning(f"No opportunity found for opportunity_id={opportunity_id}")
                return None
            return opportunity
        except Exception as e:
            logger.error(f"Error getting opportunity details for opportunity_id={opportunity_id}: {e}")
            return None

    @staticmethod
    async def get_user_by_id(
        db: AsyncSession,
        user_id: int
    ) -> Optional[Users]:
        """
        Get user information by user ID.
        For testing with user_id=1, returns mock data.
        Returns user details or None if not found.
        """
        try:
            # Special case for testing
            if user_id == TEST_USER_ID:
                from types import SimpleNamespace
                mock_user = SimpleNamespace()
                mock_user.name = "Test User"
                mock_user.email = "<EMAIL>"
                logger.info("Using mock user details for test")
                return mock_user

            query = select(Users).where(Users.id == user_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting user by id={user_id}: {e}")
            return None

    @staticmethod
    async def get_tenant_by_id(
        db: AsyncSession,
        tenant_id: str
    ) -> Optional[AESTenant]:
        """
        Get tenant information by tenant ID.
        For testing with tenant_id="test", returns mock data.
        Returns tenant details or None if not found.
        """
        try:
            # Special case for testing
            if tenant_id.lower() == TEST_TENANT_ID:
                from types import SimpleNamespace
                mock_tenant = SimpleNamespace()
                mock_tenant.tenant_name = "Test Organization"
                mock_tenant.tenant_id = TEST_TENANT_ID
                logger.info("Using mock tenant details for test")
                return mock_tenant

            query = select(AESTenant).where(AESTenant.tenant_id == tenant_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting tenant by id={tenant_id}: {e}")
            return None

   
    @staticmethod
    async def export_rfp_draft(
        db: AsyncSession,
        opportunity_id: str,
        tenant_id: str,
        user_id: int,
        version: int,
        cover_page_id: Optional[int] = None,
        trailing_page_id: Optional[int] = None,
        volume_number: int = 1
    ) -> tuple[str, str]:
        """
        Main method to export RFP draft to PDF.
        Returns tuple of (file_path, success_message).
        """
        try:
            # Get draft data
            draft_list = await RfpDraftExportController.get_draft_by_opportunity_id(db, opportunity_id)
            if not draft_list:
                raise Exception("No draft data found for the specified opportunity")

            # Get opportunity details
            opportunity_details = await RfpDraftExportController.get_opportunity_details(db, opportunity_id)
            
            # Get user details
            user_details = await RfpDraftExportController.get_user_by_id(db, user_id)
            
            # Get tenant details
            tenant_details = await RfpDraftExportController.get_tenant_by_id(db, tenant_id)
                   # Fixed typo: complaince -> compliance
            compliance = DEFAULT_COMPLIANCE_SETTINGS.copy()

            # Get cover page if provided
            cover_page_elements = None
            if cover_page_id:
                logger.info(f"Processing cover page with ID: {cover_page_id}")
                cover_page = await RfpDraftExportController.get_cover_page_from_datametastore(db, cover_page_id)
                cover_page_elements = PDFGenerator._create_cover_page_elements(
                    cover_page, tenant_details, opportunity_details, user_details, compliance, image_only=True
                )
            else:
                logger.info("No cover page ID provided, creating text-based cover page")
                cover_page_elements = PDFGenerator._create_cover_page_elements(
                    None, tenant_details, opportunity_details, user_details, compliance
                )

            # Get trailing page if provided
            trailing_page_markdown = None
            if trailing_page_id:
                trailing_page = await RfpDraftExportController.get_trailing_page_from_datametastore(db, trailing_page_id)
                if trailing_page:
                    trailing_page_markdown = PDFGenerator.convert_trailing_page_to_markdown(trailing_page)
                    logger.info("Trailing page content prepared for PDF generation")
                else:
                    logger.warning(f"Trailing page not found for ID {trailing_page_id}, proceeding without trailing page")

            # Convert to markdown first
            markdown_content = MarkdownRenderer.convert_draft_to_markdown(
                draft_list, opportunity_details, user_details, tenant_details
            )

            # Add table of contents at the beginning if available
            # Get the correct TOC field based on volume number
            toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
            logger.info(f"Checking for TOC: opportunity_details={opportunity_details is not None}, volume_number={volume_number}, toc_field={toc_field_name}")

            toc_data = None
            if opportunity_details and hasattr(opportunity_details, toc_field_name):
                toc_text = getattr(opportunity_details, toc_field_name)
                if toc_text:
                    logger.info(f"TOC text found for volume {volume_number}: {toc_text[:100]}...")
                    try:
                        # Parse the JSON TOC structure
                        toc_data = json.loads(toc_text)
                        logger.info(f"TOC JSON parsed successfully for volume {volume_number}, {len(toc_data)} items")
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse TOC JSON for volume {volume_number}: {e}")
                    except Exception as e:
                        logger.error(f"Error processing table of contents for volume {volume_number}: {e}")
                        import traceback
                        logger.error(f"Traceback: {traceback.format_exc()}")
                else:
                    logger.info(f"No TOC text found for volume {volume_number} in field {toc_field_name}")
            else:
                logger.info(f"Opportunity details missing or no {toc_field_name} field available")

            # Check for encoding issues (similar to Java code)
            replacement_chars = markdown_content.count('\ufffd')
            if replacement_chars > 0:
                logger.warning(f"Found {replacement_chars} Unicode replacement characters in content")

            # Log content length for debugging
            logger.info(f"Content generated (length: {len(markdown_content)})")

            # Generate PDF using the utility class
         

            file_path, success_message = PDFGenerator.generate_pdf(
                markdown_content=markdown_content,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                cover_page_elements=cover_page_elements,
                toc_data=toc_data,
                trailing_page_markdown=trailing_page_markdown,
                compliance=compliance,
                volume_number=volume_number,
               
            )

            return file_path, success_message

        except Exception as e:
            logger.error(f"Error exporting RFP draft: {e}")
            raise Exception(f"Error generating RFP draft: {str(e)}") 
   
    @staticmethod
    async def export_rfp_draft_docx(
        db: AsyncSession,
        opportunity_id: str,
        tenant_id: str,
        user_id: int,
        version: int,
        cover_page_id: Optional[int] = None,
        trailing_page_id: Optional[int] = None,
        volume_number: int = 1,
        include_toc: bool = True,
        toc_title: Optional[str] = None,
        toc_levels: str = "1-2",
    ) -> tuple[str, str]:
        """
        Export RFP draft to a DOCX file.

        Parameters mirror export_rfp_draft. Draft content is rendered to HTML and
        then converted to DOCX via pandoc.
        """
        try:
            # Get draft data
            draft_list = await RfpDraftExportController.get_draft_by_opportunity_id(db, opportunity_id)
            if not draft_list:
                raise Exception("No draft data found for the specified opportunity")

            # Get related details (kept for parity and potential future use)
            opportunity_details = await RfpDraftExportController.get_opportunity_details(db, opportunity_id)
            user_details = await RfpDraftExportController.get_user_by_id(db, user_id)
            tenant_details = await RfpDraftExportController.get_tenant_by_id(db, tenant_id)

            compliance = DEFAULT_COMPLIANCE_SETTINGS.copy()
            
               # Get cover page if provided
            cover_page_elements = None
            if cover_page_id:
                logger.info(f"Processing cover page with ID: {cover_page_id}")
                cover_page = await RfpDraftExportController.get_cover_page_from_datametastore(db, cover_page_id)
                cover_page_elements = {
                    "cover_page": cover_page,
                    "tenant_details": tenant_details,
                    "opportunity_details": opportunity_details,
                    "user_details": user_details,
                    "compliance": compliance,
                    "image_only": True
                }
            else:
                logger.info("No cover page ID provided, creating text-based cover page")
                cover_page_elements = {
                    "cover_page": None,
                    "tenant_details": tenant_details,
                    "opportunity_details": opportunity_details,
                    "user_details": user_details,
                    "compliance": compliance,
                    "image_only": False
                }

            # Get the correct TOC field based on volume number for DOCX export
            toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
            logger.info(f"Checking for TOC in DOCX export: volume_number={volume_number}, toc_field={toc_field_name}")

            toc_data = None
            if opportunity_details and hasattr(opportunity_details, toc_field_name):
                toc_text = getattr(opportunity_details, toc_field_name)
                if toc_text:
                    logger.info(f"TOC text found for DOCX volume {volume_number}: {toc_text[:100]}...")
                    try:
                        # Parse the JSON TOC structure
                        toc_data = json.loads(toc_text)
                        logger.info(f"TOC JSON parsed successfully for DOCX volume {volume_number}, {len(toc_data)} items")
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse TOC JSON for DOCX volume {volume_number}: {e}")
                    except Exception as e:
                        logger.error(f"Error processing table of contents for DOCX volume {volume_number}: {e}")
                        import traceback
                        logger.error(f"Traceback: {traceback.format_exc()}")
                else:
                    logger.info(f"No TOC text found for DOCX volume {volume_number} in field {toc_field_name}")
            else:
                logger.info(f"No table of contents found for DOCX volume {volume_number}, proceeding without TOC")

            # Get trailing page if provided; append it at the end of document
            trailing_page_markdown = None
            if trailing_page_id:
                trailing_page = await RfpDraftExportController.get_trailing_page_from_datametastore(db, trailing_page_id)
                if trailing_page:
                    trailing_page_markdown = PDFGenerator.convert_trailing_page_to_markdown(trailing_page)
                else:
                    logger.warning(f"Trailing page not found for ID {trailing_page_id}, proceeding without trailing page")

            # Build markdown content
            markdown_content = MarkdownRenderer.convert_draft_to_markdown(
                draft_list, opportunity_details, user_details, tenant_details
            )

            if trailing_page_markdown:
                markdown_content = f"{markdown_content}\n\n---\n\n{trailing_page_markdown}"

            # Convert markdown to HTML with compliance formatting
            html_content = HtmlRenderer.render_markdown_to_html(
                markdown_content,
                font_type=compliance.get('font_type', 'Times-Roman'),
                font_size_body=compliance.get('font_size_body'),
                font_size_header=compliance.get('font_size_header'),
                font_size_title=compliance.get('font_size_header') + 4,
                font_size_subheading=compliance.get('font_size_body'),
                line_spacing=compliance.get('line_spacing'),
            )

            # Prepare output location (mirror PDF naming)
            from datetime import datetime as _dt
            from pathlib import Path as _Path

            output_dir = _Path("generated-docx")
            output_dir.mkdir(parents=True, exist_ok=True)
            timestamp = _dt.now().strftime("%Y%m%d_%H%M%S")
            filename = f"RFP_Draft_{opportunity_id}_{tenant_id}_{timestamp}.docx"
            output_path = output_dir / filename

            # Generate DOCX
            # Determine volume title text similar to PDF generator titles
            try:
                from services.exports.generate_pdf import get_volume_title
                volume_title_text = get_volume_title(volume_number)
            except Exception:
                volume_title_text = f"Volume {volume_number}"

            final_path = DocxGenerator.generate_docx_from_html(
                html_content=html_content,
                output_file_path=str(output_path),
                metadata={
                    "title": getattr(opportunity_details, 'title', 'RFP Draft') if opportunity_details else 'RFP Draft',
                },
                header_title=volume_title_text,
                cover_page=cover_page,
                cover_page_elements=cover_page_elements,
                include_toc=include_toc,
                toc_title=toc_title or "Table of Contents",
                toc_levels="1-2",
                toc_data=toc_data,
            )

            success_message = f"RFP draft DOCX generated successfully: {final_path}"
            return final_path, success_message

        except Exception as e:
            logger.error(f"Error exporting RFP draft DOCX: {e}")
            raise Exception(f"Error generating RFP draft DOCX: {str(e)}")
