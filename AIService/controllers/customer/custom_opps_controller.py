# AIService/controllers/customer/custom_opps_controller.py

from typing import Any, Dict, Optional
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from models.customer_models import CustomOppsTable

class CustomOpportunitiesController:
    """Controller for CustomOppsTable operations"""
    
    @staticmethod
    async def get_table_of_contents(
        db: AsyncSession,
        opportunity_id: str
    ) -> list:
        """
        Get all table_of_contents values from CustomOppsTable, ordered by id.
        """
        try:
            query = select(
                CustomOppsTable.toc_text,
                CustomOppsTable.toc_text_2,
                CustomOppsTable.toc_text_3,
                CustomOppsTable.toc_text_4,
                CustomOppsTable.toc_text_5,
            ).where(CustomOppsTable.opportunity_id == opportunity_id)
            result = await db.execute(query)
            rows = result.mappings().one_or_none()

            if rows is None:
                raise ValueError("Opportunity Does not Exist")
            
            table_of_contents = []

            table_of_contents.append(rows.toc_text if rows.toc_text else None)
            table_of_contents.append(rows.toc_text_2 if rows.toc_text_2 else None)
            table_of_contents.append(rows.toc_text_3 if rows.toc_text_3 else None)
            table_of_contents.append(rows.toc_text_4 if rows.toc_text_4 else None)
            table_of_contents.append(rows.toc_text_5 if rows.toc_text_5 else None)
            return table_of_contents
        except Exception as e:
            logger.error(f"Error fetching all table_of_contents from CustomOppsTable: {e}")
            return []

    @staticmethod
    async def get_all_proposal_outlines(
        db: AsyncSession,
        opportunity_id: str
    ) -> list:
        """
        Get all proposal_outline values from CustomOppsTable, ordered by id.
        """
        try:
            query = select(
                CustomOppsTable.proposal_outline_1,
                CustomOppsTable.proposal_outline_2,
                CustomOppsTable.proposal_outline_3,
                CustomOppsTable.proposal_outline_4,
                CustomOppsTable.proposal_outline_5,
            ).where(CustomOppsTable.opportunity_id == opportunity_id)
            result = await db.execute(query)
            rows = result.scalars().all()
            return [row if row is not None else None for row in rows]
        except Exception as e:
            logger.error(f"Error fetching all proposal_outline from CustomOppsTable: {e}")
            return []

    @staticmethod
    async def update_by_opportunity_id(
        db: AsyncSession,
        opportunity_id: str,
        update_fields: Dict[str, Any]
    ) -> Optional[CustomOppsTable]:
        """
        Update fields of a CustomOppsTable record by opportunity_id.
        Only allows updating valid fields.
        Returns the updated record or None if not found.
        """
        # Get valid columns from the model
        valid_columns = {col.name for col in CustomOppsTable.__table__.columns}
        # Filter update_fields to only valid columns
        safe_fields = {k: v for k, v in update_fields.items() if k in valid_columns and k != "id"}

        if not safe_fields:
            logger.warning("No valid fields to update for CustomOppsTable.")
            return None

        try:
            # Fetch the record
            query = select(CustomOppsTable).where(CustomOppsTable.opportunity_id == opportunity_id)
            result = await db.execute(query)
            record = result.scalar_one_or_none()
            if not record:
                logger.warning(f"No CustomOppsTable record found for opportunity_id={opportunity_id}")
                return None

            # Update fields
            for field, value in safe_fields.items():
                setattr(record, field, value)

            await db.commit()
            await db.refresh(record)
            logger.info(f"Updated CustomOppsTable record with opportunity_id={opportunity_id}")
            return record
        except Exception as e:
            logger.error(f"Error updating CustomOppsTable record: {e}")
            await db.rollback()
            return None

    @staticmethod
    async def get_by_opportunity_id(
        db: AsyncSession,
        opportunity_id: str
    ) -> Optional[Any]:
        """
        Get a CustomOppsTable record by opportunity_id, returning only selected fields.
        """
        try:
            query = select(
                CustomOppsTable.title,
                CustomOppsTable.description,
                CustomOppsTable.posted_date,
                CustomOppsTable.expiration_date,
                CustomOppsTable.naics_code,
                CustomOppsTable.opportunity_type,
                CustomOppsTable.classification_code,
                CustomOppsTable.point_of_contact_first_name,
                CustomOppsTable.point_of_contact_last_name,
                CustomOppsTable.point_of_contact_email,
                CustomOppsTable.point_of_contact_phone,
                CustomOppsTable.place_of_performance_city,
                CustomOppsTable.place_of_performance_state,
                CustomOppsTable.place_of_performance_zip,
                CustomOppsTable.place_of_performance_country,
                CustomOppsTable.draft,
                CustomOppsTable.toc_text,
                CustomOppsTable.toc_text_2,
                CustomOppsTable.toc_text_3,
                CustomOppsTable.toc_text_4,
                CustomOppsTable.toc_text_5,
                CustomOppsTable.format_compliance,
            ).where(CustomOppsTable.opportunity_id == opportunity_id)
            result = await db.execute(query)
            return result.mappings().one_or_none()
        except Exception as e:
            logger.error(f"Error fetching CustomOppsTable record by opportunity_id={opportunity_id}: {e}")
            return None

    @staticmethod
    async def get_main_info_by_opportunity_id(
        db: AsyncSession,
        opportunity_id: str
    ) -> Optional[Any]:
        """
        Get a CustomOppsTable record by opportunity_id, returning only selected fields.
        """
        try:
            query = select(
                CustomOppsTable.title,
                CustomOppsTable.description,
                CustomOppsTable.posted_date,
                CustomOppsTable.expiration_date,
                CustomOppsTable.naics_code,
                CustomOppsTable.opportunity_type,
                CustomOppsTable.classification_code,
                CustomOppsTable.point_of_contact_first_name,
                CustomOppsTable.point_of_contact_last_name,
                CustomOppsTable.point_of_contact_email,
                CustomOppsTable.point_of_contact_phone,
                CustomOppsTable.place_of_performance_city,
                CustomOppsTable.place_of_performance_state,
                CustomOppsTable.place_of_performance_zip,
                CustomOppsTable.place_of_performance_country
            ).where(CustomOppsTable.opportunity_id == opportunity_id)
            result = await db.execute(query)
            return result.mappings().one_or_none()
        except Exception as e:
            logger.error(f"Error fetching CustomOppsTable record by opportunity_id={opportunity_id}: {e}")
            return None


    @staticmethod
    async def get_keywords_by_opportunity_id(
        db: AsyncSession,
        opportunity_id: str
    ) -> Optional[str]:
        """
        Get the keywords column for a CustomOppsTable record by opportunity_id.
        Returns the keywords string or None if not found.
        """
        try:
            query = select(CustomOppsTable.keywords).where(CustomOppsTable.opportunity_id == opportunity_id)
            result = await db.execute(query)
            row = result.first()
            return row[0] if row else None
        except Exception as e:
            logger.error(f"Error fetching keywords for opportunity_id={opportunity_id}: {e}")
            return None