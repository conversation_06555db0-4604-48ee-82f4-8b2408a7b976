#!/usr/bin/env python3
"""
Standalone JSON to PDF/DOCX Export Tool

This script allows you to export existing JSON files (outlines, drafts, TOCs) 
to PDF and DOCX formats without running the entire pipeline again.

Usage:
    python3 export_jsons_to_formats.py --folder /path/to/proposal/folder --opportunity-id OPID123 --tenant-id TID456
    
    Or for interactive mode:
    python3 export_jsons_to_formats.py
"""

import asyncio
import os
import json
import argparse
import shutil
from pathlib import Path
from typing import Optional, Dict, List, Any
from datetime import datetime

# Import required controllers and services
from controllers.customer.rfp_draft_export_controller import RfpDraftExportController
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from database import get_customer_db


class JSONExporter:
    """Handles exporting JSON files to PDF and DOCX formats"""
    
    def __init__(self, folder_path: str, opportunity_id: str, tenant_id: str):
        self.folder_path = folder_path
        self.opportunity_id = opportunity_id
        self.tenant_id = tenant_id
        self.user_id = 69  # Default user ID as used in pipeline
        self.version = 1
        self.cover_page_id = 4344  # Default cover page ID
        
    def find_json_files(self) -> Dict[str, List[str]]:
        """Find all JSON files in the folder structure"""
        json_files = {
            "outlines": [],
            "drafts": [],
            "tocs": []
        }
        
        # Search for JSON files in different directories
        for root, dirs, files in os.walk(self.folder_path):
            for file in files:
                if file.endswith('.json'):
                    file_path = os.path.join(root, file)
                    
                    # Categorize based on filename patterns
                    if 'outline' in file.lower():
                        json_files["outlines"].append(file_path)
                    elif 'draft' in file.lower():
                        json_files["drafts"].append(file_path)
                    elif 'toc' in file.lower():
                        json_files["tocs"].append(file_path)
        
        return json_files
    
    def extract_volume_number(self, filename: str) -> int:
        """Extract volume number from filename"""
        try:
            # Look for patterns like "volume_1", "vol_1", "_1_", etc.
            import re
            patterns = [
                r'volume[_\s]*(\d+)',
                r'vol[_\s]*(\d+)',
                r'_(\d+)_'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, filename.lower())
                if match:
                    return int(match.group(1))
            
            # Default to volume 1 if no number found
            return 1
        except:
            return 1
    
    def load_json_file(self, file_path: str) -> Optional[Dict]:
        """Load and parse JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return None
    
    def clean_outline_content_for_draft(self, markdown_content: str, title: str) -> str:
        """Minimal cleaning of outline content - let MarkdownRenderer handle all formatting"""
        try:
            lines = markdown_content.split('\n')
            cleaned_lines = []

            for line in lines:
                line = line.strip()

                # Skip empty lines
                if not line:
                    cleaned_lines.append('')
                    continue

                # Skip the main title line (MarkdownRenderer adds ## {title} automatically)
                if line.startswith(f"## {title}") or line.startswith(f"# {title}"):
                    continue

                # Skip page limit lines
                if "(Page limit:" in line and "pages)" in line:
                    continue

                # Remove quotes from content but keep the structure
                cleaned_line = line.replace('"', '').replace("'", "'")
                cleaned_lines.append(cleaned_line)

            # Remove references section completely
            result_lines = []
            in_references = False

            for line in cleaned_lines:
                if line.strip().startswith('**References:**'):
                    in_references = True
                    continue

                if in_references:
                    # Check if we've moved to a new section
                    if line.strip().startswith('**') and not line.strip().startswith('**References:**'):
                        in_references = False
                        result_lines.append(line)
                    # Skip reference lines
                    continue
                else:
                    result_lines.append(line)

            return '\n'.join(result_lines).strip()

        except Exception as e:
            print(f"Error cleaning outline content: {e}")
            return markdown_content
    
    async def export_outline_to_formats(self, outline_file: str, toc_file: Optional[str] = None) -> Dict[str, str]:
        """Export outline JSON to both PDF and DOCX formats"""
        print(f"\n📄 Exporting outline: {os.path.basename(outline_file)}")
        
        # Load outline data
        outline_data = self.load_json_file(outline_file)
        if not outline_data:
            return {"pdf": "", "docx": ""}
        
        # Load TOC data if available
        volume_toc = None
        if toc_file and os.path.exists(toc_file):
            toc_data = self.load_json_file(toc_file)
            if toc_data and isinstance(toc_data, list):
                volume_toc = toc_data
        
        # Extract volume number
        volume_number = self.extract_volume_number(outline_file)
        
        # Get TOC numbering for proper section numbering
        toc_numbering = {}
        if volume_toc:
            for item in volume_toc:
                title = item.get("title", "")
                number = item.get("number", "")
                if title and number:
                    toc_numbering[title] = number
        
        # Convert outline data to draft format for the controller
        draft_list = []
        
        if "outlines" in outline_data and outline_data["outlines"]:
            for i, section in enumerate(outline_data["outlines"], 1):
                title = section.get("title", f"Section {i}")
                
                # Handle both markdown and content fields
                content = ""
                if "markdown" in section:
                    content = self.clean_outline_content_for_draft(section["markdown"], title)
                elif "content" in section:
                    content = section["content"]
                
                # Get section number from TOC for proper header numbering
                section_number = toc_numbering.get(title, "")
                numbered_title = f"{section_number} {title}" if section_number else title
                
                draft_list.append({
                    "title": numbered_title,
                    "content": content
                })
        
        # Prepare update fields
        toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
        update_fields = {
            "draft": json.dumps(draft_list)
        }
        
        # Add TOC data if provided
        if volume_toc:
            update_fields[toc_field_name] = json.dumps(volume_toc)
        
        # Update database
        async for db in get_customer_db():
            await CustomOpportunitiesController.update_by_opportunity_id(db, self.opportunity_id, update_fields)
            break
        
        # Create output directories
        pdf_dir = os.path.join(self.folder_path, "pdfs")
        docx_dir = os.path.join(self.folder_path, "docx")
        os.makedirs(pdf_dir, exist_ok=True)
        os.makedirs(docx_dir, exist_ok=True)
        
        results = {"pdf": "", "docx": ""}
        
        # Export to PDF
        try:
            async for db in get_customer_db():
                file_path, _ = await RfpDraftExportController.export_rfp_draft(
                    db=db,
                    opportunity_id=self.opportunity_id,
                    tenant_id=self.tenant_id,
                    user_id=self.user_id,
                    version=self.version,
                    cover_page_id=self.cover_page_id,
                    trailing_page_id=None,
                    volume_number=volume_number
                )
                break
            
            if file_path and os.path.exists(file_path):
                pdf_filename = f"outline_volume_{volume_number}_{self.opportunity_id}.pdf"
                target_path = os.path.join(pdf_dir, pdf_filename)
                shutil.move(file_path, target_path)
                results["pdf"] = target_path
                print(f"  ✅ PDF exported: {target_path}")
            else:
                print(f"  ❌ PDF export failed")
                
        except Exception as e:
            print(f"  ❌ PDF export error: {e}")
        
        # Export to DOCX
        try:
            async for db in get_customer_db():
                file_path, _ = await RfpDraftExportController.export_rfp_draft_docx(
                    db=db,
                    opportunity_id=self.opportunity_id,
                    tenant_id=self.tenant_id,
                    user_id=self.user_id,
                    version=self.version,
                    cover_page_id=self.cover_page_id,
                    trailing_page_id=None,
                    volume_number=volume_number,
                    include_toc=True,
                    toc_title="Table of Contents",
                    toc_levels="1-2"
                )
                break
            
            if file_path and os.path.exists(file_path):
                docx_filename = f"outline_volume_{volume_number}_{self.opportunity_id}.docx"
                target_path = os.path.join(docx_dir, docx_filename)
                shutil.move(file_path, target_path)
                results["docx"] = target_path
                print(f"  ✅ DOCX exported: {target_path}")
            else:
                print(f"  ❌ DOCX export failed")
                
        except Exception as e:
            print(f"  ❌ DOCX export error: {e}")
        
        return results

    async def export_draft_to_formats(self, draft_file: str, toc_file: Optional[str] = None) -> Dict[str, str]:
        """Export draft JSON to both PDF and DOCX formats"""
        print(f"\n📝 Exporting draft: {os.path.basename(draft_file)}")

        # Load draft data
        draft_data = self.load_json_file(draft_file)
        if not draft_data:
            return {"pdf": "", "docx": ""}

        # Load TOC data if available
        volume_toc = None
        if toc_file and os.path.exists(toc_file):
            toc_data = self.load_json_file(toc_file)
            if toc_data and isinstance(toc_data, list):
                volume_toc = toc_data

        # Extract volume number
        volume_number = self.extract_volume_number(draft_file)

        # Handle different draft data formats
        draft_list = []
        if "draft" in draft_data and isinstance(draft_data["draft"], list):
            draft_list = draft_data["draft"]
        elif isinstance(draft_data, list):
            draft_list = draft_data
        else:
            print(f"  ❌ Invalid draft data format")
            return {"pdf": "", "docx": ""}

        # Prepare update fields
        toc_field_name = f"toc_text" if volume_number == 1 else f"toc_text_{volume_number}"
        update_fields = {
            "draft": json.dumps(draft_list)
        }

        # Add TOC data if provided
        if volume_toc:
            update_fields[toc_field_name] = json.dumps(volume_toc)

        # Update database
        async for db in get_customer_db():
            await CustomOpportunitiesController.update_by_opportunity_id(db, self.opportunity_id, update_fields)
            break

        # Create output directories
        pdf_dir = os.path.join(self.folder_path, "pdfs")
        docx_dir = os.path.join(self.folder_path, "docx")
        os.makedirs(pdf_dir, exist_ok=True)
        os.makedirs(docx_dir, exist_ok=True)

        results = {"pdf": "", "docx": ""}

        # Export to PDF
        try:
            async for db in get_customer_db():
                file_path, _ = await RfpDraftExportController.export_rfp_draft(
                    db=db,
                    opportunity_id=self.opportunity_id,
                    tenant_id=self.tenant_id,
                    user_id=self.user_id,
                    version=self.version,
                    cover_page_id=self.cover_page_id,
                    trailing_page_id=None,
                    volume_number=volume_number
                )
                break

            if file_path and os.path.exists(file_path):
                pdf_filename = f"draft_volume_{volume_number}_{self.opportunity_id}.pdf"
                target_path = os.path.join(pdf_dir, pdf_filename)
                shutil.move(file_path, target_path)
                results["pdf"] = target_path
                print(f"  ✅ PDF exported: {target_path}")
            else:
                print(f"  ❌ PDF export failed")

        except Exception as e:
            print(f"  ❌ PDF export error: {e}")

        # Export to DOCX
        try:
            async for db in get_customer_db():
                file_path, _ = await RfpDraftExportController.export_rfp_draft_docx(
                    db=db,
                    opportunity_id=self.opportunity_id,
                    tenant_id=self.tenant_id,
                    user_id=self.user_id,
                    version=self.version,
                    cover_page_id=self.cover_page_id,
                    trailing_page_id=None,
                    volume_number=volume_number,
                    include_toc=True,
                    toc_title="Table of Contents",
                    toc_levels="1-2"
                )
                break

            if file_path and os.path.exists(file_path):
                docx_filename = f"draft_volume_{volume_number}_{self.opportunity_id}.docx"
                target_path = os.path.join(docx_dir, docx_filename)
                shutil.move(file_path, target_path)
                results["docx"] = target_path
                print(f"  ✅ DOCX exported: {target_path}")
            else:
                print(f"  ❌ DOCX export failed")

        except Exception as e:
            print(f"  ❌ DOCX export error: {e}")

        return results

    async def export_all_jsons(self, export_pdf: bool = True, export_docx: bool = True) -> Dict[str, Any]:
        """Export all JSON files found in the folder to specified formats"""
        print(f"\n🚀 Starting JSON export for folder: {self.folder_path}")
        print(f"📋 Opportunity ID: {self.opportunity_id}")
        print(f"🏢 Tenant ID: {self.tenant_id}")
        print(f"📄 Export PDF: {export_pdf}")
        print(f"📝 Export DOCX: {export_docx}")

        # Find all JSON files
        json_files = self.find_json_files()

        print(f"\n📁 Found JSON files:")
        print(f"  - Outlines: {len(json_files['outlines'])}")
        print(f"  - Drafts: {len(json_files['drafts'])}")
        print(f"  - TOCs: {len(json_files['tocs'])}")

        if not any(json_files.values()):
            print("❌ No JSON files found in the specified folder!")
            return {}

        results = {
            "outlines": {},
            "drafts": {},
            "summary": {
                "total_exported": 0,
                "pdf_count": 0,
                "docx_count": 0,
                "errors": []
            }
        }

        # Helper function to find matching TOC file
        def find_matching_toc(content_file: str) -> Optional[str]:
            volume_num = self.extract_volume_number(content_file)
            for toc_file in json_files['tocs']:
                if self.extract_volume_number(toc_file) == volume_num:
                    return toc_file
            return None

        # Export outlines
        for outline_file in json_files['outlines']:
            try:
                toc_file = find_matching_toc(outline_file)
                export_results = await self.export_outline_to_formats(outline_file, toc_file)

                volume_num = self.extract_volume_number(outline_file)
                results["outlines"][f"volume_{volume_num}"] = export_results

                if export_results["pdf"]:
                    results["summary"]["pdf_count"] += 1
                if export_results["docx"]:
                    results["summary"]["docx_count"] += 1

                results["summary"]["total_exported"] += 1

            except Exception as e:
                error_msg = f"Error exporting outline {outline_file}: {e}"
                print(f"  ❌ {error_msg}")
                results["summary"]["errors"].append(error_msg)

        # Export drafts
        for draft_file in json_files['drafts']:
            try:
                toc_file = find_matching_toc(draft_file)
                export_results = await self.export_draft_to_formats(draft_file, toc_file)

                volume_num = self.extract_volume_number(draft_file)
                results["drafts"][f"volume_{volume_num}"] = export_results

                if export_results["pdf"]:
                    results["summary"]["pdf_count"] += 1
                if export_results["docx"]:
                    results["summary"]["docx_count"] += 1

                results["summary"]["total_exported"] += 1

            except Exception as e:
                error_msg = f"Error exporting draft {draft_file}: {e}"
                print(f"  ❌ {error_msg}")
                results["summary"]["errors"].append(error_msg)

        # Print summary
        print(f"\n🎉 Export completed!")
        print(f"📊 Summary:")
        print(f"  - Total files processed: {results['summary']['total_exported']}")
        print(f"  - PDF files created: {results['summary']['pdf_count']}")
        print(f"  - DOCX files created: {results['summary']['docx_count']}")
        if results['summary']['errors']:
            print(f"  - Errors: {len(results['summary']['errors'])}")
            for error in results['summary']['errors']:
                print(f"    • {error}")

        return results


def get_user_input() -> Dict[str, str]:
    """Get user input for interactive mode"""
    print("\n" + "="*60)
    print("📋 JSON TO PDF/DOCX EXPORT TOOL")
    print("="*60)

    # Get folder path
    while True:
        folder_path = input("\n📁 Enter the proposal folder path: ").strip()
        if not folder_path:
            print("❌ Folder path cannot be empty!")
            continue

        if not os.path.exists(folder_path):
            print(f"❌ Folder does not exist: {folder_path}")
            continue

        # Check if it looks like a proposal folder
        has_json = False
        for root, dirs, files in os.walk(folder_path):
            if any(f.endswith('.json') for f in files):
                has_json = True
                break

        if not has_json:
            print(f"⚠️  No JSON files found in {folder_path}")
            confirm = input("Continue anyway? (y/n): ").strip().lower()
            if confirm != 'y':
                continue

        break

    # Get opportunity ID
    while True:
        opportunity_id = input("\n🆔 Enter opportunity ID: ").strip()
        if opportunity_id:
            break
        print("❌ Opportunity ID cannot be empty!")

    # Get tenant ID
    while True:
        tenant_id = input("\n🏢 Enter tenant ID: ").strip()
        if tenant_id:
            break
        print("❌ Tenant ID cannot be empty!")

    return {
        "folder_path": folder_path,
        "opportunity_id": opportunity_id,
        "tenant_id": tenant_id
    }


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Export JSON files (outlines, drafts) to PDF and DOCX formats",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Interactive mode
  python3 export_jsons_to_formats.py

  # Command line mode
  python3 export_jsons_to_formats.py --folder /path/to/proposal --opportunity-id OPID123 --tenant-id TID456

  # Export only PDFs
  python3 export_jsons_to_formats.py --folder /path/to/proposal --opportunity-id OPID123 --tenant-id TID456 --no-docx

  # Export only DOCX
  python3 export_jsons_to_formats.py --folder /path/to/proposal --opportunity-id OPID123 --tenant-id TID456 --no-pdf
        """
    )

    parser.add_argument(
        "--folder", "-f",
        help="Path to the proposal folder containing JSON files"
    )

    parser.add_argument(
        "--opportunity-id", "-o",
        help="Opportunity ID for the export"
    )

    parser.add_argument(
        "--tenant-id", "-t",
        help="Tenant ID for the export"
    )

    parser.add_argument(
        "--no-pdf",
        action="store_true",
        help="Skip PDF export (only export DOCX)"
    )

    parser.add_argument(
        "--no-docx",
        action="store_true",
        help="Skip DOCX export (only export PDF)"
    )

    parser.add_argument(
        "--list-files", "-l",
        action="store_true",
        help="List JSON files in the folder and exit"
    )

    return parser.parse_args()


async def main():
    """Main execution function"""
    args = parse_arguments()

    # Handle list files mode
    if args.list_files:
        if not args.folder:
            print("❌ --folder is required when using --list-files")
            return

        if not os.path.exists(args.folder):
            print(f"❌ Folder does not exist: {args.folder}")
            return

        # Create temporary exporter to use file finding logic
        temp_exporter = JSONExporter(args.folder, "temp", "temp")
        json_files = temp_exporter.find_json_files()

        print(f"\n📁 JSON files in {args.folder}:")
        print(f"\n📄 Outlines ({len(json_files['outlines'])}):")
        for f in json_files['outlines']:
            print(f"  - {os.path.relpath(f, args.folder)}")

        print(f"\n📝 Drafts ({len(json_files['drafts'])}):")
        for f in json_files['drafts']:
            print(f"  - {os.path.relpath(f, args.folder)}")

        print(f"\n📋 TOCs ({len(json_files['tocs'])}):")
        for f in json_files['tocs']:
            print(f"  - {os.path.relpath(f, args.folder)}")

        return

    # Get parameters (command line or interactive)
    if args.folder and args.opportunity_id and args.tenant_id:
        # Command line mode
        params = {
            "folder_path": args.folder,
            "opportunity_id": args.opportunity_id,
            "tenant_id": args.tenant_id
        }
    else:
        # Interactive mode
        params = get_user_input()

    # Validate folder exists
    if not os.path.exists(params["folder_path"]):
        print(f"❌ Folder does not exist: {params['folder_path']}")
        return

    # Determine export formats
    export_pdf = not args.no_pdf
    export_docx = not args.no_docx

    if not export_pdf and not export_docx:
        print("❌ Cannot skip both PDF and DOCX export!")
        return

    # Create exporter and run
    exporter = JSONExporter(
        folder_path=params["folder_path"],
        opportunity_id=params["opportunity_id"],
        tenant_id=params["tenant_id"]
    )

    try:
        results = await exporter.export_all_jsons(
            export_pdf=export_pdf,
            export_docx=export_docx
        )

        # Save results summary
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = os.path.join(params["folder_path"], f"export_results_{timestamp}.json")

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Results saved to: {results_file}")

    except Exception as e:
        print(f"\n❌ Export failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
