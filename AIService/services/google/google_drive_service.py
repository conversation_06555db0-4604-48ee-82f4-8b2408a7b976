import io
import os
from typing import List, Optional

from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaIoBaseDownload
from google.oauth2.credentials import Credentials
from PyPDF2 import PdfReader

class GoogleDriveService:
    def __init__(self):
        pass

    def create_drive_service(self, access_token: str, refresh_token: Optional[str] = None):
        """Create Google Drive API client using an OAuth access token."""
        creds = Credentials(token=access_token, refresh_token=refresh_token)
        return build('drive', 'v3', credentials=creds)

    def list_all_files(self, access_token: str) -> List[dict]:
        """List all non-trashed files in the user's Google Drive."""
        try:
            drive = self.create_drive_service(access_token)
            results = drive.files().list(
                q="trashed = false",
                fields="files(id, name, size, createdTime, modifiedTime, fileExtension, webContentLink, webViewLink, iconLink, thumbnailLink, owners, shared, mimeType)",
                orderBy="modifiedTime desc"
            ).execute()
            return results.get('files', [])
        except HttpError as e:
            raise e

    def get_file_content(self, access_token: str, file_id: str, mime_type: str, refresh_token: Optional[str] = None) -> str:
        """Download file content, handling Google Docs and PDFs."""
        try:
            drive = self.create_drive_service(access_token=access_token, refresh_token=refresh_token)
            if mime_type.startswith("application/vnd.google-apps."):
                # Export Google Docs as plain text
                try:
                    request = drive.files().export_media(fileId=file_id, mimeType="text/plain")
                    fh = io.BytesIO()
                    downloader = MediaIoBaseDownload(fh, request)
                    done = False
                    while not done:
                        status, done = downloader.next_chunk()
                    return fh.getvalue().decode("utf-8")
                except Exception as e:
                    print(e)
                    return ""
            else:
                # Download non-Google files (e.g., TXT, PDF)
                try:
                    request = drive.files().get_media(fileId=file_id)
                    fh = io.BytesIO()
                    downloader = MediaIoBaseDownload(fh, request)
                    done = False
                    while not done:
                        status, done = downloader.next_chunk()
                    file_bytes = fh.getvalue()
                    if mime_type != "application/pdf":
                        return file_bytes.decode("utf-8", errors="ignore")
                    # PDF: extract text
                    pdf_stream = io.BytesIO(file_bytes)
                    reader = PdfReader(pdf_stream)
                    text = ""
                    for page in reader.pages:
                        text += page.extract_text() or ""
                    return text
                except Exception as e:
                    print(e)
                    return ""
        except Exception as e:
            return ""

    def get_file_content_with_refresh(self, access_token: str, refresh_token: str, file_id: str, mime_type: str, refresh_callback) -> str:
        """
        Download file content, refreshing token if needed.
        refresh_callback should be a function that takes refresh_token and returns a new access_token.
        """
        try:
            return self.get_file_content(access_token, file_id, mime_type)
        except HttpError as e:
            if "401" in str(e):
                new_access_token = refresh_callback(refresh_token)
                return self.get_file_content(new_access_token, file_id, mime_type)
            raise e