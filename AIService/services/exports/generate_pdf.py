"""
PDF generation service for RFP draft export
"""
import re
import os
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Any, List, Optional, Dict
from html import unescape

# Import PyPDF2 for page counting (will be used for accurate TOC)
try:
    from PyPDF2 import PdfReader
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    logger.warning("PyPDF2 not available, will use fallback TOC page calculation")

from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Table, TableStyle, Image, PageTemplate, Frame
from reportlab.platypus.flowables import Flowable
from reportlab.lib import colors
from reportlab.lib.enums import TA_JUSTIFY, TA_LEFT, TA_CENTER
from reportlab.pdfgen import canvas
from loguru import logger
from models.customer_models import AESTenant, CustomOppsTable, DataMetastore, Users


# Constants
DEFAULT_FONT_TYPE = 'Times-Roman'
DEFAULT_FONT_SIZE_BODY = 12
DEFAULT_FONT_SIZE_HEADER = 14
DEFAULT_FONT_SIZE_FOOTER = 10
DEFAULT_LINE_SPACING = 1.5
DEFAULT_MARGIN = 50
PAGE_WIDTH_POINTS = letter[0]  # 8.5 inches * 72 points/inch = 612 points
PAGE_HEIGHT_POINTS = letter[1]  # 11 inches * 72 points/inch = 792 points
TARGET_DPI = 300
TARGET_WIDTH_PX = int(8.5 * TARGET_DPI)  # 2550 pixels
TARGET_HEIGHT_PX = int(11 * TARGET_DPI)  # 3300 pixels

# Volume titles mapping
VOLUME_TITLES = {
    1: "Volume I: Technical Approach - (Solution, Compliance, Management, Staffing)",
    2: "Volume II: Cost/Price Proposal (BOE, Pricing Models, Labor)",
    3: "Volume III: Past Performance (Case Studies, References)",
    4: "Volume IV: Small Business & Subcontracting Plan",
    5: "Volume V: Compliance & Certifications (Security, Regulatory, Eligibility)"
}


def get_font_name(base_font: str, is_bold: bool = False) -> str:
    """Helper function to get correct font name"""
    if base_font.lower() == 'times-roman':
        return 'Times-Bold' if is_bold else 'Times-Roman'
    elif base_font.lower() == 'helvetica':
        return 'Helvetica-Bold' if is_bold else 'Helvetica'
    elif base_font.lower() == 'courier':
        return 'Courier-Bold' if is_bold else 'Courier'
    else:
        # Default to Times-Roman
        return 'Times-Bold' if is_bold else 'Times-Roman'


def get_volume_title(volume_number: int) -> str:
    """Get the title for a given volume number"""
    return VOLUME_TITLES.get(volume_number, f"Volume {volume_number}")


class NumberedCanvas(canvas.Canvas):
    """Custom canvas that adds headers and footers with page numbers"""
    
    def __init__(self, *args, volume_number=1, font_type=DEFAULT_FONT_TYPE,
                 font_size_header=DEFAULT_FONT_SIZE_HEADER,
                 font_size_footer=DEFAULT_FONT_SIZE_FOOTER,
                 has_cover_page=False, **kwargs):
        canvas.Canvas.__init__(self, *args, **kwargs)
        self._saved_page_states = []
        self.volume_number = volume_number
        self.font_type = font_type
        self.font_size_header = font_size_header
        self.font_size_footer = font_size_footer
        self.volume_title = VOLUME_TITLES.get(volume_number, f"Volume {volume_number}")
        self.has_cover_page = has_cover_page
        self._page_count = 0
        self._content_start_page = None

    def showPage(self):
        self._saved_page_states.append(dict(self.__dict__))
        self._startPage()
        self._page_count += 1

    def save(self):
        """Add header, footer, and page info to each page"""
        num_pages = len(self._saved_page_states)
        for state in self._saved_page_states:
            self.__dict__.update(state)
            # Skip headers/footers for cover page (first page)
            if not (self.has_cover_page and self._pageNumber == 1):
                self.draw_header_footer(num_pages)
            canvas.Canvas.showPage(self)
        canvas.Canvas.save(self)

    def draw_header_footer(self, page_count):
        """Draw header and footer on the current page"""
        # Get page dimensions
        page_width, page_height = letter
        
        # Draw header
        self._draw_header(page_width, page_height)
        
        # Draw footer with page number
        self._draw_footer(page_width, page_height, page_count)
    
    def _draw_header(self, page_width: float, page_height: float):
        """Draw the header with volume title"""
        # Set font for header using body font size
        header_font = get_font_name(self.font_type)
        self.setFont(header_font, self.font_size_body)
        self.setFillColor(colors.black)
        
        # Position header at top of page with margin
        header_y = page_height - 30  # 30 points from top
        header_x = 50  # 50 points from left (same as margin)
        
        # Draw volume title
        self.drawString(header_x, header_y, self.volume_title)
    
    def _draw_footer(self, page_width: float, page_height: float, page_count: int):
        """Draw the footer with page number"""
        # Set font for footer
        footer_font = get_font_name(self.font_type, is_bold=False)
        self.setFont(footer_font, self.font_size_footer)
        self.setFillColor(colors.black)

        # Position footer at bottom of page with margin
        footer_y = 30  # 30 points from bottom

        # Calculate content page number (restart numbering for content)
        if self.has_cover_page:
            # Cover page = no number, TOC = no number, Content = 1, 2, 3...
            if self._pageNumber <= 2:
                return  # Don't show page numbers on cover page and TOC
            content_page_number = self._pageNumber - 2
        else:
            # TOC = no number, Content = 1, 2, 3...
            if self._pageNumber == 1:
                return  # Don't show page number on TOC
            content_page_number = self._pageNumber - 1

        # Draw page number (center-aligned)
        page_text = str(content_page_number)
        text_width = self.stringWidth(page_text, footer_font, self.font_size_footer)
        center_x = page_width / 2  # Center of page
        self.drawString(center_x - (text_width / 2), footer_y, page_text)


class HeaderFooterTemplate:
    """Legacy template class for backward compatibility"""
    
    def __init__(self, volume_number: int = 1, font_type: str = DEFAULT_FONT_TYPE, 
                 font_size_body: int = DEFAULT_FONT_SIZE_BODY, 
                 font_size_footer: int = DEFAULT_FONT_SIZE_FOOTER):
        self.volume_number = volume_number
        self.font_type = font_type
        self.font_size_body = font_size_body
        self.font_size_footer = font_size_footer
        self.volume_title = VOLUME_TITLES.get(volume_number, f"Volume {volume_number}")
    
    def __call__(self, canvas, doc):
        """Draw header and footer on each page"""
        # Save the current state
        canvas.saveState()
        
        # Get page dimensions
        page_width, page_height = letter
        
        # Draw header
        self._draw_header(canvas, page_width, page_height)
        
        # Draw footer with page number
        self._draw_footer(canvas, page_width, page_height, doc.page)
        
        # Restore the state
        canvas.restoreState()
    
    def _draw_header(self, canvas, page_width: float, page_height: float):
        """Draw the header with volume title"""
        # Set font for header using body font size
        header_font = get_font_name(self.font_type)
        canvas.setFont(header_font, self.font_size_body)
        canvas.setFillColor(colors.black)
        header_y = page_height - 30  # 30 points from top
        # Position header at top of page with margin
        text_width = canvas.stringWidth(self.volume_title, header_font, self.font_size_body)
        center_x = page_width / 2  # Center of page
        canvas.drawString(center_x - (text_width / 2), header_y, self.volume_title)
    
    def _draw_footer(self, canvas, page_width: float, page_height: float, page_number: int):
        """Draw the footer with page number"""
        # Set font for footer
        footer_font = get_font_name(self.font_type, is_bold=False)
        canvas.setFont(footer_font, self.font_size_footer)
        canvas.setFillColor(colors.black)
        
        # Position footer at bottom of page with margin
        footer_y = 30  # 30 points from bottom
        
        # Draw page number (center-aligned)
        page_text = str(page_number)
        text_width = canvas.stringWidth(page_text, footer_font, self.font_size_footer)
        center_x = page_width / 2  # Center of page
        canvas.drawString(center_x - (text_width / 2), footer_y, page_text)
        


class FullPageImageFlowable(Flowable):
    """Custom flowable for drawing full-page cover images with optional text overlay"""
    
    def __init__(self, image_path: str, text_data: Optional[Dict[str, Any]] = None, font_type: str = DEFAULT_FONT_TYPE, font_size_body: int = DEFAULT_FONT_SIZE_BODY, font_size_header: int = DEFAULT_FONT_SIZE_HEADER, font_size_footer: int = DEFAULT_FONT_SIZE_FOOTER, line_spacing: float = DEFAULT_LINE_SPACING, is_cover_page: bool = True):
        super().__init__()
        self.image_path = image_path
        self.text_data = text_data  # Dictionary containing text information
        self.font_type = font_type
        self.font_size_body = font_size_body
        self.font_size_header = font_size_header
        self.font_size_footer = font_size_footer
        self.line_spacing = line_spacing
        self.is_cover_page = is_cover_page
        # Set dimensions based on whether this is a cover page or not
        if is_cover_page:
            # Full page size for cover pages
            self.width, self.height = letter
        else:
            # Reduced size for regular pages (accounting for headers/footers)
            margin_points = 50
            self.width = letter[0] - (2 * margin_points)
            self.height = letter[1] - (2 * margin_points) - 80  # Account for header and footer space
    
    def wrap(self, availWidth: float, availHeight: float) -> tuple[float, float]:
        """Return the size this flowable will take"""
        return (self.width, self.height)
    
    def draw(self):
        """Draw the image to cover the available space and add text overlay if provided"""
        # Get the current page dimensions
        page_width, page_height = letter
        
        if self.is_cover_page:
            # For cover pages, draw image to cover the entire page (no margins)
            self.canv.drawImage(self.image_path, 0, 0, width=page_width, height=page_height)
            logger.info(f"Drew full-page cover image: {self.image_path}")
            
            # Draw text overlay if text_data is provided
            if self.text_data:
                self._draw_text_overlay(page_width, page_height)
        else:
            # For regular pages, draw image within the frame bounds
            margin_points = 50
            x = margin_points
            y = margin_points + 40  # Account for header space
            width = page_width - (2 * margin_points)
            height = page_height - (2 * margin_points) - 80  # Account for header and footer space
            
            self.canv.drawImage(self.image_path, x, y, width=width, height=height)
            logger.info(f"Drew framed image: {self.image_path}")
            
            # Draw text overlay if text_data is provided
            if self.text_data:
                self._draw_text_overlay(width, height, x, y)
    
    def _draw_text_overlay(self, page_width: float, page_height: float, offset_x: float = 0, offset_y: float = 0):
        """Draw text overlay on the cover page"""
        try:
            # Set text color to white for better visibility on images
            self.canv.setFillColor(colors.white)
            
            # Calculate starting position (center of available space)
            center_x = offset_x + (page_width / 2)
            current_y = offset_y + page_height - 150  # Start from top with some margin
            
            # Draw opportunity title
            if self.text_data.get('opportunity_title'):
                self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_header)
                title = self.text_data['opportunity_title']
                self.canv.drawCentredString(center_x, current_y, title)
                current_y -= 60
            
            # Draw agency name
            if self.text_data.get('agency_name'):
                self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_header)
                agency_name = self.text_data['agency_name']
                self.canv.drawCentredString(center_x, current_y, agency_name)
                current_y -= 40
            
            # Draw "Submitted by:" section
            self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_body)
            self.canv.drawCentredString(center_x, current_y, "Submitted by:")
            current_y -= 30
            
            # Draw tenant name
            if self.text_data.get('tenant_name'):
                self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_body)
                tenant_name = self.text_data['tenant_name']
                self.canv.drawCentredString(center_x, current_y, tenant_name)
                current_y -= 25
            
            # Draw tenant address
            if self.text_data.get('tenant_address'):
                self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_body)
                address_lines = self.text_data['tenant_address'].split('<br/>')
                for line in address_lines:
                    if line.strip():
                        self.canv.drawCentredString(center_x, current_y, line.strip())
                        current_y -= 20
                current_y -= 10
            
            # Draw "Submitted to:" section
            self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_body)
            self.canv.drawCentredString(center_x, current_y, "Submitted to:")
            current_y -= 30
            
            # Draw opportunity POC details
            if self.text_data.get('opportunity_poc'):
                self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_body)
                poc_lines = self.text_data['opportunity_poc'].split('<br/>')
                for line in poc_lines:
                    if line.strip():
                        self.canv.drawCentredString(center_x, current_y, line.strip())
                        current_y -= 20
                current_y -= 10
            
            # Draw classification code
            if self.text_data.get('classification_code'):
                self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_body)
                class_text = f"Classification Code: {self.text_data['classification_code']}"
                self.canv.drawCentredString(center_x, current_y, class_text)
                current_y -= 20
            
            # Draw opportunity ID
            if self.text_data.get('opportunity_id'):
                self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_body)
                opp_text = f"Opportunity ID: {self.text_data['opportunity_id']}"
                self.canv.drawCentredString(center_x, current_y, opp_text)
                current_y -= 30
            
            # Draw date
            if self.text_data.get('date'):
                self.canv.setFont(get_font_name(self.font_type, is_bold=True), self.font_size_body)
                self.canv.drawCentredString(center_x, current_y, self.text_data['date'])
            
            # Draw disclaimer
            #self._draw_disclaimer(center_x, offset_y + 50)  # 50 points from bottom
            
            logger.info("Successfully drew text overlay on cover page")
            
        except Exception as e:
            logger.error(f"Error drawing text overlay: {e}")
    
    def _draw_disclaimer(self, center_x: float, disclaimer_y: float):
        """Draw the disclaimer text at the bottom of the cover page"""
        try:
            # Set font for disclaimer (small font)
            disclaimer_font = get_font_name(self.font_type, is_bold=True)
            disclaimer_font_size = 8  # Small font size like in Java code
            self.canv.setFont(disclaimer_font, disclaimer_font_size)
            # Use white color to match other cover page text elements (for visibility on images)
            self.canv.setFillColor(colors.white)
            
            # Disclaimer text (same as Java code)
            disclaimer = "This proposal includes data that shall not be disclosed outside the Government and shall not be duplicated, used, or disclosed—in whole or in part—for any purpose other than to evaluate this proposal. If, however, a contract is awarded to this offeror as a result of—or in connection with—the submission of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent provided in the resulting contract. This restriction does not limit the Government's right to use information contained in this data if it is obtained from another source without restriction. The data subject to this restriction are contained in all sheets of this document."
            
            # Wrap text to fit page width
            max_width = letter[0] - 100  # Page width minus margins
            disclaimer_lines = self._wrap_text(disclaimer, disclaimer_font, disclaimer_font_size, max_width)
            
            # Draw each line of the disclaimer
            current_y = disclaimer_y
            for line in disclaimer_lines:
                # Center the text
                text_width = self.canv.stringWidth(line, disclaimer_font, disclaimer_font_size)
                x_position = center_x - (text_width / 2)
                self.canv.drawString(x_position, current_y, line)
                current_y -= 8  # Line spacing like in Java code
            
            logger.info("Successfully drew disclaimer on cover page")
            
        except Exception as e:
            logger.error(f"Error drawing disclaimer: {e}")
    
    def _wrap_text(self, text: str, font_name: str, font_size: int, max_width: float) -> List[str]:
        """Wrap text to fit within the specified width"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            test_width = self.canv.stringWidth(test_line, font_name, font_size)
            
            if test_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines











class TOCEntryFlowable(Flowable):
    """Custom flowable for table of contents entries with dots extending to the right edge"""
    
    def __init__(self, title: str, page_number: int, level: int, font_type: str, font_size: int, line_spacing: float, available_width: float):
        super().__init__()
        self.title = title
        self.page_number = page_number
        self.level = level
        self.font_type = font_type
        self.font_size = font_size
        self.line_spacing = line_spacing
        self.available_width = available_width
        self.height = font_size * line_spacing + 4  # Add some padding
    
    def wrap(self, availWidth: float, availHeight: float) -> tuple[float, float]:
        """Return the size this flowable will take"""
        return (availWidth, self.height)
    
    def draw(self):
        """Draw the TOC entry with dots extending to the right edge and aligned page numbers"""
        # Calculate indentation based on level
        indent = self.level * 20  # 20 points per level
        
        # Set font
        font_name = get_font_name(self.font_type, is_bold=True)
        self.canv.setFont(font_name, self.font_size)
        self.canv.setFillColor(colors.black)
        
        # Calculate text width
        text_width = self.canv.stringWidth(self.title, font_name, self.font_size)
        
        # Calculate page number width
        page_text = f" {self.page_number}"
        page_width = self.canv.stringWidth(page_text, font_name, self.font_size)
        
        # Get the actual available width from the canvas
        # The canvas width is the full page width minus margins
        canvas_width = self.canv._pagesize[0] - (2 * 50)  # Assuming 50pt margins
        
        # Fixed position for page numbers (right-aligned)
        page_x = canvas_width - page_width - 5  # 5 points from right edge
        
        # Calculate available space for dots
        # Start position is indent + text width + small gap
        start_x = indent + text_width + 5
        # End position is fixed page number position minus small gap
        end_x = page_x - 5
        
        # Calculate how many dots we can fit
        if end_x > start_x:
            dot_width = self.canv.stringWidth(".", font_name, self.font_size)
            available_dots_space = end_x - start_x
            max_dots = int(available_dots_space / dot_width)
            dots = "." * max(5, max_dots)  # Minimum 5 dots
        else:
            dots = "....."  # Minimum dots
        
        # Draw title with link
        self.canv.drawString(indent, 0, self.title)
        
        # Draw dots
        dots_x = start_x
        self.canv.drawString(dots_x, 0, dots)
        
        # Draw page number with link
        self.canv.drawString(page_x, 0, page_text)
        
        # Note: Internal linking would require more complex page destination management
        # For now, we focus on proper alignment and formatting


class PageTrackingFlowable(Flowable):
    """Custom flowable that tracks page numbers for TOC generation"""
    
    def __init__(self, title: str, level: int = 0):
        super().__init__()
        self.title = title
        self.level = level
        self.page_number = None
        self.height = 0
    
    def wrap(self, availWidth: float, availHeight: float) -> tuple[float, float]:
        """Return the size this flowable will take"""
        return (availWidth, self.height)
    
    def draw(self):
        """Draw nothing, just track page number"""
        if self.page_number is None:
            self.page_number = self.canv.getPageNumber()
        # Don't draw anything - this is just for tracking


class PDFGenerator:
    """Service for generating PDF documents from markdown content"""

    @staticmethod
    def _process_bold_text(text: str) -> str:
        """Convert **text** to ReportLab bold markup"""
        # Find all **text** patterns and convert to <b>text</b>
        def replace_bold(match):
            return f"<b>{match.group(1)}</b>"
        
        # Replace **text** with <b>text</b> for ReportLab
        result = re.sub(r'\*\*(.*?)\*\*', replace_bold, text)
        return result

    @staticmethod
    def _split_long_text(text: str, max_length: int = 500) -> List[str]:
        """Split very long text into smaller chunks to prevent layout issues"""
        if len(text) <= max_length:
            return [text]
        
        # Try to split at sentence boundaries first
        sentences = re.split(r'(?<=[.!?])\s+', text)
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) <= max_length:
                current_chunk += sentence + " "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + " "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # If we still have very long chunks, split by words
        final_chunks = []
        for chunk in chunks:
            if len(chunk) <= max_length:
                final_chunks.append(chunk)
            else:
                words = chunk.split()
                current_word_chunk = ""
                for word in words:
                    if len(current_word_chunk) + len(word) + 1 <= max_length:
                        current_word_chunk += word + " "
                    else:
                        if current_word_chunk:
                            final_chunks.append(current_word_chunk.strip())
                        current_word_chunk = word + " "
                if current_word_chunk:
                    final_chunks.append(current_word_chunk.strip())
        
        return final_chunks

    @staticmethod
    def _add_table_to_elements(elements: list, table_rows: list):
        """Add a properly formatted table to the elements list"""
        if not table_rows:
            logger.warning("PDF Generator: No table rows to add")
            return
        
        logger.info(f"PDF Generator: Adding table with {len(table_rows)} rows")
        
        # Clean up table rows - remove any separator rows that might have slipped through
        cleaned_rows = []
        for row in table_rows:
            # Check if this row is a separator
            is_separator = True
            for cell in row:
                cell_content = cell.strip()
                if cell_content and not all(c in ':-| ' for c in cell_content):
                    is_separator = False
                    break
            
            # Additional check for separator patterns
            if is_separator:
                combined_content = ''.join(row)
                if all(c in ':-| ' for c in combined_content) or '----' in combined_content:
                    logger.info(f"PDF Generator: Skipping separator row: {row}")
                    continue  # Skip this separator row
            
            cleaned_rows.append(row)
        
        # Use cleaned rows
        table_rows = cleaned_rows
        if not table_rows:
            logger.warning("PDF Generator: No valid table rows after cleaning")
            return
        
        logger.info(f"PDF Generator: Processing {len(table_rows)} cleaned table rows")
        
        # Process bold text in table cells and limit cell content length
        processed_rows = []
        for row in table_rows:
            processed_row = []
            for cell in row:
                # Limit cell content to prevent extremely long cells
                cell_content = cell.strip()
                if len(cell_content) > 1000:  # Limit to 1000 characters per cell
                    cell_content = cell_content[:997] + "..."
                    logger.warning(f"PDF Generator: Truncated long cell content to {len(cell_content)} characters")
                
                processed_cell = PDFGenerator._process_bold_text(cell_content)
                
                # Create Paragraph for each cell to support bold formatting with better text wrapping
                cell_style = ParagraphStyle(
                    'TableCell',
                    fontSize=8,  # Even smaller font for table cells
                    fontName='Times-Roman',
                    alignment=TA_LEFT,
                    leading=10,  # Tighter line spacing
                    wordWrap='LTR',  # Enable word wrapping
                    spaceBefore=2,
                    spaceAfter=2,
                    leftIndent=2,
                    rightIndent=2
                )
                processed_row.append(Paragraph(processed_cell, cell_style))
            processed_rows.append(processed_row)
        
        # Calculate available width (page width minus margins)
        available_width = PAGE_WIDTH_POINTS - (2 * inch)  # Subtract left and right margins
        
        # Calculate column widths based on content
        num_cols = len(table_rows[0]) if table_rows else 1
        col_width = available_width / num_cols
        
        # Create table with calculated column widths
        table = Table(processed_rows, colWidths=[col_width] * num_cols)
        
        # Determine if first row is a header (simple heuristic)
        has_header = len(table_rows) > 1
        
        # Basic table style with better cell management
        table_style = [
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Times-Roman'),
            ('FONTSIZE', (0, 0), (-1, -1), 8),  # Smaller font for tables
            ('BOTTOMPADDING', (0, 0), (-1, -1), 2),
            ('TOPPADDING', (0, 0), (-1, -1), 2),
            ('LEFTPADDING', (0, 0), (-1, -1), 2),
            ('RIGHTPADDING', (0, 0), (-1, -1), 2),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),  # Lighter grid
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('WORDWRAP', (0, 0), (-1, -1), 'LTR'),  # Enable word wrapping
            ('ROWBACKGROUNDS', (0, 0), (-1, -1), [colors.white, colors.lightgrey]),  # Alternating colors
        ]
        
        # Header styling if we have one
        if has_header:
            table_style.extend([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('FONTNAME', (0, 0), (-1, 0), 'Times-Bold'),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('FONTSIZE', (0, 0), (-1, 0), 9),  # Slightly larger for headers
                ('BOTTOMPADDING', (0, 0), (-1, 0), 4),
                ('TOPPADDING', (0, 0), (-1, 0), 4),
            ])
        
        table.setStyle(TableStyle(table_style))
        
        # Set table properties for better page handling
        table.hAlign = 'LEFT'
        table.keepWithNext = False
        table.splitByRow = True
        table.splitInRow = True  # Allow splitting within rows if needed
        table.repeatRows = 1 if has_header else 0  # Repeat header on new pages
        
        # Add a small spacer before the table
        elements.append(Spacer(1, 6))
        # Check if table is very large and split it if needed
        if len(processed_rows) > 20:  # If table has more than 20 rows, split it
            logger.info(f"PDF Generator: Large table detected ({len(processed_rows)} rows), splitting into chunks")
            chunk_size = 15  # Split into chunks of 15 rows
            for i in range(0, len(processed_rows), chunk_size):
                chunk = processed_rows[i:i + chunk_size]
                chunk_table = Table(chunk, colWidths=[col_width] * num_cols)
                chunk_table.setStyle(TableStyle(table_style))
                chunk_table.hAlign = 'LEFT'
                chunk_table.keepWithNext = False
                chunk_table.splitByRow = True
                chunk_table.splitInRow = True
                chunk_table.repeatRows = 1 if has_header and i == 0 else 0  # Only repeat header on first chunk
                
                elements.append(Spacer(1, 6))
                elements.append(chunk_table)
                elements.append(Spacer(1, 6))
                
                logger.info(f"PDF Generator: Added table chunk {i//chunk_size + 1} with {len(chunk)} rows")
        else:
            elements.append(table)
            elements.append(Spacer(1, 6))  # Add a small spacer after the table
        
        logger.info(f"PDF Generator: Successfully added table with {len(table_rows)} rows")

    @staticmethod
    def _process_markdown_to_reportlab(
        markdown_content: str,
        font_type: str = DEFAULT_FONT_TYPE,
        font_size_body: int = DEFAULT_FONT_SIZE_BODY,
        font_size_header: int = DEFAULT_FONT_SIZE_HEADER,
        font_size_footer: int = DEFAULT_FONT_SIZE_FOOTER,
        line_spacing: float = DEFAULT_LINE_SPACING,
        is_toc: bool = False
    ) -> list:
        """Convert markdown content to ReportLab elements"""
        # Get ReportLab styles
        styles = getSampleStyleSheet()
        
        # Create custom styles with compliance formatting (no parent inheritance to avoid font size conflicts)
        title_style = ParagraphStyle(
            'CustomTitle',
            fontSize=font_size_header,
            spaceAfter=20,
            spaceBefore=20,
            textColor=colors.black,
            fontName=get_font_name(font_type, is_bold=True),
            leading=font_size_header * line_spacing,
            alignment=TA_LEFT,
            allowOrphans=0,
            wordWrap=1
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            fontSize=font_size_header,
            spaceAfter=12,
            spaceBefore=16,
            textColor=colors.black,
            fontName=get_font_name(font_type, is_bold=True),
            leading=font_size_header * line_spacing,
            alignment=TA_LEFT,
            allowOrphans=0,
            wordWrap=1
        )
        
        subheading_style = ParagraphStyle(
            'CustomSubHeading',
            fontSize=font_size_body,
            spaceAfter=8,
            spaceBefore=12,
            textColor=colors.black,
            fontName=get_font_name(font_type, is_bold=True),
            leading=font_size_body * line_spacing,
            alignment=TA_LEFT,
            allowOrphans=0,
            wordWrap=1
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            fontSize=font_size_body,
            spaceAfter=8,
            spaceBefore=8,
            alignment=TA_JUSTIFY,
            fontName=get_font_name(font_type, is_bold=False),
            leading=font_size_body * line_spacing,
            allowOrphans=0,
            wordWrap=1
        )
        
        bold_style = ParagraphStyle(
            'CustomBold',
            fontSize=font_size_body,
            spaceAfter=6,
            spaceBefore=6,
            fontName=get_font_name(font_type, is_bold=True),
            leading=font_size_body * line_spacing,
            alignment=TA_LEFT,
            allowOrphans=0,
            wordWrap=1
        )
        
        bullet_style = ParagraphStyle(
            'CustomBullet',
            fontSize=font_size_body,
            spaceAfter=4,
            spaceBefore=2,
            leftIndent=20,
            bulletIndent=10,
            fontName=get_font_name(font_type, is_bold=False),
            leading=font_size_body * line_spacing,
            alignment=TA_LEFT,
            allowOrphans=0,
            wordWrap=1
        )
        
        # TOC-specific styles with black text
        toc_title_style = ParagraphStyle(
            'TocTitle',
            fontSize=font_size_header,
            spaceAfter=20,
            spaceBefore=20,
            textColor=colors.black,
            fontName=get_font_name(font_type, is_bold=True),
            leading=font_size_header * line_spacing,
            alignment=TA_LEFT,
            allowOrphans=0,
            wordWrap=1
        )
        
        toc_heading_style = ParagraphStyle(
            'TocHeading',
            fontSize=font_size_header,
            spaceAfter=12,
            spaceBefore=16,
            textColor=colors.black,
            fontName=get_font_name(font_type, is_bold=True),
            leading=font_size_header * line_spacing,
            alignment=TA_LEFT,
            allowOrphans=0,
            wordWrap=1
        )
        
        toc_subheading_style = ParagraphStyle(
            'TocSubHeading',
            fontSize=font_size_body,
            spaceAfter=8,
            spaceBefore=12,
            textColor=colors.black,
            fontName=get_font_name(font_type, is_bold=True),
            leading=font_size_body * line_spacing,
            alignment=TA_LEFT,
            allowOrphans=0,
            wordWrap=1
        )

        # Split content into lines
        lines = markdown_content.split('\n')
        elements = []
        table_rows = []
        in_table = False
        
        logger.info(f"PDF Generator: Processing {len(lines)} lines of markdown content")
        
        i = 0
        max_iterations = len(lines) * 2  # Safety limit to prevent infinite loops
        iteration_count = 0
        while i < len(lines):
            iteration_count += 1
            if iteration_count > max_iterations:
                logger.error(f"PDF Generator: Infinite loop detected after {iteration_count} iterations, stopping processing")
                break
            line = lines[i].strip()
            
            if not line:
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"PDF Generator: Ending table with {len(table_rows)} rows")
                    PDFGenerator._add_table_to_elements(elements, table_rows)
                    table_rows = []
                    in_table = False
                # Don't add spacer - spacing is handled by spaceBefore/spaceAfter in styles
                i += 1
                continue
            
            # Process different markdown elements
            if line.startswith('# '):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"PDF Generator: Ending table with {len(table_rows)} rows due to title")
                    PDFGenerator._add_table_to_elements(elements, table_rows)
                    table_rows = []
                    in_table = False
                text = line[2:].strip()
                style = toc_title_style if is_toc else title_style
                elements.append(Paragraph(text, style))
            elif line.startswith('## '):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"PDF Generator: Ending table with {len(table_rows)} rows due to heading")
                    PDFGenerator._add_table_to_elements(elements, table_rows)
                    table_rows = []
                    in_table = False
                text = line[3:].strip()
                style = toc_heading_style if is_toc else heading_style
                elements.append(Paragraph(text, style))
            elif line.startswith('### '):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"PDF Generator: Ending table with {len(table_rows)} rows due to subheading")
                    PDFGenerator._add_table_to_elements(elements, table_rows)
                    table_rows = []
                    in_table = False
                text = line[4:].strip()
                style = toc_subheading_style if is_toc else subheading_style
                elements.append(Paragraph(text, style))
            elif line.startswith('**') and line.endswith('**') and len(line) > 4 and '**' not in line[2:-2]:
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"PDF Generator: Ending table with {len(table_rows)} rows due to bold text")
                    PDFGenerator._add_table_to_elements(elements, table_rows)
                    table_rows = []
                    in_table = False
                text = line[2:-2].strip()
                elements.append(Paragraph(text, bold_style))
            elif line.startswith('* '):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"PDF Generator: Ending table with {len(table_rows)} rows due to bullet point")
                    PDFGenerator._add_table_to_elements(elements, table_rows)
                    table_rows = []
                    in_table = False
                # Handle bullet points
                text = line[2:].strip()
                # Check if bullet point text is very long and split it if needed
                if len(text) > 600:  # If bullet text is very long, split it
                    logger.info(f"PDF Generator: Splitting long bullet point ({len(text)} characters)")
                    text_chunks = PDFGenerator._split_long_text(text, max_length=600)
                    for chunk_idx, chunk in enumerate(text_chunks):
                        # Process bold text within each chunk
                        processed_chunk = PDFGenerator._process_bold_text(chunk)
                        bullet_text = f"• {processed_chunk}" if chunk_idx == 0 else f"  {processed_chunk}"  # Only bullet on first line
                        elements.append(Paragraph(bullet_text, bullet_style))
                else:
                    # Process bold text within bullet points
                    processed_text = PDFGenerator._process_bold_text(text)
                    bullet_text = f"• {processed_text}"
                    elements.append(Paragraph(bullet_text, bullet_style))
            elif line.startswith('---'):
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"PDF Generator: Ending table with {len(table_rows)} rows due to horizontal rule")
                    PDFGenerator._add_table_to_elements(elements, table_rows)
                    table_rows = []
                    in_table = False
                # Don't add spacer - spacing is handled by spaceBefore/spaceAfter in styles
            elif line.strip() == '<PAGEBREAK>':
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"PDF Generator: Ending table with {len(table_rows)} rows due to page break")
                    PDFGenerator._add_table_to_elements(elements, table_rows)
                    table_rows = []
                    in_table = False
                # Add page break
                elements.append(PageBreak())
            elif line.startswith('| ') and '|' in line[2:]:
                # Handle table rows
                table_data = [cell.strip() for cell in line.split('|')[1:-1]]
                if table_data:
                    # Check if this is a separator row (contains only dashes, colons, or similar)
                    is_separator = True
                    for cell in table_data:
                        cell_content = cell.strip()
                        # Skip if cell contains non-separator characters
                        if cell_content and not all(c in ':-| ' for c in cell_content):
                            is_separator = False
                            break
                    
                    # Additional check for common separator patterns
                    if is_separator:
                        # Check for patterns like :------------------, |------------------, etc.
                        combined_content = ''.join(table_data)
                        if all(c in ':-| ' for c in combined_content) or '----' in combined_content:
                            is_separator = True
                        else:
                            is_separator = False
                    
                    if not is_separator:
                        logger.info(f"PDF Generator: Adding table row: {table_data}")
                        table_rows.append(table_data)
                        in_table = True
            elif line.startswith('|---') or line.startswith('| ---') or line.startswith('|:---') or line.startswith('| :---'):
                # Skip table separator lines but keep table context
                logger.info("PDF Generator: Skipping table separator line")
                pass
            elif in_table and '|' in line:
                # Handle table rows that don't start with | (incomplete tables)
                table_data = [cell.strip() for cell in line.split('|')]
                if len(table_data) > 1:  # At least 2 cells (empty first cell + content)
                    # Remove empty first and last cells if they exist
                    if table_data[0].strip() == '':
                        table_data = table_data[1:]
                    if table_data and table_data[-1].strip() == '':
                        table_data = table_data[:-1]
                    if table_data:
                        logger.info(f"PDF Generator: Adding incomplete table row: {table_data}")
                        table_rows.append(table_data)
            else:
                # End table if we were in one
                if in_table and table_rows:
                    logger.info(f"PDF Generator: Ending table with {len(table_rows)} rows due to regular text")
                    PDFGenerator._add_table_to_elements(elements, table_rows)
                    table_rows = []
                    in_table = False
                if line:  # Only add non-empty lines
                    # Check if the line is very long and split it if needed
                    if len(line) > 800:  # If line is very long, split it
                        logger.info(f"PDF Generator: Splitting long line ({len(line)} characters)")
                        text_chunks = PDFGenerator._split_long_text(line, max_length=800)
                        for chunk in text_chunks:
                            # Process bold text within each chunk
                            processed_chunk = PDFGenerator._process_bold_text(chunk)
                            elements.append(Paragraph(processed_chunk, normal_style))
                    else:
                        # Process bold text within the line
                        processed_line = PDFGenerator._process_bold_text(line)
                        elements.append(Paragraph(processed_line, normal_style))
            
            i += 1
        
        # Add any remaining table
        if in_table and table_rows:
            logger.info(f"PDF Generator: Adding final table with {len(table_rows)} rows")
            PDFGenerator._add_table_to_elements(elements, table_rows)
        
        logger.info(f"PDF Generator: Generated {len(elements)} ReportLab elements after processing {i} lines")
        return elements

    @staticmethod
    def _prepare_cover_page_text_data(
        tenant_details: Any,
        opportunity_details: Any,
        user_details: Any
    ) -> dict:
        """Prepare text data for cover page overlay"""
        text_data = {}
        
        # Opportunity title
        if opportunity_details and hasattr(opportunity_details, 'title') and opportunity_details.title:
            text_data['opportunity_title'] = opportunity_details.title
        else:
            text_data['opportunity_title'] = "Request for Proposal"
        
        # Agency name
        if opportunity_details and hasattr(opportunity_details, 'agency_name') and opportunity_details.agency_name:
            text_data['agency_name'] = opportunity_details.agency_name
        
        # Tenant name
        if tenant_details and hasattr(tenant_details, 'tenant_name') and tenant_details.tenant_name:
            text_data['tenant_name'] = tenant_details.tenant_name
        
        # Tenant address
        if tenant_details:
            address_parts = []
            if hasattr(tenant_details, 'tenant_primary_address_1') and tenant_details.tenant_primary_address_1:
                address_parts.append(tenant_details.tenant_primary_address_1)
            if hasattr(tenant_details, 'tenant_primary_address_2') and tenant_details.tenant_primary_address_2:
                address_parts.append(tenant_details.tenant_primary_address_2)
            if hasattr(tenant_details, 'tenant_primary_city') and tenant_details.tenant_primary_city:
                city_state_zip = tenant_details.tenant_primary_city
                if hasattr(tenant_details, 'tenant_primary_state') and tenant_details.tenant_primary_state:
                    city_state_zip += f", {tenant_details.tenant_primary_state}"
                if hasattr(tenant_details, 'tenant_primary_zipcode') and tenant_details.tenant_primary_zipcode:
                    city_state_zip += f" {tenant_details.tenant_primary_zipcode}"
                address_parts.append(city_state_zip)
            if hasattr(tenant_details, 'tenant_primary_country') and tenant_details.tenant_primary_country:
                address_parts.append(tenant_details.tenant_primary_country)
            
            if address_parts:
                text_data['tenant_address'] = "<br/>".join(address_parts)
        
        # Opportunity POC details
        if opportunity_details:
            poc_parts = []
            if hasattr(opportunity_details, 'point_of_contact_first_name') or hasattr(opportunity_details, 'point_of_contact_last_name'):
                first_name = getattr(opportunity_details, 'point_of_contact_first_name', '')
                last_name = getattr(opportunity_details, 'point_of_contact_last_name', '')
                full_name = f"{first_name} {last_name}".strip()
                if full_name:
                    poc_parts.append(full_name)
            if hasattr(opportunity_details, 'point_of_contact_email') and opportunity_details.point_of_contact_email:
                poc_parts.append(opportunity_details.point_of_contact_email)
            if hasattr(opportunity_details, 'point_of_contact_phone') and opportunity_details.point_of_contact_phone:
                poc_parts.append(opportunity_details.point_of_contact_phone)
            
            if poc_parts:
                text_data['opportunity_poc'] = "<br/>".join(poc_parts)
        
        # Contact information (user details)
        if user_details:
            contact_parts = []
            if hasattr(user_details, 'name') and user_details.name:
                contact_parts.append(user_details.name)
            if hasattr(user_details, 'email') and user_details.email:
                contact_parts.append(user_details.email)
            
            if contact_parts:
                text_data['contact_info'] = "<br/>".join(contact_parts)
        
        # Classification code
        if opportunity_details and hasattr(opportunity_details, 'classification_code') and opportunity_details.classification_code:
            text_data['classification_code'] = opportunity_details.classification_code
        
        # Opportunity ID
        if opportunity_details and hasattr(opportunity_details, 'opportunity_id') and opportunity_details.opportunity_id:
            text_data['opportunity_id'] = opportunity_details.opportunity_id
        
        # Date
        from datetime import datetime
        text_data['date'] = datetime.now().strftime("%B %d, %Y")
        
        return text_data

    @staticmethod
    def create_text_cover_page(
        tenant_details: Any,
        opportunity_details: Any,
        user_details: Any,
        compliance: Optional[dict] = None
    ) -> Optional[List]:
        """
        Create a text-based cover page with tenant and opportunity information.
        Returns list of ReportLab elements or None if creation fails.
        """
        try:
            elements = []
            
            # Use compliance settings if provided, otherwise use defaults
            if compliance:
                font_type = compliance.get('font_type', DEFAULT_FONT_TYPE)
                font_size_body = compliance.get('font_size_body', DEFAULT_FONT_SIZE_BODY)
                font_size_header = compliance.get('font_size_header', DEFAULT_FONT_SIZE_HEADER)
                font_size_footer = compliance.get('font_size_footer', DEFAULT_FONT_SIZE_FOOTER)
                line_spacing = compliance.get('line_spacing', DEFAULT_LINE_SPACING)
                logger.info(f"Using compliance settings for text cover page: font={font_type}, body_size={font_size_body}, header_size={font_size_header}")
            else:
                # Use default settings when no compliance is provided
                font_type = DEFAULT_FONT_TYPE
                font_size_body = DEFAULT_FONT_SIZE_BODY
                font_size_header = DEFAULT_FONT_SIZE_HEADER
                font_size_footer = DEFAULT_FONT_SIZE_FOOTER
                line_spacing = DEFAULT_LINE_SPACING
                logger.info("No compliance settings provided for text cover page, using defaults")
            
            # Create styles for the cover page
            title_style = ParagraphStyle(
                'CoverTitle',
                fontSize=font_size_header + 10,  # Larger than header for title
                spaceAfter=30,
                spaceBefore=100,  # Start from top of page
                textColor=colors.black,
                fontName=get_font_name(font_type, is_bold=True),
                leading=(font_size_header + 10) * line_spacing,
                alignment=TA_CENTER,
                allowOrphans=0,
                wordWrap=1
            )
            
            subtitle_style = ParagraphStyle(
                'CoverSubtitle',
                fontSize=font_size_header,
                spaceAfter=20,
                spaceBefore=20,
                textColor=colors.black,
                fontName=get_font_name(font_type, is_bold=True),
                leading=font_size_header * line_spacing,
                alignment=TA_CENTER,
                allowOrphans=0,
                wordWrap=1
            )
            
            info_style = ParagraphStyle(
                'CoverInfo',
                fontSize=font_size_body,
                spaceAfter=8,
                spaceBefore=8,
                textColor=colors.black,
                fontName=get_font_name(font_type, is_bold=True),
                leading=font_size_body * line_spacing,
                alignment=TA_CENTER,
                allowOrphans=0,
                wordWrap=1
            )
            
            # Build cover page content
            cover_content = []
            
            # Opportunity Title (main title)
            if opportunity_details and hasattr(opportunity_details, 'title') and opportunity_details.title:
                cover_content.append(Paragraph(opportunity_details.title, title_style))
            else:
                cover_content.append(Paragraph("Request for Proposal", title_style))
            
            # Add some space
            cover_content.append(Spacer(1, 20))
            
            # Agency Name
            if opportunity_details and hasattr(opportunity_details, 'agency_name') and opportunity_details.agency_name:
                cover_content.append(Paragraph(opportunity_details.agency_name, subtitle_style))
            
            # Add some space
            cover_content.append(Spacer(1, 40))
            
            # Submitted by section
            cover_content.append(Paragraph("Submitted by:", bold_style))
            cover_content.append(Spacer(1, 10))
            
            # Tenant Information Section
            if tenant_details:
                # Tenant Name
                if hasattr(tenant_details, 'tenant_name') and tenant_details.tenant_name:
                    cover_content.append(Paragraph(tenant_details.tenant_name, info_style))
                
                # Tenant Address
                address_parts = []
                if hasattr(tenant_details, 'tenant_primary_address_1') and tenant_details.tenant_primary_address_1:
                    address_parts.append(tenant_details.tenant_primary_address_1)
                if hasattr(tenant_details, 'tenant_primary_address_2') and tenant_details.tenant_primary_address_2:
                    address_parts.append(tenant_details.tenant_primary_address_2)
                if hasattr(tenant_details, 'tenant_primary_city') and tenant_details.tenant_primary_city:
                    city_state_zip = tenant_details.tenant_primary_city
                    if hasattr(tenant_details, 'tenant_primary_state') and tenant_details.tenant_primary_state:
                        city_state_zip += f", {tenant_details.tenant_primary_state}"
                    if hasattr(tenant_details, 'tenant_primary_zipcode') and tenant_details.tenant_primary_zipcode:
                        city_state_zip += f" {tenant_details.tenant_primary_zipcode}"
                    address_parts.append(city_state_zip)
                if hasattr(tenant_details, 'tenant_primary_country') and tenant_details.tenant_primary_country:
                    address_parts.append(tenant_details.tenant_primary_country)
                
                if address_parts:
                    address_text = "<br/>".join(address_parts)
                    cover_content.append(Paragraph(address_text, info_style))
            
            # Add some space
            cover_content.append(Spacer(1, 30))
            
            # Submitted to section
            cover_content.append(Paragraph("Submitted to:", bold_style))
            cover_content.append(Spacer(1, 10))
            
            # Opportunity POC Information
            if opportunity_details:
                poc_parts = []
                if hasattr(opportunity_details, 'point_of_contact_first_name') or hasattr(opportunity_details, 'point_of_contact_last_name'):
                    first_name = getattr(opportunity_details, 'point_of_contact_first_name', '')
                    last_name = getattr(opportunity_details, 'point_of_contact_last_name', '')
                    full_name = f"{first_name} {last_name}".strip()
                    if full_name:
                        poc_parts.append(full_name)
                if hasattr(opportunity_details, 'point_of_contact_email') and opportunity_details.point_of_contact_email:
                    poc_parts.append(opportunity_details.point_of_contact_email)
                if hasattr(opportunity_details, 'point_of_contact_phone') and opportunity_details.point_of_contact_phone:
                    poc_parts.append(opportunity_details.point_of_contact_phone)
                
                if poc_parts:
                    poc_text = "<br/>".join(poc_parts)
                    cover_content.append(Paragraph(poc_text, info_style))
            
            # Add some space
            cover_content.append(Spacer(1, 30))
            
            # Opportunity Classification Code
            if opportunity_details and hasattr(opportunity_details, 'classification_code') and opportunity_details.classification_code:
                cover_content.append(Paragraph(f"Classification Code: {opportunity_details.classification_code}", info_style))
            
            # Opportunity ID/Name
            if opportunity_details and hasattr(opportunity_details, 'opportunity_id') and opportunity_details.opportunity_id:
                cover_content.append(Paragraph(f"Opportunity ID: {opportunity_details.opportunity_id}", info_style))
            
            # Add some space
            cover_content.append(Spacer(1, 30))
            
            # Date
            from datetime import datetime
            current_date = datetime.now().strftime("%B %d, %Y")
            cover_content.append(Paragraph(current_date, info_style))
            
            # Add some space before disclaimer
            cover_content.append(Spacer(1, 40))
            
            # Disclaimer
            disclaimer_style = ParagraphStyle(
                'Disclaimer',
                fontSize=8,  # Small font size like in Java code
                spaceAfter=4,
                spaceBefore=4,
                textColor=colors.black,  # Use black for text-based cover pages to match other elements
                fontName=get_font_name(font_type, is_bold=True),
                leading=8,  # Line spacing like in Java code
                alignment=TA_CENTER,
                allowOrphans=0,
                wordWrap=1
            )
            
            disclaimer_text = "This proposal includes data that shall not be disclosed outside the Government and shall not be duplicated, used, or disclosed—in whole or in part—for any purpose other than to evaluate this proposal. If, however, a contract is awarded to this offeror as a result of—or in connection with—the submission of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent provided in the resulting contract. This restriction does not limit the Government's right to use information contained in this data if it is obtained from another source without restriction. The data subject to this restriction are contained in all sheets of this document."
            
            cover_content.append(Paragraph(disclaimer_text, disclaimer_style))
            
            elements.extend(cover_content)
            logger.info(f"Successfully created text-based cover page with {len(cover_content)} elements")
            
            return elements
            
        except Exception as e:
            logger.error(f"Error creating text cover page: {e}")
            return None
    @staticmethod
    def _create_cover_page_elements(
        cover_page: Optional[DataMetastore],
        tenant_details: Optional[AESTenant],
        opportunity_details: Optional[CustomOppsTable],
        user_details: Optional[Users],
        compliance: Optional[dict] = None,
        image_only: bool = False
    ) -> Optional[List]:
        """Helper method to create cover page elements"""
        try:
            if cover_page:
                # Create cover page with optional text overlay based on image_only parameter
                cover_page_elements = PDFGenerator.create_cover_page_image(
                    cover_page=cover_page,
                    include_text_overlay=not image_only,  # Only include text overlay if not image_only
                    tenant_details=tenant_details,
                    opportunity_details=opportunity_details,
                    user_details=user_details,
                    compliance=compliance
                )
                if cover_page_elements:
                    if image_only:
                        logger.info(f"Image-only cover page prepared: {len(cover_page_elements)} elements")
                    else:
                        logger.info(f"Cover page with text overlay prepared: {len(cover_page_elements)} elements")
                    return cover_page_elements
                else:
                    logger.warning("Failed to create cover page with image")
            
            # Fallback to text-based cover page only if not image_only
            if not image_only:
                cover_page_elements = PDFGenerator.create_text_cover_page(
                    tenant_details=tenant_details,
                    opportunity_details=opportunity_details,
                    user_details=user_details,
                    compliance=compliance
                )
                if cover_page_elements:
                    logger.info(f"Text-based cover page prepared: {len(cover_page_elements)} elements")
                    return cover_page_elements
                else:
                    logger.warning("Failed to create text-based cover page")
            else:
                logger.warning("Image-only mode requested but no valid image cover page found")
            
            return None
                
        except Exception as e:
            logger.error(f"Error creating cover page elements: {e}")
            return None


    @staticmethod
    def create_cover_page_elements(
        cover_page: Optional[DataMetastore],
        tenant_details: Optional[AESTenant],
        opportunity_details: Optional[CustomOppsTable],
        user_details: Optional[Users],
        compliance: Optional[dict] = None,
        image_only: bool = False
    ) -> Optional[List]:
        """
        Create cover page elements with optional image-only mode.
        
        Args:
            cover_page: The cover page document
            tenant_details: Tenant information
            opportunity_details: Opportunity information
            user_details: User information
            compliance: Optional compliance formatting settings
            image_only: If True, cover page will be image-only without text overlay
            
        Returns:
            List of ReportLab elements for the cover page or None if creation fails
        """
        return PDFGenerator._create_cover_page_elements(
            cover_page=cover_page,
            tenant_details=tenant_details,
            opportunity_details=opportunity_details,
            user_details=user_details,
            compliance=compliance,
            image_only=image_only
        )

    @staticmethod
    def create_cover_page_image(cover_page: DataMetastore, include_text_overlay: bool = False, 
                               tenant_details: Any = None, opportunity_details: Any = None, 
                               user_details: Any = None,compliance: Optional[dict] = None) -> Optional[List]:
        """
        Convert cover page document to ReportLab elements.
        Returns list of ReportLab elements or None if conversion fails.
        """
        try:
            if not cover_page.original_document:
                logger.warning("Cover page has no original document content")
                return None

            elements = []
            
            # Use compliance settings if provided, otherwise use defaults
            if compliance:
                margin_points = compliance.get('margin', DEFAULT_MARGIN)
                font_type = compliance.get('font_type', DEFAULT_FONT_TYPE)
                font_size_body = compliance.get('font_size_body', DEFAULT_FONT_SIZE_BODY)
                font_size_header = compliance.get('font_size_header', DEFAULT_FONT_SIZE_HEADER)
                font_size_footer = compliance.get('font_size_footer', DEFAULT_FONT_SIZE_FOOTER)
                line_spacing = compliance.get('line_spacing', DEFAULT_LINE_SPACING)
                logger.info(f"Using compliance settings for cover page: font={font_type}, body_size={font_size_body}, header_size={font_size_header}")
            else:
                # Use default settings when no compliance is provided
                margin_points = DEFAULT_MARGIN
                font_type = DEFAULT_FONT_TYPE
                font_size_body = DEFAULT_FONT_SIZE_BODY
                font_size_header = DEFAULT_FONT_SIZE_HEADER
                font_size_footer = DEFAULT_FONT_SIZE_FOOTER
                line_spacing = DEFAULT_LINE_SPACING
                logger.info("No compliance settings provided for cover page, using defaults")
            
            # Get content type to determine how to handle the document
            content_type = getattr(cover_page, 'original_document_content_type', 'application/octet-stream').lower()
            logger.info(f"Processing cover page with content type: {content_type}")
            
            # Handle different content types
            if 'image' in content_type or cover_page.original_document_file_name.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                # Handle image files
                try:
                    from io import BytesIO
                    from PIL import Image as PILImage
                    
                    # Create image from binary data
                    img_stream = BytesIO(cover_page.original_document)
                    pil_image = PILImage.open(img_stream)
                    
                    # Convert to RGB if necessary
                    if pil_image.mode in ('RGBA', 'LA', 'P'):
                        pil_image = pil_image.convert('RGB')
                    
                    # Get original dimensions
                    original_width, original_height = pil_image.size
                    logger.info(f"Original image size: {original_width} x {original_height}")
                    
                    # Calculate proper pixel dimensions for print quality
                    # For full page (8.5 x 11 inches) at 300 DPI
                    target_width_px = int(8.5 * 300)  # 8.5 inches * 300 DPI = 2550 pixels
                    target_height_px = int(11 * 300)  # 11 inches * 300 DPI = 3300 pixels
                    
                    # Resize image to print-quality resolution
                    pil_image = pil_image.resize((target_width_px, target_height_px), PILImage.Resampling.LANCZOS)
                    logger.info(f"Resized image to print quality: {target_width_px} x {target_height_px} pixels")
                    
                    # Save to temporary file for ReportLab
                    import tempfile
                    import os
                    
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                        pil_image.save(temp_file, format='PNG', optimize=False)
                        temp_file_path = temp_file.name
                    
                    # Create ReportLab Image element with proper sizing
                    img_element = Image(temp_file_path)
                    
                    # Create the full-page image flowable
                    text_data = None
                    if include_text_overlay and tenant_details and opportunity_details and user_details:
                        text_data = PDFGenerator._prepare_cover_page_text_data(
                            tenant_details, opportunity_details, user_details
                        )
                    
                    img_element = FullPageImageFlowable(temp_file_path, text_data, font_type=font_type, font_size_body=font_size_body, font_size_header=font_size_header, font_size_footer=font_size_footer, line_spacing=line_spacing, is_cover_page=True)
                    
                    logger.info(f"Created image element to cover entire page with {'text overlay' if text_data else 'no text'}")
                    
                    elements.append(img_element)
                    
                    # Don't clean up the temp file yet - it will be cleaned up after PDF generation
                    # Store the temp file path for later cleanup
                    if not hasattr(PDFGenerator, '_temp_files'):
                        PDFGenerator._temp_files = []
                    PDFGenerator._temp_files.append(temp_file_path)
                    
                    logger.info(f"Successfully created image cover page from: {cover_page.original_document_file_name}")
                    
                except ImportError:
                    logger.warning("PIL (Pillow) not available, skipping cover page")
                    return None
                except Exception as img_error:
                    logger.error(f"Error processing image cover page: {img_error}")
                    return None
                    
            elif 'pdf' in content_type or cover_page.original_document_file_name.lower().endswith('.pdf'):
                # Handle PDF files - extract first page as image
                try:
                    import tempfile
                    import os
                    from PyPDF2 import PdfReader
                    from PIL import Image as PILImage
                    import fitz  # PyMuPDF
                    
                    # Save PDF to temporary file
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                        temp_file.write(cover_page.original_document)
                        temp_pdf_path = temp_file.name
                    
                    # Extract first page as image using PyMuPDF
                    pdf_document = fitz.open(temp_pdf_path)
                    if len(pdf_document) > 0:
                        first_page = pdf_document[0]
                        # Render page as image with ultra-high resolution for best quality
                        mat = fitz.Matrix(8, 8)  # Ultra-high scale factor for best quality
                        pix = first_page.get_pixmap(matrix=mat)
                        
                        # Save as temporary image with maximum quality
                        with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as img_file:
                            pix.save(img_file.name)
                            img_path = img_file.name
                        
                        # Get the pixmap dimensions
                        original_width = pix.width
                        original_height = pix.height
                        logger.info(f"Extracted PDF page size: {original_width} x {original_height}")
                        
                        # The pixmap is already at high resolution (8x scale factor)
                        # We can use it directly since it's already optimized for print quality
                        
                        # Create ReportLab Image element with proper sizing
                        img_element = Image(img_path)
                        
                        # Create the full-page image flowable
                        text_data = None
                        if include_text_overlay and tenant_details and opportunity_details and user_details:
                            text_data = PDFGenerator._prepare_cover_page_text_data(
                                tenant_details, opportunity_details, user_details
                            )
                        
                        img_element = FullPageImageFlowable(img_path, text_data, font_type=font_type, font_size_body=font_size_body, font_size_header=font_size_header, font_size_footer=font_size_footer, line_spacing=line_spacing, is_cover_page=True)
                        
                        logger.info(f"Created PDF image element to cover entire page with {'text overlay' if text_data else 'no text'}")
                        
                        elements.append(img_element)
                        
                        # Don't clean up the temp files yet - they will be cleaned up after PDF generation
                        # Store the temp file paths for later cleanup
                        if not hasattr(PDFGenerator, '_temp_files'):
                            PDFGenerator._temp_files = []
                        PDFGenerator._temp_files.extend([temp_pdf_path, img_path])
                        
                        logger.info(f"Successfully extracted first page from PDF cover: {cover_page.original_document_file_name}")
                    else:
                        logger.warning("PDF has no pages, skipping cover page")
                        return None
                    
                    pdf_document.close()
                    
                except ImportError:
                    logger.warning("PyMuPDF not available, skipping cover page")
                    return None
                except Exception as pdf_error:
                    logger.error(f"Error processing PDF cover page: {pdf_error}")
                    return None
                
            else:
                # Unsupported content type, skip cover page
                logger.info(f"Unsupported content type, skipping cover page: {content_type}")
                return None
            
            return elements
            
        except Exception as e:
            logger.error(f"Error creating cover page elements: {e}")
            return None







    @staticmethod
    def convert_trailing_page_to_markdown(trailing_page: DataMetastore) -> Optional[str]:
        """
        Convert trailing page document to markdown format.
        Returns markdown string or None if conversion fails.
        """
        try:
            if not trailing_page.original_document:
                logger.warning("Trailing page has no original document content")
                return None

            # For now, we'll return a placeholder
            # In a real implementation, you would convert the binary data to text/markdown
            # based on the content type
            
            content_type = getattr(trailing_page, 'original_document_content_type', 'application/octet-stream')
            logger.info(f"Trailing page content type: {content_type}")
            
            # Return a placeholder markdown
            return f"## Trailing Page\n\nContent from: {trailing_page.original_document_file_name}\n\n*This is a placeholder for the trailing page content.*"
            
        except Exception as e:
            logger.error(f"Error converting trailing page to markdown: {e}")
            return None

    @staticmethod
    def _generate_toc_with_accurate_pages(
        toc_data: List[Dict[str, Any]], 
        markdown_content: str, 
        has_cover_page: bool,
        font_type: str,
        font_size_header: int,
        line_spacing: float,
        margin_points: float
    ) -> List:
        """Generate TOC with accurate page numbers using two-pass approach"""
        try:
            # Check if PyPDF2 is available for accurate page counting
            if not PYPDF2_AVAILABLE:
                logger.warning("PyPDF2 not available, using fallback TOC generation")
                return PDFGenerator._generate_toc_simple_fallback(toc_data, has_cover_page, font_type, font_size_header, line_spacing, margin_points)
            
            elements = []
            
            # Add TOC title
            toc_title_style = ParagraphStyle(
                'TocTitle',
                fontSize=font_size_header,
                spaceAfter=5,
                spaceBefore=20,
                textColor=colors.black,
                fontName=get_font_name(font_type, is_bold=True),
                leading=font_size_header * line_spacing,
                alignment=TA_LEFT,
                allowOrphans=0,
                wordWrap=1
            )
            elements.append(Paragraph("Table of Contents", toc_title_style))
            elements.append(Spacer(1, 5))
            
            # Calculate page width for dots
            available_width = PAGE_WIDTH_POINTS - (2 * margin_points)
            
            # First pass: Build the main content to get actual page numbers
            temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
            temp_file.close()
            
            try:
                # Create temporary document with same settings
                temp_doc = SimpleDocTemplate(
                    temp_file.name,
                    pagesize=letter,
                    rightMargin=margin_points,
                    leftMargin=margin_points,
                    topMargin=margin_points,
                    bottomMargin=margin_points
                )
                
                # Process markdown content for page calculation
                markdown_elements = PDFGenerator._process_markdown_to_reportlab(
                    markdown_content,
                    font_type=font_type,
                    font_size_body=12,
                    font_size_header=font_size_header,
                    font_size_footer=10,
                    line_spacing=line_spacing
                )
                
                # Build temporary document to get page numbers
                temp_doc.build(markdown_elements)
                
                # Read the temporary PDF to get page information
                reader = PdfReader(temp_file.name)
                total_pages = len(reader.pages)
                
                logger.info(f"Calculated {total_pages} pages for main content")
                
                # Calculate starting page for content (restart numbering at 1)
                content_start_page = 1  # Content always starts at page 1
                
                # Distribute TOC entries across the calculated pages
                if toc_data:
                    # Calculate pages per section for even distribution
                    pages_per_section = max(1, (total_pages - 1) // len(toc_data))
                    
                    for i, item in enumerate(toc_data):
                        number = item.get('number', '')
                        title = item.get('title', '')
                        full_title = f"{number} {title}"
                        
                        # Calculate page number based on content distribution
                        if i == 0:
                            page_number = content_start_page
                        else:
                            page_number = content_start_page + (i * pages_per_section)
                        
                        # Ensure page number doesn't exceed total pages
                        page_number = min(page_number, content_start_page + total_pages - 1)
                        
                        # Create TOC entry with calculated page number
                        toc_entry = TOCEntryFlowable(
                            full_title, page_number, 0,
                            font_type, font_size_header, line_spacing, available_width
                        )
                        elements.append(toc_entry)
                        logger.info(f"Added TOC entry: {full_title} at page {page_number}")
                        
                        # Add subsections if they exist
                        subsections = item.get('subsections', [])
                        for j, subsection in enumerate(subsections):
                            sub_number = subsection.get('number', '')
                            sub_title = subsection.get('title', '')
                            sub_full_title = f"{sub_number} {sub_title}"
                            
                            # Calculate subsection page number
                            sub_page_number = page_number + 1 + j
                            sub_page_number = min(sub_page_number, content_start_page + total_pages - 1)
                            
                            # Create TOC subsection entry
                            toc_sub_entry = TOCEntryFlowable(
                                sub_full_title, sub_page_number, 1,
                                font_type, font_size_header, line_spacing, available_width
                            )
                            elements.append(toc_sub_entry)
                            logger.info(f"Added TOC subsection: {sub_full_title} at page {sub_page_number}")
                        
                        elements.append(Spacer(1, 8))
                
            finally:
                # Clean up temporary file
                try:
                    if os.path.exists(temp_file.name):
                        os.unlink(temp_file.name)
                except Exception as cleanup_error:
                    logger.warning(f"Failed to clean up temporary file: {cleanup_error}")
            
            # Add page break after TOC to separate from main content
            elements.append(PageBreak())
            logger.info("Generated TOC with accurate page numbers using two-pass approach")
            return elements
            
        except Exception as e:
            logger.error(f"Error generating TOC with accurate pages: {e}")
            return PDFGenerator._generate_toc_simple_fallback(toc_data, has_cover_page, font_type, font_size_header, line_spacing, margin_points)

    @staticmethod
    def _generate_toc_simple_fallback(
        toc_data: List[Dict[str, Any]], 
        has_cover_page: bool,
        font_type: str,
        font_size_header: int,
        line_spacing: float,
        margin_points: float
    ) -> List:
        """Fallback TOC generation with simple page distribution"""
        try:
            elements = []
            
            # Add TOC title
            toc_title_style = ParagraphStyle(
                'TocTitle',
                fontSize=font_size_header,
                spaceAfter=10,
                spaceBefore=20,
                textColor=colors.black,
                fontName=get_font_name(font_type, is_bold=True),
                leading=font_size_header * line_spacing,
                alignment=TA_LEFT,
                allowOrphans=0,
                wordWrap=1
            )
            elements.append(Paragraph("Table of Contents", toc_title_style))
            elements.append(Spacer(1, 10))
            
            # Calculate page width for dots
            available_width = PAGE_WIDTH_POINTS - (2 * margin_points)
            
            # Calculate starting page for content (restart numbering at 1)
            content_start_page = 1  # Content always starts at page 1
            
            # Simple distribution: 2 pages per section
            for i, item in enumerate(toc_data):
                number = item.get('number', '')
                title = item.get('title', '')
                full_title = f"{number} {title}"
                
                # Calculate page number with simple distribution
                page_number = content_start_page + (i * 2)
                
                # Create TOC entry
                toc_entry = TOCEntryFlowable(
                    full_title, page_number, 0,
                    font_type, font_size_header, line_spacing, available_width
                )
                elements.append(toc_entry)
                logger.info(f"Added TOC entry (fallback): {full_title} at page {page_number}")
                
                # Add subsections if they exist
                subsections = item.get('subsections', [])
                for j, subsection in enumerate(subsections):
                    sub_number = subsection.get('number', '')
                    sub_title = subsection.get('title', '')
                    sub_full_title = f"{sub_number} {sub_title}"
                    
                    # Subsection on next page after main section
                    sub_page_number = page_number + 1
                    
                    # Create TOC subsection entry
                    toc_sub_entry = TOCEntryFlowable(
                        sub_full_title, sub_page_number, 1,
                        font_type, font_size_header, line_spacing, available_width
                    )
                    elements.append(toc_sub_entry)
                    logger.info(f"Added TOC subsection (fallback): {sub_full_title} at page {sub_page_number}")
                
                elements.append(Spacer(1, 8))
            
            # Add page break after TOC
            elements.append(PageBreak())
            logger.info("Generated TOC with fallback page numbers")
            return elements
            
        except Exception as e:
            logger.error(f"Error in fallback TOC generation: {e}")
            return []



    @staticmethod
    def generate_pdf(
        markdown_content: str,
        opportunity_id: str,
        tenant_id: str,
        cover_page_elements: Optional[List] = None,
        toc_data: Optional[List[Dict[str, Any]]] = None,
        trailing_page_markdown: Optional[str] = None,
        output_dir: str = "generated-pdfs",
        compliance: Optional[dict] = None,
        volume_number: int = 1,
        image_only: bool = False
    ) -> tuple[str, str]:
        """
        Generate PDF from markdown content with optional cover and trailing pages.
        
        Args:
            markdown_content: The main content to convert to PDF
            opportunity_id: The opportunity identifier
            tenant_id: The tenant identifier
            cover_page_elements: Optional cover page elements
            toc_data: Optional table of contents data
            trailing_page_markdown: Optional trailing page content
            output_dir: Directory to save the generated PDF
            compliance: Optional compliance formatting settings
            volume_number: Volume number (1-5) to determine header title
            image_only: If True, cover page will be image-only without text overlay
            
        Returns:
            tuple[str, str]: (file_path, success_message)
        """
        try:
            # Create directory if it doesn't exist
            dir_path = Path(output_dir)
            dir_path.mkdir(exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"RFP_Draft_{opportunity_id}_{tenant_id}_{timestamp}.pdf"
            file_path = dir_path / filename

            # Apply compliance formatting if provided, otherwise use defaults
            if compliance:
                # Use compliance settings directly without fallback to defaults
                margin_points = compliance.get('margin', DEFAULT_MARGIN)
                font_type = compliance.get('font_type', DEFAULT_FONT_TYPE)
                font_size_body = compliance.get('font_size_body', DEFAULT_FONT_SIZE_BODY)
                font_size_header = compliance.get('font_size_header', DEFAULT_FONT_SIZE_HEADER)
                font_size_footer = compliance.get('font_size_footer', DEFAULT_FONT_SIZE_FOOTER)
                line_spacing = compliance.get('line_spacing', DEFAULT_LINE_SPACING)
                
                logger.info(f"Using compliance formatting: margin={margin_points}pts, font={font_type}, body_size={font_size_body}, header_size={font_size_header}, footer_size={font_size_footer}, line_spacing={line_spacing}")
            else:
                # Use default formatting only when no compliance is provided
                margin_points = inch
                font_type = DEFAULT_FONT_TYPE
                font_size_body = DEFAULT_FONT_SIZE_BODY
                font_size_header = DEFAULT_FONT_SIZE_HEADER
                font_size_footer = DEFAULT_FONT_SIZE_FOOTER
                line_spacing = DEFAULT_LINE_SPACING
                logger.info("No compliance settings provided, using default formatting")
            
            # Build elements list
            elements = []
            
            # Handle cover page separately if provided
            has_cover_page = False
            if cover_page_elements:
                logger.info(f"Processing cover page with {len(cover_page_elements)} elements")
                has_cover_page = True
            else:
                logger.info("No cover page elements to add")

            # Create PDF document with compliance margins
            doc = SimpleDocTemplate(
                str(file_path),
                pagesize=letter,
                rightMargin=margin_points,
                leftMargin=margin_points,
                topMargin=margin_points,
                bottomMargin=margin_points
            )

            # Create header/footer template for regular pages
            header_footer_template = HeaderFooterTemplate(
                volume_number=volume_number,
                font_type=font_type,
                font_size_body=8,  # Set header font size to 8
                font_size_footer=font_size_footer
            )

            # Create page templates based on whether we have a cover page
            if has_cover_page and cover_page_elements:
                # Create a full-page frame for the cover page (no margins)
                cover_frame = Frame(
                    0, 0,  # x, y position
                    letter[0], letter[1],  # width, height (full page)
                    leftPadding=0,
                    bottomPadding=0,
                    rightPadding=0,
                    topPadding=0,
                    id='cover_frame'
                )
                
                # Create page template for cover page (no header/footer)
                cover_template = PageTemplate(
                    id='cover_template',
                    frames=[cover_frame]
                )
                
                # Create regular page template with adjusted margins for header/footer
                regular_frame = Frame(
                    margin_points, margin_points + 40,  # x, y position (account for header)
                    letter[0] - 2 * margin_points,  # width
                    letter[1] - 2 * margin_points - 80,  # height (account for header and footer)
                    leftPadding=0,
                    bottomPadding=0,
                    rightPadding=0,
                    topPadding=0,
                    id='regular_frame'
                )
                
                regular_template = PageTemplate(
                    id='regular_template',
                    frames=[regular_frame]
                )
                
                # Add templates to document (cover first, then regular)
                doc.addPageTemplates([cover_template, regular_template])
                
                logger.info("Created templates for cover page and regular pages")
            else:
                # Create regular page template for all pages
                regular_frame = Frame(
                    margin_points, margin_points + 40,  # x, y position (account for header)
                    letter[0] - 2 * margin_points,  # width
                    letter[1] - 2 * margin_points - 80,  # height (account for header and footer)
                    leftPadding=0,
                    bottomPadding=0,
                    rightPadding=0,
                    topPadding=0,
                    id='regular_frame'
                )
                
                regular_template = PageTemplate(
                    id='regular_template',
                    frames=[regular_frame]
                )
                
                # Add template to document
                doc.addPageTemplates([regular_template])
                
                logger.info("Created template for all pages")

            # Add cover page FIRST if provided
            if has_cover_page and cover_page_elements:
                logger.info(f"Adding {len(cover_page_elements)} cover page elements to PDF")
                elements.extend(cover_page_elements)
                logger.info("Cover page added")
            
            # Generate TOC with accurate page numbers if provided - BEFORE adding main content
            if toc_data:
                logger.info("Generating TOC with accurate page numbers")
                toc_elements = PDFGenerator._generate_toc_with_accurate_pages(
                    toc_data, markdown_content, has_cover_page,
                    font_type, font_size_header, line_spacing, margin_points
                )
                # Add TOC elements after cover page (if present) but before main content
                elements.extend(toc_elements)
                logger.info("TOC added after cover page")
            
            # Convert markdown to ReportLab elements with compliance formatting
            markdown_elements = PDFGenerator._process_markdown_to_reportlab(
                markdown_content,
                font_type=font_type,
                font_size_body=font_size_body,
                font_size_header=font_size_header,
                font_size_footer=font_size_footer,
                line_spacing=line_spacing
            )
            elements.extend(markdown_elements)
            logger.info("Main content added")
            
            # Add trailing page if provided
            if trailing_page_markdown:
                elements.append(PageBreak())
                trailing_elements = PDFGenerator._process_markdown_to_reportlab(
                    trailing_page_markdown,
                    font_type=font_type,
                    font_size_body=font_size_body,
                    font_size_header=font_size_header,
                    font_size_footer=font_size_footer,
                    line_spacing=line_spacing
                )
                elements.extend(trailing_elements)

            # Build PDF with header and footer callbacks
            if has_cover_page and cover_page_elements:
                # For documents with cover pages, skip header/footer on first page (cover) and TOC pages
                def custom_header_footer(canvas, doc):
                    # Skip headers/footers for cover page (page 1) and TOC pages
                    # TOC typically starts on page 2 if there's a cover page
                    if doc.page == 1:  # Cover page
                        return
                    if toc_data and doc.page == 2:  # TOC page (if present)
                        return
                    # Apply headers/footers to all other pages
                    header_footer_template(canvas, doc)
                
                doc.build(elements, onFirstPage=lambda canvas, doc: None, onLaterPages=custom_header_footer)
            else:
                # For documents without cover pages, skip TOC pages but add header/footer to others
                def custom_header_footer_no_cover(canvas, doc):
                    # Skip headers/footers for TOC page (page 1 if no cover)
                    if toc_data and doc.page == 1:  # TOC page
                        return
                    # Apply headers/footers to all other pages
                    header_footer_template(canvas, doc)
                
                doc.build(elements, onFirstPage=custom_header_footer_no_cover, onLaterPages=custom_header_footer_no_cover)

            logger.info(f"PDF file successfully saved to: {file_path.absolute()}")
            return str(file_path.absolute()), f"RFP Draft PDF successfully generated and saved to: {file_path.absolute()}"

        except Exception as e:
            logger.error(f"Error generating PDF: {e}")
            
            # Check if it's a layout issue and try to fix it
            if "too large" in str(e) or "Flowable" in str(e):
                logger.warning("Detected layout issue, attempting to fix with simplified formatting")
                try:
                    # Try again with simplified formatting
                    simplified_compliance = {
                        'font_size_body': 10,  # Smaller font
                        'font_size_header': 12,  # Smaller header
                        'line_spacing': 1.2,  # Tighter spacing
                        'margin': 30  # Smaller margins
                    }
                    
                    # Rebuild elements with simplified formatting
                    simplified_elements = []
                    
                    # Add cover page if present
                    if has_cover_page and cover_page_elements:
                        simplified_elements.extend(cover_page_elements)
                    
                    # Add TOC if present
                    if toc_data:
                        simplified_toc_elements = PDFGenerator._generate_toc_with_accurate_pages(
                            toc_data, markdown_content, has_cover_page,
                            font_type, 12, 1.2, 30  # Simplified settings
                        )
                        simplified_elements.extend(simplified_toc_elements)
                    
                    # Add main content with simplified formatting
                    simplified_markdown_elements = PDFGenerator._process_markdown_to_reportlab(
                        markdown_content,
                        font_type=font_type,
                        font_size_body=10,
                        font_size_header=12,
                        font_size_footer=8,
                        line_spacing=1.2
                    )
                    simplified_elements.extend(simplified_markdown_elements)
                    
                    # Create simplified document
                    simplified_doc = SimpleDocTemplate(
                        str(file_path),
                        pagesize=letter,
                        rightMargin=30,
                        leftMargin=30,
                        topMargin=30,
                        bottomMargin=30
                    )
                    
                    # Build with simplified elements
                    simplified_doc.build(simplified_elements)
                    
                    logger.info(f"PDF file successfully saved with simplified formatting: {file_path.absolute()}")
                    return str(file_path.absolute()), f"RFP Draft PDF successfully generated with simplified formatting: {file_path.absolute()}"
                    
                except Exception as simplified_error:
                    logger.error(f"Simplified formatting also failed: {simplified_error}")
            
            # Fallback: save as markdown if PDF generation fails
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                markdown_filename = f"RFP_Draft_{opportunity_id}_{tenant_id}_{timestamp}.md"
                markdown_file_path = dir_path / markdown_filename
                with open(markdown_file_path, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)
                logger.warning(f"PDF generation failed, saved as markdown: {markdown_file_path.absolute()}")
                return str(markdown_file_path.absolute()), f"PDF generation failed, saved as markdown: {markdown_file_path.absolute()}"
            except Exception as fallback_error:
                logger.error(f"Both PDF and markdown saving failed: {fallback_error}")
                raise Exception(f"Error saving file: {str(e)}")
        finally:
            # Clean up temporary files
            if hasattr(PDFGenerator, '_temp_files'):
                for temp_file in PDFGenerator._temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.unlink(temp_file)
                            logger.info(f"Cleaned up temporary file: {temp_file}")
                    except Exception as cleanup_error:
                        logger.warning(f"Failed to clean up temporary file {temp_file}: {cleanup_error}")
                PDFGenerator._temp_files = [] 

    @staticmethod
    def generate_pdf_with_image_only_cover(
        markdown_content: str,
        opportunity_id: str,
        tenant_id: str,
        cover_page: Optional[DataMetastore],
        tenant_details: Optional[AESTenant],
        opportunity_details: Optional[CustomOppsTable],
        user_details: Optional[Users],
        toc_data: Optional[List[Dict[str, Any]]] = None,
        trailing_page_markdown: Optional[str] = None,
        output_dir: str = "generated-pdfs",
        compliance: Optional[dict] = None,
        volume_number: int = 1
    ) -> tuple[str, str]:
        """
        Generate PDF with image-only cover page (no text overlay).
        
        Args:
            markdown_content: The main content to convert to PDF
            opportunity_id: The opportunity identifier
            tenant_id: The tenant identifier
            cover_page: The cover page document
            tenant_details: Tenant information
            opportunity_details: Opportunity information
            user_details: User information
            toc_data: Optional table of contents data
            trailing_page_markdown: Optional trailing page content
            output_dir: Directory to save the generated PDF
            compliance: Optional compliance formatting settings
            volume_number: Volume number (1-5) to determine header title
            
        Returns:
            tuple[str, str]: (file_path, success_message)
        """
        # Create image-only cover page elements
        cover_page_elements = PDFGenerator.create_cover_page_elements(
            cover_page=cover_page,
            tenant_details=tenant_details,
            opportunity_details=opportunity_details,
            user_details=user_details,
            compliance=compliance,
            image_only=True  # Force image-only mode
        )
        
        # Generate PDF with image-only cover page
        return PDFGenerator.generate_pdf(
            markdown_content=markdown_content,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            cover_page_elements=cover_page_elements,
            toc_data=toc_data,
            trailing_page_markdown=trailing_page_markdown,
            output_dir=output_dir,
            compliance=compliance,
            volume_number=volume_number,
            image_only=True  # Pass through to ensure image-only behavior
        )