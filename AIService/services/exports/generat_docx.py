from __future__ import annotations

import os
from pathlib import Path
from typing import Dict, List, Optional, Any

from io import BytesIO
from loguru import logger
import re

from models.customer_models import AESTenant, CustomOppsTable, DataMetastore, Users
from docx import Document

class DocxGenerator:
    """
    Generate DOCX files from rendered HTML using html2docx.

    Usage:
        html = "<h1>Title</h1><p>Hello</p>"
        output = DocxGenerator.generate_docx_from_html(html, "/tmp/output.docx")
    """
    @staticmethod
    def _create_cover_page_elements(
        doc: Optional[Document],
        cover_page: Optional[DataMetastore],
        tenant_details: Optional[AESTenant],
        opportunity_details: Optional[CustomOppsTable],
        user_details: Optional[Users],
        compliance: Optional[dict] = None,
        image_only: bool = False
    ) -> Optional[Document]:
        """
        Helper method to create cover page elements for DOCX generation.
        Mirrors the PDF generator's cover page creation logic.
        """
        try:
            from docx import Document
            from docx.shared import Pt, Inches
            from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
            from datetime import datetime

            # Set default compliance settings if not provided
            compliance = compliance or {}
            font_type = compliance.get('font_type', 'Times New Roman')
            font_size_body = compliance.get('font_size_body', 12)
            font_size_header = compliance.get('font_size_header', 14)

            # Image-based cover page
            if cover_page and not image_only:
                # Handle image-based cover page with optional text overlay
                cover_page_element = DocxGenerator._create_image_cover_page(
                    cover_doc=doc,
                    cover_page=cover_page,
                    include_text_overlay=True,  # Include text overlay for non-image-only mode
                    tenant_details=tenant_details,
                    opportunity_details=opportunity_details,
                    user_details=user_details,
                    compliance=compliance
                )
                if cover_page_element:
                    logger.info(f"Image-based cover page created with text overlay")
                    return cover_page_element
                else:
                    logger.warning("Failed to create image-based cover page, falling back to text-based")
            elif cover_page and image_only:
                # Handle image-only cover page (no text overlay)
                cover_page_element = DocxGenerator._create_image_cover_page(
                    cover_doc=doc,
                    cover_page=cover_page,
                    include_text_overlay=False,  # No text overlay for image-only mode
                    tenant_details=tenant_details,
                    opportunity_details=opportunity_details,
                    user_details=user_details,
                    compliance=compliance
                )
                if cover_page_element:
                    logger.info(cover_page_element)
                    logger.info(f"Image-only cover page created")
                    return cover_page_element
                else:
                    logger.warning("Failed to create image-only cover page")

            # Text-based cover page
            if not image_only:
                # Tenant/Organization Name
                if tenant_details and hasattr(tenant_details, 'tenant_name'):
                    para = doc.add_paragraph()
                    run = para.add_run(tenant_details.tenant_name)
                    run.font.name = font_type
                    run.font.size = Pt(font_size_header)
                    para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

                # Opportunity Details
                if opportunity_details:
                    # Opportunity Classification Code
                    if hasattr(opportunity_details, 'classification_code') and opportunity_details.classification_code:
                        para = doc.add_paragraph()
                        run = para.add_run(f"Classification Code: {opportunity_details.classification_code}")
                        run.font.name = font_type
                        run.font.size = Pt(font_size_body)
                        para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

                    # Opportunity ID/Name
                    if hasattr(opportunity_details, 'opportunity_id') and opportunity_details.opportunity_id:
                        para = doc.add_paragraph()
                        run = para.add_run(f"Opportunity ID: {opportunity_details.opportunity_id}")
                        run.font.name = font_type
                        run.font.size = Pt(font_size_body)
                        para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

                # Date
                para = doc.add_paragraph()
                run = para.add_run(datetime.now().strftime("%B %d, %Y"))
                run.font.name = font_type
                run.font.size = Pt(font_size_body)
                para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

                # Disclaimer
                disclaimer_text = (
                    "This proposal includes data that shall not be disclosed outside the Government and shall not be "
                    "duplicated, used, or disclosed—in whole or in part—for any purpose other than to evaluate this proposal. "
                    "If, however, a contract is awarded to this offeror as a result of—or in connection with—the submission "
                    "of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent "
                    "provided in the resulting contract. This restriction does not limit the Government's right to use "
                    "information contained in this data if it is obtained from another source without restriction. The data "
                    "subject to this restriction are contained in all sheets of this document."
                )
                para = doc.add_paragraph()
                run = para.add_run(disclaimer_text)
                run.font.name = font_type
                run.font.size = Pt(font_size_body - 2)  # Slightly smaller for disclaimer
                para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

                logger.info("Created text-based cover page elements for DOCX")
                return doc

            logger.warning("No cover page elements created")
            return None

        except Exception as e:
            logger.error(f"Error creating cover page elements for DOCX: {e}")
            return None

    @staticmethod
    def _create_image_cover_page(
        cover_doc: Optional[Document],
        cover_page: DataMetastore,
        include_text_overlay: bool = False,
        tenant_details: Optional[AESTenant] = None,
        opportunity_details: Optional[CustomOppsTable] = None,
        user_details: Optional[Users] = None,
        compliance: Optional[dict] = None
    ) -> Optional[Document]:
        """
        Create an image-based cover page for DOCX with optional text overlay.
        
        Args:
            cover_page: The cover page document containing the image
            include_text_overlay: Whether to include text overlay on the image
            tenant_details: Tenant information for text overlay
            opportunity_details: Opportunity information for text overlay
            user_details: User information for text overlay
            compliance: Optional compliance formatting settings
            
        Returns:
            List of Document objects representing the cover page or None if creation fails
        """
        try:
            if not cover_page.original_document:
                logger.warning("Cover page has no original document content")
                return None

            # Use compliance settings if provided, otherwise use defaults
            if compliance:
                font_type = compliance.get('font_type', 'Times New Roman')
                font_size_body = compliance.get('font_size_body', 12)
                font_size_header = compliance.get('font_size_header', 14)
                logger.info(f"Using compliance settings for image cover page: font={font_type}, body_size={font_size_body}, header_size={font_size_header}")
            else:
                font_type = 'Times New Roman'
                font_size_body = 12
                font_size_header = 14
                logger.info("No compliance settings provided for image cover page, using defaults")

            # Get content type to determine how to handle the document
            content_type = getattr(cover_page, 'original_document_content_type', 'application/octet-stream').lower()
            logger.info(f"Processing image cover page with content type: {content_type}")

            # Handle different content types
            if 'image' in content_type or cover_page.original_document_file_name.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                # Handle image files
                try:
                    from io import BytesIO
                    from PIL import Image as PILImage
                    import tempfile
                    import os

                    # Create image from binary data
                    img_stream = BytesIO(cover_page.original_document)
                    pil_image = PILImage.open(img_stream)

                    # Convert to RGB if necessary
                    if pil_image.mode in ('RGBA', 'LA', 'P'):
                        pil_image = pil_image.convert('RGB')

                    # Get original dimensions
                    original_width, original_height = pil_image.size
                    logger.info(f"Original image size: {original_width} x {original_height}")

                    # Calculate proper dimensions for Word document
                    # Standard letter size is 8.5 x 11 inches
                    # Convert to points (1 inch = 72 points)
                    page_width_pt = 8.5 * 72  # 612 points
                    page_height_pt = 11 * 72   # 792 points

                    # Calculate image dimensions to fit the page
                    # Maintain aspect ratio while fitting within page bounds
                    img_aspect = original_width / original_height
                    page_aspect = page_width_pt / page_height_pt

                    if img_aspect > page_aspect:
                        # Image is wider than page, fit to width
                        img_width_pt = page_width_pt
                        img_height_pt = page_width_pt / img_aspect
                    else:
                        # Image is taller than page, fit to height
                        img_height_pt = page_height_pt
                        img_width_pt = page_height_pt * img_aspect

                    # Resize image to appropriate resolution for Word
                    # Use 96 DPI (Word's default display resolution)
                    target_width_px = int(img_width_pt * 96 / 72)  # Convert points to pixels at 96 DPI
                    target_height_px = int(img_height_pt * 96 / 72)

                    pil_image = pil_image.resize((target_width_px, target_height_px), PILImage.Resampling.LANCZOS)
                    logger.info(f"Resized image for Word: {target_width_px} x {target_height_px} pixels")

                    # Save to temporary file for Word
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
                        pil_image.save(temp_file, format='PNG', optimize=False)
                        temp_file_path = temp_file.name

                    # Create a new document for the cover page
                    from docx import Document
                    from docx.shared import Pt
                    # cover_doc = Document()

                    # Add the image to the document
                    # Center the image on the page
                    from docx.shared import Inches
                    from docx.enum.text import WD_ALIGN_PARAGRAPH

                    # Calculate image dimensions in inches for Word
                    img_width_inches = img_width_pt / 72
                    img_height_inches = img_height_pt / 72

                    # Add image paragraph and center it
                    img_para = cover_doc.add_paragraph()
                    img_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                    # Add image to the paragraph
                    run = img_para.add_run()
                    run.add_picture(temp_file_path, width=Inches(img_width_inches), height=Inches(img_height_inches))

                    # Add text overlay if requested
                    if include_text_overlay and (tenant_details or opportunity_details or user_details):
                        text_data = DocxGenerator._prepare_cover_page_text_data(
                            tenant_details, opportunity_details, user_details
                        )
                        
                        # Add text overlay elements
                        if text_data.get('tenant_name'):
                            para = cover_doc.add_paragraph()
                            run = para.add_run(text_data['tenant_name'])
                            run.font.name = font_type
                            run.font.size = Pt(font_size_header)
                            para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        if text_data.get('opportunity_title'):
                            para = cover_doc.add_paragraph()
                            run = para.add_run(text_data['opportunity_title'])
                            run.font.name = font_type
                            run.font.size = Pt(font_size_header)
                            para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        if text_data.get('opportunity_id'):
                            para = cover_doc.add_paragraph()
                            run = para.add_run(f"Opportunity ID: {text_data['opportunity_id']}")
                            run.font.name = font_type
                            run.font.size = Pt(font_size_body)
                            para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        if text_data.get('classification_code'):
                            para = cover_doc.add_paragraph()
                            run = para.add_run(f"Classification Code: {text_data['classification_code']}")
                            run.font.name = font_type
                            run.font.size = Pt(font_size_body)
                            para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        if text_data.get('date'):
                            para = cover_doc.add_paragraph()
                            run = para.add_run(text_data['date'])
                            run.font.name = font_type
                            run.font.size = Pt(font_size_body)
                            para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                        # Add disclaimer if not image-only
                        disclaimer_text = (
                            "This proposal includes data that shall not be disclosed outside the Government and shall not be "
                            "duplicated, used, or disclosed—in whole or in part—for any purpose other than to evaluate this proposal. "
                            "If, however, a contract is awarded to this offeror as a result of—or in connection with—the submission "
                            "of this data, the Government shall have the right to duplicate, use, or disclose the data to the extent "
                            "provided in the resulting contract. This restriction does not limit the Government's right to use "
                            "information contained in this data if it is obtained from another source without restriction. The data "
                            "subject to this restriction are contained in all sheets of this document."
                        )
                        para = cover_doc.add_paragraph()
                        run = para.add_run(disclaimer_text)
                        run.font.name = font_type
                        run.font.size = Pt(font_size_body - 2)  # Slightly smaller for disclaimer
                        para.alignment = WD_ALIGN_PARAGRAPH.CENTER

                    # Clean up temporary file
                    try:
                        os.unlink(temp_file_path)
                    except Exception:
                        pass  # Ignore cleanup errors

                    logger.info(f"Successfully created image cover page from: {cover_page.original_document_file_name}")
                    return cover_doc

                except ImportError:
                    logger.warning("PIL (Pillow) not available, skipping image cover page")
                    return None
                except Exception as e:
                    logger.error(f"Error creating image cover page: {e}")
                    return None
            else:
                logger.warning(f"Unsupported content type for image cover page: {content_type}")
                return None

        except Exception as e:
            logger.error(f"Error in _create_image_cover_page: {e}")
            return None

    @staticmethod
    def _add_toc_entry_with_page_number(
        doc: Document, 
        entry_text: str, 
        page_number: int, 
        indent_level: int = 0,
        compliance: Optional[dict] = None
    ) -> None:
        """
        Add a TOC entry with uniform dotted lines and page numbers.
        Format: "2.2 Section................................7"
        """
        try:
            from docx.shared import Pt, Inches
            from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_TAB_ALIGNMENT, WD_TAB_LEADER
            
            # Get font settings from compliance
            font_type = compliance.get('font_type', 'Times New Roman') if compliance else 'Times New Roman'
            font_size_body = compliance.get('font_size_body', 12) if compliance else 12
            
            # Create paragraph for TOC entry
            p = doc.add_paragraph()
            
            # Set indentation based on level
            if indent_level > 0:
                p.paragraph_format.left_indent = Pt(20 * indent_level)  # 20pt per level for clear hierarchy
            
            # Configure tab stop with dot leaders
            # Position the tab stop to allow proper spacing for page numbers
            try:
                # Calculate tab position - leave enough space for page numbers (about 0.7 inches from right)
                section = doc.sections[0]
                page_width = section.page_width
                right_margin = section.right_margin
                tab_position = page_width - right_margin - Pt(50)  # 50pt (~0.7") from right edge
                
                # Add right-aligned tab stop with dot leader
                p.paragraph_format.tab_stops.add_tab_stop(tab_position, WD_TAB_ALIGNMENT.RIGHT, WD_TAB_LEADER.DOTS)
            except Exception:
                # Fallback: use standard position
                p.paragraph_format.tab_stops.add_tab_stop(Inches(6.5), WD_TAB_ALIGNMENT.RIGHT, WD_TAB_LEADER.DOTS)
            
            # Add text run with proper formatting
            run = p.add_run(entry_text)
            run.font.name = font_type
            run.font.size = Pt(font_size_body)
            
            # Add tab character (this will create the dotted line)
            tab_run = p.add_run('\t')
            
            # Add page number
            page_run = p.add_run(str(page_number))
            page_run.font.name = font_type
            page_run.font.size = Pt(font_size_body)
            
            # Set paragraph formatting
            p.alignment = WD_ALIGN_PARAGRAPH.LEFT
            p.paragraph_format.space_after = Pt(3)  # Small spacing between entries
            p.paragraph_format.space_before = Pt(0)
            
            # Ensure no hanging indents that could clip text
            p.paragraph_format.first_line_indent = Pt(0)
            p.paragraph_format.hanging_indent = Pt(0)
            
        except Exception as e:
            logger.warning(f"Error adding TOC entry with page number: {e}")
            # Fallback: simple entry without formatting
            p = doc.add_paragraph(f"{entry_text} ... {page_number}")

    @staticmethod
    def _prepare_cover_page_text_data(
        tenant_details: Any,
        opportunity_details: Any,
        user_details: Any
    ) -> dict:
        """
        Prepare text data for cover page overlay.
        Similar to PDF generator's _prepare_cover_page_text_data method.
        """
        text_data = {}
        
        # Opportunity title
        if opportunity_details and hasattr(opportunity_details, 'title') and opportunity_details.title:
            text_data['opportunity_title'] = opportunity_details.title
        else:
            text_data['opportunity_title'] = "Request for Proposal"
        
        # Agency name
        if opportunity_details and hasattr(opportunity_details, 'agency_name') and opportunity_details.agency_name:
            text_data['agency_name'] = opportunity_details.agency_name
        
        # Tenant name
        if tenant_details and hasattr(tenant_details, 'tenant_name') and tenant_details.tenant_name:
            text_data['tenant_name'] = tenant_details.tenant_name
        
        # Tenant address
        if tenant_details:
            address_parts = []
            if hasattr(tenant_details, 'tenant_primary_address_1') and tenant_details.tenant_primary_address_1:
                address_parts.append(tenant_details.tenant_primary_address_1)
            if hasattr(tenant_details, 'tenant_primary_address_2') and tenant_details.tenant_primary_address_2:
                address_parts.append(tenant_details.tenant_primary_address_2)
            if hasattr(tenant_details, 'tenant_primary_city') and tenant_details.tenant_primary_city:
                city_state_zip = tenant_details.tenant_primary_city
                if hasattr(tenant_details, 'tenant_primary_state') and tenant_details.tenant_primary_state:
                    city_state_zip += f", {tenant_details.tenant_primary_state}"
                if hasattr(tenant_details, 'tenant_primary_zipcode') and tenant_details.tenant_primary_zipcode:
                    city_state_zip += f" {tenant_details.tenant_primary_zipcode}"
                address_parts.append(city_state_zip)
            if hasattr(tenant_details, 'tenant_primary_country') and tenant_details.tenant_primary_country:
                address_parts.append(tenant_details.tenant_primary_country)
            
            if address_parts:
                text_data['tenant_address'] = "<br/>".join(address_parts)
        
        # Opportunity POC details
        if opportunity_details:
            poc_parts = []
            if hasattr(opportunity_details, 'point_of_contact_first_name') or hasattr(opportunity_details, 'point_of_contact_last_name'):
                first_name = getattr(opportunity_details, 'point_of_contact_first_name', '')
                last_name = getattr(opportunity_details, 'point_of_contact_last_name', '')
                full_name = f"{first_name} {last_name}".strip()
                if full_name:
                    poc_parts.append(full_name)
            if hasattr(opportunity_details, 'point_of_contact_email') and opportunity_details.point_of_contact_email:
                poc_parts.append(opportunity_details.point_of_contact_email)
            if hasattr(opportunity_details, 'point_of_contact_phone') and opportunity_details.point_of_contact_phone:
                poc_parts.append(opportunity_details.point_of_contact_phone)
            
            if poc_parts:
                text_data['opportunity_poc'] = "<br/>".join(poc_parts)
        
        # Contact information (user details)
        if user_details:
            contact_parts = []
            if hasattr(user_details, 'name') and user_details.name:
                contact_parts.append(user_details.name)
            if hasattr(user_details, 'email') and user_details.email:
                contact_parts.append(user_details.email)
            
            if contact_parts:
                text_data['contact_info'] = "<br/>".join(contact_parts)
        
        # Classification code
        if opportunity_details and hasattr(opportunity_details, 'classification_code') and opportunity_details.classification_code:
            text_data['classification_code'] = opportunity_details.classification_code
        
        # Opportunity ID
        if opportunity_details and hasattr(opportunity_details, 'opportunity_id') and opportunity_details.opportunity_id:
            text_data['opportunity_id'] = opportunity_details.opportunity_id
        
        # Date
        from datetime import datetime
        text_data['date'] = datetime.now().strftime("%B %d, %Y")
        
        return text_data

    @staticmethod
    def generate_docx_from_html(
        html_content: str,
        output_file_path: str | os.PathLike,
        metadata: Optional[Dict[str, str]] = None,  # kept for API compatibility; currently unused
        reference_docx_path: Optional[str | os.PathLike] = None,  # unused with html2docx
        css_paths: Optional[List[str]] = None,
        inline_css: bool = False,
        enforce_table_grid: bool = True,
        header_title: Optional[str] = None,
        include_toc: bool = True,
        toc_title: str = "Table of Contents",
        toc_levels: str = "1-3",
        toc_data: Optional[List[Dict]] = None,
        cover_page: DataMetastore = None,  # Cover page elements to add before main content
        cover_page_elements: Optional[Dict[str, Any]] = None,  # Dict with keys: cover_page, tenant_details, opportunity_details, user_details, compliance, image_only
    ) -> str:
        """
        Convert rendered HTML content to a DOCX file using html2docx.

        - html_content: HTML string to convert
        - output_file_path: destination .docx path
        - css_paths: optional list of CSS files to inline into HTML before conversion
        - inline_css: if True and css_paths provided/available, inline CSS into the HTML
        - enforce_table_grid: if True, post-process tables to ensure full grid borders
        - cover_page_elements: Optional cover page elements to add before main content

        Returns absolute output file path.
        """
        if not isinstance(html_content, str) or not html_content.strip():
            raise ValueError("html_content must be a non-empty string")

        # Prepare output path
        output_path = Path(output_file_path).expanduser().resolve()
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Prepare CSS
        resolved_css_paths: List[Path] = []
        if css_paths:
            resolved_css_paths = [Path(p).expanduser().resolve() for p in css_paths if p]
        else:
            default_css = Path(__file__).parent / "table.css"
            if default_css.exists():
                resolved_css_paths = [default_css.resolve()]

        effective_html = html_content
        if inline_css and resolved_css_paths:
            try:
                css_text = "\n".join(p.read_text(encoding="utf-8") for p in resolved_css_paths)
                style_block = f"<style>\n{css_text}\n</style>\n"
                if "</head>" in effective_html:
                    effective_html = effective_html.replace("</head>", style_block + "</head>", 1)
                else:
                    # No head present; to avoid CSS text appearing in the document, skip inlining
                    logger.info("Skipping CSS inlining: no <head> present in HTML")
                logger.info(f"Inlined CSS into HTML: {[str(p) for p in resolved_css_paths]}")
            except Exception as _e:
                logger.warning(f"Failed to inline CSS: {_e}")

        # Create document in correct order: Cover Page -> TOC -> Main Content
        try:
            from docx import Document
            from docx.shared import Pt
            from docx.enum.text import WD_BREAK, WD_ALIGN_PARAGRAPH, WD_TAB_ALIGNMENT, WD_TAB_LEADER
            from services.exports.create_reference_docx import set_table_borders
            from docx.oxml import OxmlElement
            from docx.oxml.ns import qn
            from html2docx import html2docx as _html2docx

            # Start with empty document
            doc = Document()
            has_cover_page = False

            # 1. Add cover page FIRST
            if cover_page:
                logger.info("Adding cover page at beginning")
                has_cover_page = True
                doc = DocxGenerator._create_cover_page_elements(
                    doc,
                    cover_page=cover_page_elements.get('cover_page'),
                    tenant_details=cover_page_elements.get('tenant_details'),
                    opportunity_details=cover_page_elements.get('opportunity_details'),
                    user_details=cover_page_elements.get('user_details'),
                    compliance=cover_page_elements.get('compliance'),
                    image_only=cover_page_elements.get('image_only'),
                )
                # Page break after cover
                para = doc.add_paragraph()
                run = para.add_run()
                run.add_break(WD_BREAK.PAGE)

            # 2. Add TOC SECOND with proper page numbers
            if include_toc and toc_data:
                logger.info("Adding TOC with page numbers")
                
                # Add TOC title
                toc_title_par = doc.add_paragraph(toc_title)
                toc_title_par.alignment = WD_ALIGN_PARAGRAPH.CENTER
                try:
                    toc_title_par.style = doc.styles['Heading 1']
                    # Style the title with larger font
                    for run in toc_title_par.runs:
                        run.font.size = Pt(16)
                        run.bold = True
                except:
                    pass
                
                # Add spacing after title
                toc_title_par.paragraph_format.space_after = Pt(12)
                
                content_start_page = 1  # Content always starts at page 1
                
                # Estimate pages per section for distribution
                # Simple estimation: distribute sections across estimated total pages
                total_sections = len(toc_data)
                pages_per_section = max(1, 8 // total_sections) if total_sections > 0 else 2  # Assume ~8 pages total
                
                current_page = content_start_page
                
                for i, item in enumerate(toc_data):
                    number = str(item.get('number', '') or '').strip()
                    title = str(item.get('title', '') or '').strip()
                    entry_text = f"{number} {title}".strip()
                    
                    # Add main TOC entry with page number and dot leaders
                    DocxGenerator._add_toc_entry_with_page_number(
                        doc, entry_text, current_page, 0, 
                        cover_page_elements.get('compliance') if cover_page_elements else None
                    )
                    
                    # Process subsections with indentation
                    subsections = item.get('subsections', [])
                    for j, sub in enumerate(subsections):
                        sub_number = str(sub.get('number', '') or '').strip()
                        sub_title = str(sub.get('title', '') or '').strip()
                        sub_entry_text = f"{sub_number} {sub_title}".strip()
                        
                        # Subsections get same page or next page
                        sub_page = current_page if j == 0 else current_page + 1
                        
                        DocxGenerator._add_toc_entry_with_page_number(
                            doc, sub_entry_text, sub_page, 1,  # indent_level = 1 for subsections
                            cover_page_elements.get('compliance') if cover_page_elements else None
                        )
                        
                        if j > 0:  # Increment page for subsequent subsections
                            current_page += 1
                    
                    # Move to next page for next main section
                    current_page += pages_per_section
                
                logger.info(f"Added {total_sections} TOC entries with page numbers")
                
                # Page break after TOC
                para = doc.add_paragraph()
                run = para.add_run()
                run.add_break(WD_BREAK.PAGE)

            # 3. Convert HTML and add main content LAST
            logger.info(f"Converting HTML content")
            doc_title = (metadata or {}).get('title') if isinstance(metadata, dict) else None
            if not doc_title:
                doc_title = 'RFP Draft'
            
            html_doc_bytes: BytesIO = _html2docx(effective_html, doc_title)
            html_doc = Document(html_doc_bytes)
            
            # Copy HTML content to main document
            for para in html_doc.paragraphs:
                new_para = doc.add_paragraph(para.text)
                new_para.style = para.style
            
            for table in html_doc.tables:
                doc._body._body.append(table._tbl)

        except Exception as exc:
            raise RuntimeError(f"Failed to generate DOCX: {exc}") from exc

        # Apply final formatting
        try:

            # Enforce full grid borders on tables and apply 'Table Grid' style if available
            for table in doc.tables:
                try:
                    table.style = 'Table Grid'
                except Exception:
                    pass
                try:
                    set_table_borders(table)
                except Exception:
                    pass

            # Parse font sizes from HTML (body and headings)
            body_pt = None
            h1_pt = h2_pt = h3_pt = None
            try:
                m_body = re.search(r"font-size\s*:\s*(\d+(?:\.\d+)?)pt", effective_html, flags=re.IGNORECASE)
                if m_body:
                    body_pt = float(m_body.group(1))
                m_h1 = re.search(r"<h1[^>]*style=\"[^\"]*font-size\s*:\s*(\d+(?:\.\d+)?)pt", effective_html, flags=re.IGNORECASE)
                if m_h1:
                    h1_pt = float(m_h1.group(1))
                m_h2 = re.search(r"<h2[^>]*style=\"[^\"]*font-size\s*:\s*(\d+(?:\.\d+)?)pt", effective_html, flags=re.IGNORECASE)
                if m_h2:
                    h2_pt = float(m_h2.group(1))
                m_h3 = re.search(r"<h3[^>]*style=\"[^\"]*font-size\s*:\s*(\d+(?:\.\d+)?)pt", effective_html, flags=re.IGNORECASE)
                if m_h3:
                    h3_pt = float(m_h3.group(1))
            except Exception:
                pass

            # Apply sizes to Word styles where available
            try:
                if body_pt:
                    doc.styles['Normal'].font.size = Pt(body_pt)
            except Exception:
                pass
            try:
                if h1_pt:
                    doc.styles['Heading 1'].font.size = Pt(h1_pt)
            except Exception:
                pass
            try:
                if h2_pt:
                    doc.styles['Heading 2'].font.size = Pt(h2_pt)
            except Exception:
                pass
            try:
                if h3_pt:
                    doc.styles['Heading 3'].font.size = Pt(h3_pt)
            except Exception:
                pass

            # Enforce justified alignment on body paragraphs but keep TOC lines (with tab stops) left aligned
            for para in doc.paragraphs:
                try:
                    ts = getattr(para.paragraph_format, 'tab_stops', None)
                    if ts and len(ts) > 0:
                        # likely a TOC line; skip changing alignment
                        continue
                    para.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
                except Exception:
                    continue

            # Add header (centered volume title) and footer (centered page number field) ONLY to sections after TOC
            # Cover page and TOC sections should not have headers/footers
            try:
                for idx, section in enumerate(doc.sections):
                    if idx == 0:
                        # First section: could be cover page + TOC or just TOC
                        if cover_page:
                            # Cover page + TOC section: ensure header/footer are empty
                            # Remove any existing header/footer paragraphs content
                            for p in list(section.header.paragraphs):
                                for r in list(p.runs):
                                    r.clear()
                            for p in list(section.footer.paragraphs):
                                for r in list(p.runs):
                                    r.clear()
                        else:
                            # Just TOC section: ensure header/footer are empty
                            for p in list(section.header.paragraphs):
                                for r in list(p.runs):
                                    r.clear()
                            for p in list(section.footer.paragraphs):
                                for r in list(p.runs):
                                    r.clear()
                        continue

                    # Content sections: add header/footer
                    header = section.header
                    header_par = header.paragraphs[0] if header.paragraphs else header.add_paragraph()
                    header_par.text = ''
                    header_run = header_par.add_run(header_title or '')
                    header_run.font.size = Pt(8)
                    header_par.alignment = WD_ALIGN_PARAGRAPH.CENTER

                    footer = section.footer
                    footer_par = footer.paragraphs[0] if footer.paragraphs else footer.add_paragraph()
                    footer_par.alignment = WD_ALIGN_PARAGRAPH.CENTER
                    # PAGE field
                    fld_begin = OxmlElement('w:fldChar'); fld_begin.set(qn('w:fldCharType'), 'begin')
                    instr = OxmlElement('w:instrText'); instr.set(qn('xml:space'), 'preserve'); instr.text = 'PAGE'
                    fld_end = OxmlElement('w:fldChar'); fld_end.set(qn('w:fldCharType'), 'end')
                    rr = footer_par.add_run()
                    rr._r.append(fld_begin); rr._r.append(instr); rr._r.append(fld_end)
            except Exception:
                pass

            doc.save(str(output_path))
            # Ensure fields (PAGEREF/TOC) are updated on open so page numbers appear
            try:
                settings_el = doc.settings.part.element
                # Remove existing updateFields to avoid duplicates
                for el in list(settings_el):
                    if el.tag == qn('w:updateFields'):
                        settings_el.remove(el)
                upd = OxmlElement('w:updateFields')
                upd.set(qn('w:val'), 'true')
                settings_el.append(upd)
                doc.save(str(output_path))
            except Exception:
                pass
        except Exception as _e:
            logger.warning(f"Post-processing failed (tables/fonts): {_e}")

        if not output_path.exists():
            raise RuntimeError("Failed to create the expected DOCX file")

        logger.info(f"DOCX generated: {output_path}")
        return str(output_path)


__all__ = ["DocxGenerator"]


