from docx import Document
from docx.oxml import OxmlElement
from docx.oxml.ns import qn


def set_table_borders(table) -> None:
    """
    Apply full grid borders to a python-docx table by editing the underlying XML.
    """
    tbl = table._tbl
    tblPr = tbl.get_or_add_tblPr()

    # Create table borders element
    borders = OxmlElement('w:tblBorders')

    for name in ('top', 'left', 'bottom', 'right', 'insideH', 'insideV'):
        border_el = OxmlElement(f'w:{name}')
        border_el.set(qn('w:val'), 'single')   # single line
        border_el.set(qn('w:sz'), '8')         # border size (8 = 1pt)
        border_el.set(qn('w:space'), '0')
        border_el.set(qn('w:color'), '000000') # black
        borders.append(border_el)

    # Append or replace existing tblBorders
    existing = tblPr.find(qn('w:tblBorders'))
    if existing is not None:
        tblPr.remove(existing)

    tblPr.append(borders)


def create_reference_docx(path: str = 'reference.docx') -> None:
    doc = Document()
    doc.add_heading('Reference: Table Style (Grid Lines)', level=1)
    doc.add_paragraph('This reference doc defines a table style with full grid lines.')

    # Insert a sample table
    table = doc.add_table(rows=4, cols=3)
    table.style = 'Table Grid'  # fallback; we'll force borders via XML anyway

    # Fill sample data
    headers = ['Column A', 'Column B', 'Column C']
    for i, th in enumerate(headers):
        table.cell(0, i).text = th

    for r in range(1, 4):
        for c in range(3):
            table.cell(r, c).text = f'R{r}C{c+1}'

    # Set full table borders
    set_table_borders(table)

    doc.save(path)
    print(f'Wrote reference docx: {path}')


__all__ = [
    'set_table_borders',
    'create_reference_docx',
]


