import re
from html import escape
from typing import List

from loguru import logger


# Constants (mirroring render_markdown.py)
DEFAULT_FONT_TYPE = 'Times-Roman'
DEFAULT_FONT_SIZE_BODY = 12
DEFAULT_FONT_SIZE_HEADER = 14
DEFAULT_FONT_SIZE_TITLE = 18
DEFAULT_FONT_SIZE_SUBHEADING = 12


class HtmlRenderer:
    """Utility class for rendering markdown-like content to HTML"""

    @staticmethod
    def _process_bold_text(text: str) -> str:
        """Convert **text** to <strong>text</strong>"""
        return re.sub(r"\*\*(.*?)\*\*", lambda m: f"<strong>{m.group(1)}</strong>", text)

    @staticmethod
    def _is_separator_row(row: List[str]) -> bool:
        """Detect markdown separator rows like | --- |:---:| --- |"""
        # A row is a separator if every non-empty cell consists only of '-', ':', '|', or spaces
        for cell in row:
            cell_text = cell.strip()
            if cell_text and not all(ch in ":-| " for ch in cell_text):
                return False
        # Extra guard: treat rows containing long dashes as separators
        if any("---" in cell for cell in row):
            return True
        # If all cells were empty it's not a separator
        return any(cell.strip() for cell in row)

    @staticmethod
    def _flush_table(rows: List[List[str]], has_header: bool) -> str:
        if not rows:
            return ""

        # Clean out separator rows to avoid rendering them as data rows
        cleaned_rows: List[List[str]] = []
        for row in rows:
            if HtmlRenderer._is_separator_row(row):
                logger.info(f"HTML Renderer: Skipping separator row: {row}")
                continue
            cleaned_rows.append(row)

        if not cleaned_rows:
            logger.warning("HTML Renderer: No valid table rows after cleaning")
            return ""

        thead_html = ""
        tbody_rows = cleaned_rows
        if has_header and len(cleaned_rows) >= 1:
            header_cells = ''.join(
                f"<th style=\"border:1pt solid #000;padding:6px 8px;text-align:justify;background:#d9d9d9;\">{HtmlRenderer._process_bold_text(cell)}</th>"
                for cell in cleaned_rows[0]
            )
            thead_html = f"<thead><tr>{header_cells}</tr></thead>"
            tbody_rows = cleaned_rows[1:]

        body_html_parts = []
        for row in tbody_rows:
            tds = ''.join(
                f"<td style=\"border:1pt solid #000;padding:6px 8px;vertical-align:top;word-wrap:break-word;white-space:normal;word-break:break-word;overflow-wrap:anywhere;text-align:justify;\">{HtmlRenderer._process_bold_text(cell)}</td>"
                for cell in row
            )
            body_html_parts.append(f"<tr>{tds}</tr>")
        tbody_html = f"<tbody>{''.join(body_html_parts)}</tbody>"

        table_html = (
            "<table style=\"border-collapse:collapse;border-spacing:0;width:100%;margin:0;table-layout:fixed;border:1pt solid #000;\">"
            f"{thead_html}{tbody_html}"
            "</table>"
        )
        logger.info(f"HTML Renderer: Rendered table with {len(cleaned_rows)} rows; has_header={has_header}")
        return table_html

    @staticmethod
    def render_markdown_to_html(
        markdown_content: str,
        font_type: str,
        font_size_body: int,
        font_size_header: int,
        font_size_title: int,
        font_size_subheading: int,
        line_spacing: float
    ) -> str:
        """
        Convert markdown-like content to HTML string.

        Mirrors the signature of render_markdown_to_reportlab and handles:
        - Headings (#, ##, ###)
        - Bold (**text**)
        - Bullet points (* item)
        - Horizontal rules (---)
        - Tables using pipe syntax
        - Paragraphs for regular text
        """
        try:
            lines = markdown_content.split('\n')
            logger.info(f"HTML Renderer: Processing {len(lines)} lines of markdown content")

            html_parts: List[str] = []
            table_rows: List[List[str]] = []
            in_table = False
            table_has_header = False
            in_list = False

            # Map ReportLab font names to CSS font-family fallbacks
            font_family = 'Times New Roman, Times, serif' if 'Times' in font_type else 'serif'

            def close_table_if_open():
                nonlocal table_rows, in_table, table_has_header
                if in_table and table_rows:
                    html_parts.append(HtmlRenderer._flush_table(table_rows, table_has_header))
                    table_rows = []
                in_table = False
                table_has_header = False

            def close_list_if_open():
                nonlocal in_list
                if in_list:
                    html_parts.append('</ul>')
                in_list = False

            # Opening wrapper with base typography
            html_parts.append(
                (
                    f"<div style=\"font-family:{escape(font_family)};"
                    f" font-size:{font_size_body}pt; line-height:{line_spacing}; color:#000; text-align:justify;\">"
                )
            )

            i = 0
            while i < len(lines):
                raw_line = lines[i]
                line = raw_line.strip()

                if not line:
                    close_table_if_open()
                    close_list_if_open()
                    i += 1
                    continue

                if line.startswith('# '):
                    close_table_if_open()
                    close_list_if_open()
                    text = HtmlRenderer._process_bold_text(line[2:].strip())
                    html_parts.append(
                        f"<h1 style=\"font-size:{font_size_title}pt;margin:0;color:#0b3d91;font-weight:700;text-align:justify;\">{text}</h1>"
                    )
                elif line.startswith('## '):
                    close_table_if_open()
                    close_list_if_open()
                    text = HtmlRenderer._process_bold_text(line[3:].strip())
                    html_parts.append(
                        f"<h2 style=\"font-size:{font_size_header}pt;margin:0;color:#2f4f4f;font-weight:700;text-align:justify;\">{text}</h2>"
                    )
                elif line.startswith('### '):
                    close_table_if_open()
                    close_list_if_open()
                    text = HtmlRenderer._process_bold_text(line[4:].strip())
                    html_parts.append(
                        f"<h3 style=\"font-size:{font_size_subheading}pt;margin:0;color:#2f4f4f;font-weight:700;text-align:justify;\">{text}</h3>"
                    )
                elif line.startswith('**') and line.endswith('**') and len(line) > 4 and '**' not in line[2:-2]:
                    close_table_if_open()
                    close_list_if_open()
                    text = HtmlRenderer._process_bold_text(line[2:-2].strip())
                    html_parts.append(f"<p style=\"margin:0;text-align:justify;\"><strong>{text}</strong></p>")
                elif line.startswith('* '):
                    close_table_if_open()
                    if not in_list:
                        html_parts.append('<ul style="margin:0 0 0 1.2em;padding:0;">')
                        in_list = True
                    item_text = HtmlRenderer._process_bold_text(line[2:].strip())
                    html_parts.append(f"<li style=\"margin:0;padding:0;text-align:justify;\">{item_text}</li>")
                elif line.startswith('---'):
                    close_table_if_open()
                    close_list_if_open()
                    html_parts.append('<hr style="margin:0;"/>')
                elif line.startswith('| ') and '|' in line[2:]:
                    # Table row
                    close_list_if_open()
                    table_data = [cell.strip() for cell in line.split('|')[1:-1]]
                    if table_data:
                        table_rows.append(table_data)
                        in_table = True
                        # Do not set header yet; wait for separator line if present
                elif re.match(r"^\s*\|?\s*:?\-\-\-+:?\s*(\|\s*:?\-\-\-+:?\s*)+\|?\s*$", line):
                    # Markdown table header separator line (e.g., |---|:---:|---|)
                    # Keep table open and mark header present
                    in_table = True
                    table_has_header = True
                elif in_table and '|' in line:
                    # Incomplete/continuation table row
                    close_list_if_open()
                    table_data = [cell.strip() for cell in line.split('|')]
                    if len(table_data) > 1:
                        if table_data[0] == '':
                            table_data = table_data[1:]
                        if table_data and table_data[-1] == '':
                            table_data = table_data[:-1]
                        if table_data:
                            table_rows.append(table_data)
                else:
                    close_table_if_open()
                    close_list_if_open()
                    processed_line = HtmlRenderer._process_bold_text(line)
                    html_parts.append(f"<p style=\"margin:0;text-align:justify;\">{processed_line}</p>")

                i += 1

            # Close any open structures
            if in_table and table_rows:
                html_parts.append(HtmlRenderer._flush_table(table_rows, table_has_header))
            if in_list:
                html_parts.append('</ul>')

            # Closing wrapper
            html_parts.append('</div>')

            html = '\n'.join(html_parts)
            logger.info(f"HTML Renderer: Generated HTML with {len(html)} characters")
            return html

        except Exception as e:
            logger.error(f"Error rendering markdown to HTML: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return f"<div><h1>Error Rendering HTML</h1><p>{escape(str(e))}</p></div>"


    @staticmethod
    def convert_draft_to_html(
        draft_list,
        opportunity_details=None,
        user_details=None,
        tenant_details=None,
        font_type: str = DEFAULT_FONT_TYPE,
        font_size_body: int = DEFAULT_FONT_SIZE_BODY,
        font_size_header: int = DEFAULT_FONT_SIZE_HEADER,
        font_size_title: int = DEFAULT_FONT_SIZE_TITLE,
        font_size_subheading: int = DEFAULT_FONT_SIZE_SUBHEADING,
        line_spacing: float = 1.5
    ) -> str:
        """
        Convenience method that mirrors MarkdownRenderer.convert_draft_to_markdown,
        then transforms the result into HTML using render_markdown_to_html.
        """
        try:
            from services.exports.render_markdown import MarkdownRenderer  # local import to avoid circular at module import time
            logger.info(f"HTML Renderer: Converting draft with {len(draft_list) if hasattr(draft_list, '__len__') else 'unknown'} sections to HTML")
            markdown_content = MarkdownRenderer.convert_draft_to_markdown(
                draft_list, opportunity_details, user_details, tenant_details
            )
            return HtmlRenderer.render_markdown_to_html(
                markdown_content,
                font_type=font_type,
                font_size_body=font_size_body,
                font_size_header=font_size_header,
                font_size_title=font_size_title,
                font_size_subheading=font_size_subheading,
                line_spacing=line_spacing,
            )
        except Exception as e:
            logger.error(f"Error converting draft to HTML: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return f"<div><h1>Error Converting Draft</h1><p>{escape(str(e))}</p></div>"


__all__ = ["HtmlRenderer"]


