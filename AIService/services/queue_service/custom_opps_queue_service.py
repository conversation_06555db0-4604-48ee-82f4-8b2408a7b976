from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import CustomOppsQueue as CustomerCustomOppsQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import or_


class CustomOppsQueueService:
    """Service for handling custom opportunities queue operations"""
    
    @staticmethod
    async def get_custom_opps_queue_items(db: AsyncSession, limit: int = 10) -> List[CustomerCustomOppsQueue]:
        """Get new custom opps queue items"""
        try:
            query = select(CustomerCustomOppsQueue).where(
                 or_(
                    CustomerCustomOppsQueue.status == "COMPLETED",
                    CustomerCustomOppsQueue.status == "AI_FAILED",
                    CustomerCustomOppsQueue.status == "FAILED"
                )
            ).order_by(CustomerCustomOppsQueue.created_date.desc()).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting new custom opps queue items: {e}")
            return []
        

    @staticmethod
    async def claim_new_queue_items(db: AsyncSession, limit: int = 10) -> List[CustomerCustomOppsQueue]:
        """
        Atomically claim up to `limit` NEW items and mark them as CLAIMED.
        Ensures multiple workers don’t process the same items.
        """
        try:
            # Build a CTE to select and lock the rows
            cte = (
                select(CustomerCustomOppsQueue)
                 .where(
                    or_(
                        CustomerCustomOppsQueue.status == "COMPLETED",
                        CustomerCustomOppsQueue.status == "AI_FAILED"
                    )
                )
                .order_by(CustomerCustomOppsQueue.created_date.desc())
                .limit(limit)
                .with_for_update(skip_locked=True)
            ).cte("to_claim")

            # Perform the update and return updated rows
            update_stmt = (
                update(CustomerCustomOppsQueue)
                .where(CustomerCustomOppsQueue.opps_id.in_(select(cte.c.opps_id)))
                .values(status="CLAIMED", update_date=datetime.utcnow())
                .returning(CustomerCustomOppsQueue)
            )

            result = await db.execute(update_stmt)
            await db.commit()

            claimed_items = result.scalars().all()
            logger.info(f"Claimed {len(claimed_items)} custom opps items for processing")
            return list(claimed_items)

        except Exception as e:
            logger.error(f"Error claiming custom opps queue items: {e}")
            await db.rollback()
            return []
    
    @staticmethod
    async def update_custom_opps_queue_status(
        db: AsyncSession, 
        opps_id: str, 
        status: str
    ) -> bool:
        """Update custom opps queue status"""
        try:
            query = update(CustomerCustomOppsQueue).where(
                CustomerCustomOppsQueue.opps_id == opps_id
            ).values(
                status=status,
                update_date=datetime.utcnow()
            )
            
            result = await db.execute(query)
            await db.commit()
            
            logger.info(f"Updated custom opps queue status for opps_id {opps_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating custom opps queue status: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def create_custom_opps_queue_item(
        db: AsyncSession,
        opps_source: str,
        opps_id: str,
        tenant_id: str,
        originating_ip_address: Optional[str] = None
    ) -> Optional[CustomerCustomOppsQueue]:
        """Create a new custom opps queue item"""
        try:
            new_item = CustomerCustomOppsQueue(
                opps_source=opps_source,
                opps_id=opps_id,
                tenant_id=tenant_id,
                status="NEW",
                originating_ip_address=originating_ip_address,
                created_date=datetime.utcnow()
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created new custom opps queue item for opps_id {opps_id}")
            return new_item
        except Exception as e:
            logger.error(f"Error creating custom opps queue item: {e}")
            await db.rollback()
            return None 
