import json
import re
from typing import Any, Dict, Optional

class ProposalUtilities:
    @staticmethod
    def is_rfp(type: str) -> bool:
        if not type:
            return False

        type = type.lower()
        if type == "presolicitation":
            return False
            
        keywords = ["solicitation", "request for proposals", "rfp", "rfq", "request for quotation"]
        if any(keyword in type for keyword in keywords):
            return True
            
        return False
    
    @staticmethod
    def get_volume_information(volume_number: int) -> str:

        if volume_number == 1:
            # Instructions for Technical Approach
            return """
            The Table of Contents for this Volume 1 of the proposal potentially contains any of the following sections
            shown below. Using the information found in <context>, determine the appropriate table of contents for Volume 1.
            DO NOT be restricted to the Potential Sections, IF the information found in <context> or <grading-criteria> asks
            for any SPECIFIC TASKS or INFORMATION to be included, ENSURE TO INCLUDE them.

            **Potential Sections:**

            Executive Summary:
            Purpose: Provide an overview of the proposal, highlighting win themes.
            Details: Summary of the technical approach, differentiators, and compliance with the RFP/RFI requirements.

            Understanding of the Government Agency's Requirements:
            Purpose: Show an in-depth understanding of the agency's needs.
            Details: Analyze the agency's pain points and project objectives with tailored solutions.

            Project Management Plan:
            Purpose: Describe strategies for effective project management.
            Details: Timelines, milestones, resource allocation, and risk management strategies.

            Staffing Plan:
            Purpose: Detail the team structure and staffing approach.
            Details: Roles, responsibilities, expertise of key team members, and staffing requirements.

            Quality Control & Quality Assurance Plans:
            Purpose: Explain measures for quality and regulatory compliance.
            Details: Quality control processes, assurance standards, and adherence to regulatory measures.

            Transition In/Out Plans:
            Purpose: Describe strategies for smooth transitions.
            Details: Transition timelines, contingency plans, and compliance considerations.

            Risk Mitigation Plan:
            Purpose: Identify potential risks and propose mitigation strategies.
            Details: Risk assessment methods, mitigation measures, and contingency planning.

            Task Order Management Plan:
            Purpose: Explain how individual task orders will be managed and executed.
            Details: Task order assignment, monitoring, and performance management.

            Technical Requirements:
            Purpose: Detail the technical solution.
            Details: Comprehensive technical approach, requirement mapping, compliance matrices, and technical visuals.

            Management & Others:
            Purpose: Address any additional sections required by the solicitation.
            Details: Include legal compliance statements or supplemental information.

            **Constraints:**
            - You DO NOT need to add every section specified here, only the ones deemed important using the information found in <context>.
            - The sections to be generated for this table of contents is NOT LIMITED TO the sections stated in Potential Sections.
            - DO NOT include or generate any section related to cost, pricing, finance or past performance.

            """

        elif volume_number == 2:
        # Instructions for Cost Proposal
            return """
            The Table of Contents for this Volume 2 of the proposal potentially contains any of the following sections
            shown below. Using the information found in <context>, determine the appropriate table of contents for Volume 1.
            DO NOT be restricted to the Potential Sections, IF the information found in <context> asks
            for any SPECIFIC TASKS or INFORMATION to be included, ENSURE TO INCLUDE them.

            1. **Cost & Pricing Metrics**: This section includes the following:
                - **Basis of Estimate (BOE)**: Clearly defined areas for labor estimates, material costs, indirect expenses, and the rationale for each pricing element. Detailed breakdown per CLIN/Task if applicable.
                - **Direct Labor Costs**: Detailed rates, hours, and labor categories.
                - **Material & Other Direct Costs (ODCs)**: Itemized list, justification, and pricing basis.
                - **Subcontractor Costs**: Detailed breakdown if applicable.
                - **Indirect Costs**: Overhead, G&A rates, and application base.
                - **Profit/Fee**: Proposed fee structure and calculation.
                - **Total Proposed Price Summary**: Clear summary table.
                - **Competitive Pricing Analysis**: (Optional internal reference or appendix) A section where users can compare their pricing proposal against historical data and competitors' pricing models.
                - **Pricing Models**: Placeholders for different pricing models (e.g., fixed-price, cost-reimbursable) with clear guidance on how to fill out each model according to RFP instructions.
            2. **Cost Justification & Price Reasonableness**: Narrative explaining the cost buildup and demonstrating price reasonableness.
            3. **Assumptions**: Any assumptions made during cost estimation.

            **Constraints:**
            - You DO NOT need to add every section specified here, only the ones deemed important using the information found in <context>.
            - The sections to be generated for this table of contents is NOT LIMITED TO the sections stated in Potential Sections.

            """

        elif volume_number == 3:
        # Instructions synthesized for Past Performance based on VolumeThree's context
            return """
            The Table of Contents for this Volume 3 of the proposal potentially contains any of the following sections
            shown below. Using the information found in <context>, determine the appropriate table of contents for Volume 1.
            DO NOT be restricted to the Potential Sections, IF the information found in <context> asks
            for any SPECIFIC TASKS or INFORMATION to be included, ENSURE TO INCLUDE them.

            1. **Introduction/Executive Summary**:
            Purpose: Briefly introduce the past performance volume and summarize the relevance and strength of the examples provided.
            Details: Highlight overall experience related to the RFP's scope, complexity, and size.

            2. **Past Performance Matrix**:
            Purpose: Provide a quick reference table summarizing key contract details for all references.
            Details: Columns typically include: Contract Name/Number, Customer Agency, Period of Performance, Total Contract Value, Brief Scope Summary, Relevance to Current RFP (Similarity in scope, size, complexity), Customer POC (if permitted).

            3. **Detailed Past Performance References (Short Forms - typically 3 to 5 examples)**:
            Purpose: Provide in-depth information for each key past performance example cited in the matrix.
            Details for each reference (structure based on common requirements and VolumeThree's `additionalInstrctions`):
                - **Contract Identification**:
                    - Name of Contracting Entity/Customer Agency
                    - Contract Number & Title
                    - Contract Type (FFP, T&M, Cost-Reimbursable, etc.)
                - **Contract Details**:
                    - Total Contract Value & Funded Value (if applicable)
                    - Period of Performance (Start/End Dates)
                - **Performance Description**:
                    - Detailed Description of Work/Services Performed (Focus on relevance to RFP requirements: scope, complexity/diversity of tasks, technical skills demonstrated)
                    - Primary Location(s) of Work
                    - Key Personnel Involved (Optional, if relevant and requested)
                    - Skills/Expertise Required and Demonstrated
                - **Performance Assessment**:
                    - Problems Encountered & Solutions Implemented/Corrective Actions Taken
                    - CPARS Ratings Summary (if available and permitted by RFP) or other performance assessments.
                - **Customer Point of Contact**:
                    - Customer POC Name, Title, Phone, Email (Verify if permission is needed/granted).
                - **Offeror Role**: (Prime, Subcontractor, JV Member)

            4. **Subcontractor Past Performance** (if applicable and required by RFP):
            Purpose: Detail the relevant past performance of key subcontractors.
            Details: Similar format to prime contractor references, focusing on the subcontractor's specific role and contributions on their relevant contracts.

            5. **Consent Forms** (if required by RFP):
            Purpose: Include signed forms authorizing the government to contact past performance references.

            6. **Additional Supporting Documentation** (Optional, if allowed):
            Purpose: Include letters of commendation, award fee scorecards, or other objective proof points.

            **Constraints:**
            - You DO NOT need to add every section specified here, only the ones deemed important using the information found in <context>.
            - The sections to be generated for this table of contents is NOT LIMITED TO the sections stated in Potential Sections.

            """

        elif volume_number == 4:
        # Instructions for Small Business Subcontracting Plan
            return """
            The Table of Contents for this Volume 4 of the proposal potentially contains any of the following sections
            shown below. Using the information found in <context>, determine the appropriate table of contents for Volume 1.
            DO NOT be restricted to the Potential Sections, IF the information found in <context> asks
            for any SPECIFIC TASKS or INFORMATION to be included, ENSURE TO INCLUDE them.

            1. **Identification Data**:
            - Offeror/Contractor Name, Address, DUNS/UEI
            - Solicitation/Contract Number
            - Item/Service Description
            - Place of Performance

            2. **Type of Plan**:
            - Individual Plan or Master Plan (Specify which)
            - Commercial Plan (if applicable)

            3. **Goals**: (Separate goals stated in terms of total planned subcontracting dollars and percentages of total planned subcontracting dollars)
            - Total Estimated Dollar Value of All Planned Subcontracting
            - a. Small Business (SB) Goal ($ and %)
            - b. Small Disadvantaged Business (SDB) Goal ($ and %) (subset of SB)
            - c. Women-Owned Small Business (WOSB) Goal ($ and %) (subset of SB)
            - d. Historically Underutilized Business Zone (HUBZone) Small Business Goal ($ and %) (subset of SB)
            - e. Veteran-Owned Small Business (VOSB) Goal ($ and %) (subset of SB)
            - f. Service-Disabled Veteran-Owned Small Business (SDVOSB) Goal ($ and %) (subset of VOSB)

            4. **Description of Products/Services to be Subcontracted**:
            - Principal types of supplies and services to be subcontracted.
            - Identification of types planned for subcontracting to SB, SDB, WOSB, HUBZone, VOSB, SDVOSB concerns.

            5. **Basis for Goal Establishment**:
            - Description of the method used to develop the subcontracting goals (e.g., based on historical data, market research, project requirements).

            6. **Method for Identifying Potential Sources**:
            - Description of the method used to identify potential small business sources (e.g., System for Award Management (SAM/DSBS), outreach events, trade associations, SBA resources).

            7. **Indirect Costs**:
            - Statement on whether indirect costs were included in establishing goals (and method if included).

            8. **Plan Administrator**:
            - Name, Title, Duties, and Contact Information of the official who will administer the subcontracting plan.

            9. **Equitable Opportunity Assurance**:
            - Description of efforts the offeror will take to ensure small businesses have an equitable opportunity to compete (e.g., outreach, counseling, breaking down requirements).

            10. **Flow-Down Clause Assurance**:
            - Assurance that the offeror will include the clause at FAR 52.219-8, "Utilization of Small Business Concerns," in all applicable subcontracts.
            - Assurance that the offeror will require subcontractors (except small businesses) receiving subcontracts over the simplified acquisition threshold ($750K generally, adjusted) to adopt a similar subcontracting plan.

            11. **Reporting and Cooperation Assurance**:
            - Assurance of cooperation in studies/surveys.
            - Assurance of submitting required reports (e.g., ISR, SSR in eSRS).
            - Assurance of ensuring subcontractors submit required reports.

            12. **Recordkeeping**:
            - Description of the types of records maintained to demonstrate compliance (e.g., source lists, solicitations, awards, outreach efforts).

            13. **Statutory Requirements Assurance**: (Good Faith Effort)
            - Assurance that the offeror will make a good faith effort to acquire articles, equipment, supplies, services, or materials, or obtain the performance of construction work from the small business concerns that it used in preparing the bid or proposal, in the same or greater scope, amount, and quality used in preparing the bid or proposal.
            - Assurance to provide SBA with written explanation if failing to meet the above.

            14. **Signatures**:
            - Signature of authorized official, Typed Name, Title, Date.

                **Constraints:**
            - You DO NOT need to add every section specified here, only the ones deemed important using the information found in <context>.
            - The sections to be generated for this table of contents is NOT LIMITED TO the sections stated in Potential Sections.
            - DO NOT include or generate any section related to cost, pricing, finance or past performance.

            """

        elif volume_number == 5:
        # Instructions for Certifications, Security, and Compliance
            return """
            The Table of Contents for this Volume 5 of the proposal potentially contains any of the following sections
            shown below. Using the information found in <context>, determine the appropriate table of contents for Volume 1.
            DO NOT be restricted to the Potential Sections, IF the information found in <context> asks
            for any SPECIFIC TASKS or INFORMATION to be included, ENSURE TO INCLUDE them.

            1. **Introduction and Compliance Overview**:
            - Purpose of this volume.
            - General statement affirming commitment to security and compliance as required by the RFP.

            2. **Security Certifications & Authorizations**: (Tailor based on RFP requirements)
            - FedRAMP Authorization (if applicable, specify level - High, Moderate, Low, Li-SaaS)
            - Cybersecurity Maturity Model Certification (CMMC) (if applicable, specify level)
            - ISO/IEC 27001 Certification (or statement of compliance)
            - NIST SP 800-171 Compliance Assessment (Score, SPRS submission date if applicable)
            - Other relevant security certifications (e.g., SOC 2 Type II)

            3. **Regulatory Compliance Statements**: (Tailor based on RFP requirements)
            - ITAR Compliance Statement (if applicable)
            - FISMA Compliance Statement (if applicable)
            - HIPAA Compliance Statement (if handling PHI)
            - Section 508 Compliance (VPAT - Voluntary Product Accessibility Template)
            - Data Privacy Compliance (e.g., GDPR, CCPA if applicable)
            - Other specific regulatory requirements mentioned in the RFP.

            4. **Organizational Security Controls Overview**: (Optional, unless requested)
            - Brief description of key security policies and procedures (e.g., access control, incident response, data encryption). Reference to SSP if applicable.

            5. **Personnel Security**: (If applicable and requested)
            - Statement regarding personnel security screening processes.
            - Information on facility security clearances (FCL) or personnel clearances (PCL) if required.

            6. **Third-Party Audit Reports or Assessments**: (If required)
            - Summary or attestation letters from relevant audits (e.g., FedRAMP 3PAO assessment, ISO audit).

            7. **Representations, Certifications, and Attestations**:
            - Signed copies of specific forms required by the RFP (e.g., FAR 52.204-24, 52.209-x, 52.212-3 if commercial).
            - Any custom attestations required by the solicitation regarding specific clauses or requirements.

            8. **Acknowledgement of Compliance with Specific Contract Clauses**:
            - Explicit statement acknowledging understanding and agreement to comply with key security, privacy, or compliance clauses cited in the RFP (list specific clause numbers if helpful).

            9. **Appendices** (if needed for lengthy documents like VPATs or Certifications)

            **Constraints:**
            - You DO NOT need to add every section specified here, only the ones deemed important using the information found in <context>.
            - The sections to be generated for this table of contents is NOT LIMITED TO the sections stated in Potential Sections.
            - DO NOT include or generate any section related to cost, pricing, finance or past performance.
        """

        else:
            raise ValueError(f"Unknown volume type: {volume_number}")

    @staticmethod
    def extract_json_from_backticks(text: str) -> Optional[Dict[str, Any]]:
        """
        Extracts the first JSON object found within ```json ... ``` backticks in the given text.
        Returns the parsed JSON as a Python dictionary, or None if not found or invalid.
        """
        pattern = r"```json\\s*(\\{.*?\\})\\s*```"
        match = re.search(pattern, text, re.DOTALL)
        if match:
            json_str = match.group(1)
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                return None
        return None

    @staticmethod
    def extract_json_from_markdown(text: str) -> Optional[Dict[str, Any]]:
        """
        Extracts JSON content from markdown code blocks (```json ... ```).
        """
        start_marker = "```json"
        end_marker = "```"

        start_index = text.find(start_marker)
        if start_index == -1:
            return None

        start_index += len(start_marker)
        end_index = text.find(end_marker, start_index)

        if end_index == -1:
            return None

        json_content = text[start_index:end_index].strip()

        try:
            return json.loads(json_content)
        except json.JSONDecodeError:
            return None

    @staticmethod
    def extract_json_from_brackets(
        text: str,
        bracket_type: str = "{}"
    ) -> Optional[Dict[str, Any]]:
        """
        Extracts the JSON object found between the first opening and the last closing bracket
        of the specified type in the given text.
        Returns the parsed JSON as a Python dictionary, or None if not found or invalid.
        This is robust for nested JSON objects.

        Args:
            text (str): The input text containing the JSON.
            bracket_type (str): The type of brackets to use. 
                - "{}" for curly braces (default, for JSON objects)
                - "[]" for square brackets (for JSON arrays)
                - "()" for parentheses (rare, but supported)
        """
        if bracket_type == "{}":
            open_bracket, close_bracket = "{", "}"
        elif bracket_type == "[]":
            open_bracket, close_bracket = "[", "]"
        elif bracket_type == "()":
            open_bracket, close_bracket = "(", ")"
        else:
            raise ValueError("Unsupported bracket_type. Use '{}', '[]', or '()'.")

        start = text.find(open_bracket)
        end = text.rfind(close_bracket)
        if start != -1 and end != -1 and end > start:
            json_str = text[start:end+1]
            print(f"Extracted JSON string: {json_str}")
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                return None
        return None

    @staticmethod
    def save_json_to_file(data: Dict[str, Any], file_path: str) -> None:
        """
        Saves a Python dictionary as JSON to the specified file path.
        """
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    @staticmethod
    def read_json_from_file(file_path: str) -> Dict[str, Any]:
        """
        Reads a JSON file and returns its contents as a Python dictionary.
        """
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)

    @staticmethod
    def save_text_to_file(text: str, file_path: str) -> None:
        """
        Saves a string to a text file at the specified file path.
        """
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(text)

    @staticmethod
    def read_text_from_file(file_path: str) -> str:
        """
        Reads the contents of a text file and returns it as a string.
        """
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read()
