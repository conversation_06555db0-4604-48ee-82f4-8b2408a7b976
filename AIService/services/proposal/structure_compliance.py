import asyncio
import json
from typing import Any, Dict, List
from services.chroma.chroma_service import ChromaService
from database import get_kontratar_db
from services.llm.llm_factory import get_llm
from loguru import logger
from services.proposal.compliance_schemas import (
    StructureComplianceSchema,
    ComplianceValidator
)


## I have a feeling content compliance already covers this
## But feel free to make use of it if you think it is necessary
class StructureComplianceService:
    """
    Service for generating structure compliance context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.chroma_service = ChromaService(embedding_api_url, None)
        # Use the LLM factory to get the configured LLM
        self.max_tokens = 3096
        self.llm = get_llm(
            temperature=0,
            num_ctx=6000,
            num_predict=self.max_tokens,
            base_url=llm_api_url
        )

    async def get_structure_compliance_context(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_chunks: int = 10,
    ) -> List[str]:
        """
        Query ChromaDB for relevant structure compliance context chunks.
        Cleans up newlines and tabs in the returned chunks.
        """

        chroma_query = '''
            Show the structure of the content expected to be seen in each volume or in the RFI, the approaches
            to be shown and page limits.
        '''
        
        async for db in get_kontratar_db():
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            # Clean up newlines and tabs
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            break
        
        return requirements_context

    async def generate_structure_compliance(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_tokens: int = 2048,
    ) -> Dict[str, Any]:
        """
        Generate structure compliance output using ChromaDB and LLM.
        Returns a dict with 'content' (LLM output).
        """
        logger.info(f"Starting generate_structure_compliance for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")

        # Get context
        chroma_query = '''
            Show the structure of the content expected to be seen in each volume or in the RFI, the approaches
            to be shown and page limits.
        '''

        async for db in get_kontratar_db():
            max_chunks = 5
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.debug(f"Querying ChromaDB with collection_name={collection_name}, chroma_query={chroma_query.strip()}, max_chunks={max_chunks}")
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            # Clean up newlines and tabs
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            compliance_context = "\n".join(requirements_context)
            logger.debug(f"Retrieved {len(requirements_context)} relevant context chunks for structure compliance.")
            break

        logger.info(f"Context for LLM:\n{compliance_context}")
        

        system_prompt = '''
            **Role:**
            You are a senior government proposal structure expert with 20+ years of experience in federal contracting and RFP/RFI response organization. You specialize in creating compliant proposal structures that meet exact government requirements and page limitations.

            **Critical Mission:**
            Analyze government solicitation requirements and create a precise proposal structure that ensures complete compliance with volume organization, content requirements, and page limitations. Incorrect structure could result in proposal rejection.

            **Structure Analysis Requirements:**
            1. **VOLUME IDENTIFICATION**: Identify all required volumes (Volume I-V for RFPs, single structure for RFIs) as specified in the solicitation
            2. **CONTENT ORGANIZATION**: For each volume, determine the required sections, content areas, and organizational structure
            3. **PAGE LIMIT COMPLIANCE**: CRITICAL - Ensure that individual section page limits DO NOT exceed the total volume page limit when combined
            4. **SECTION ALLOCATION**: Distribute page limits across sections proportionally while staying within total volume limits
            5. **GOVERNMENT STANDARDS**: Follow federal proposal formatting and organizational standards

            **Page Limit Calculation Rules:**
            - Each volume has a MAXIMUM total page limit that cannot be exceeded
            - Individual section page limits must be allocated within the total volume limit
            - If total volume limit is 10 pages, all sections combined cannot exceed 10 pages
            - Use proportional allocation based on content complexity and requirements
            - Default to smaller allocations (1-2 pages) unless specific requirements demand more

            **Output Standards:**
            - Generate valid, parseable JSON following the exact schema provided
            - Use ONLY information from the solicitation context
            - Ensure mathematical accuracy in page limit calculations
            - Maintain exact terminology from the solicitation
            - NO assumptions or generic content - extract only what is specified
        '''
        user_prompt = f'''
            <context>
                {compliance_context}
            </context>

            **STRUCTURE ANALYSIS DIRECTIVE:**
            Analyze the solicitation documentation in the context above and create a compliant proposal structure that meets all government requirements and page limitations.

            **STEP 1: EXTRACT PAGE LIMITS**
            First, carefully read the context to identify any volume page limits. Look for phrases like:
            - "Page limitations are as follows"
            - "VOLUME TITLE PAGE LIMITS"
            - "Technical Capability: 10"
            - "shall not exceed X pages"

            **STEP 2: CALCULATE SECTION ALLOCATIONS**
            For each volume with a specified page limit, distribute pages among sections so that:
            - Each section gets at least 1-2 pages
            - The total for all sections equals or is less than the volume limit
            - More complex sections get more pages, simpler sections get fewer pages

            **CRITICAL PAGE LIMIT REQUIREMENT:**
            When assigning page limits to sections within a volume, ensure that the sum of all section page limits DOES NOT EXCEED the total volume page limit.

            EXAMPLE: If Volume I has a 10-page limit, all sections within Volume I must total EXACTLY 10 pages or less.
            - Section 1: 3 pages
            - Section 2: 2 pages
            - Section 3: 2 pages
            - Section 4: 2 pages
            - Section 5: 1 page
            TOTAL: 10 pages ✓ (COMPLIANT)

            NEVER do this:
            - Section 1: 4 pages
            - Section 2: 4 pages
            - Section 3: 3 pages
            - Section 4: 3 pages
            - Section 5: 3 pages
            - Section 6: 3 pages
            TOTAL: 20 pages ✗ (EXCEEDS 10-page limit)

            **Required JSON Schema:**
            {{
              "structure": [
                {{
                  "volume_title": "string",
                  "total_page_limit": number (optional),
                  "content": [
                    {{
                      "section_name": "string",
                      "page_limit": number
                    }}
                  ]
                }}
              ]
            }}

            **Schema Field Definitions:**
            - "structure": Array of volumes required by the solicitation
              - For RFPs: Multiple volumes (Volume I, Volume II, etc.) as specified in solicitation
              - For RFIs: Single item with volume_title "RFI"
            - "volume_title": Exact volume name from solicitation (e.g., "Volume I - Technical Capability", "Volume II - Price")
            - "total_page_limit": Total page limit for the entire volume (if specified in solicitation)
            - "content": Array of required sections within each volume
            - "section_name": Specific content area or section title as required by solicitation
            - "page_limit": Pages allocated to each section (must be at least 1 and sum to ≤ total volume limit)
              * NEVER use 0 pages for any section
              * Use government proposal standards when limits not specified
              * Minimum 1 page per section, adjust based on content complexity

            **Page Allocation Guidelines:**
            - Extract total volume page limits from the solicitation context
            - If volume page limits are specified: Allocate section page limits proportionally within total volume limits
            - If volume page limits are NOT specified: Use reasonable government proposal standards:
              * Technical volumes: 15-25 pages total
              * Management volumes: 10-15 pages total
              * Past performance volumes: 10-20 pages total
              * Price volumes: 5-10 pages total
            - For individual sections when limits not specified:
              * Executive summary: 2-3 pages
              * Technical approach: 4-8 pages
              * Management approach: 3-6 pages
              * Past performance: 3-5 pages per project
              * Personnel qualifications: 2-4 pages per key person
              * Standard sections: 2-4 pages
            - Ensure mathematical accuracy: sum of section limits ≤ volume limit
            - Never assign 0 pages to any section

            **Output Requirements:**
            - Generate valid JSON only - no additional text or formatting
            - Use ONLY information explicitly stated in the context
            - Maintain exact volume titles and section names from solicitation
            - Verify page limit calculations before output

            **CRITICAL PAGE LIMIT REQUIREMENTS:**
            - NEVER assign 0 pages to any section
            - Every section must have a reasonable page allocation (minimum 1-2 pages)
            - MOST IMPORTANT: If volume page limits are specified in the context, the sum of all section page limits within that volume MUST NOT exceed the volume limit
            - When page limits are not specified in the solicitation, use professional judgment based on:
              * Government proposal standards and best practices
              * Content complexity and evaluation requirements
              * Typical industry standards for similar sections
            - Ensure all page allocations are realistic and achievable
            - ALWAYS verify your math: add up all section page limits to ensure they don't exceed the volume limit
        '''
        # Try LLM up to n_llm_attempts
        content = None
        n_llm_attempts = 3
        logger.info("Invoking LLM for structure compliance.")
        for attempt in range(n_llm_attempts):
            try:
                messages = [
                    ("system", system_prompt),
                    ("human", user_prompt)
                ]
                content = self.llm.invoke(messages)
                break
            except Exception as e:
                logger.error(f"LLM invocation failed for structure compliance (attempt {attempt + 1}): {e}")
                if attempt == n_llm_attempts - 1:
                    raise RuntimeError(f"LLM failed after {n_llm_attempts} attempts: {e}")
                await asyncio.sleep(1)

        if content is None:
            raise RuntimeError("LLM failed to generate content after all attempts")

        logger.info("LLM invocation successful for structure compliance.")
        logger.debug(f"LLM structure compliance output: {content.content[:500]}{'...' if len(content.content) > 500 else ''}")

        # Parse and validate JSON response
        try:
            from services.proposal.utilities import ProposalUtilities

            # Extract JSON from the response (try markdown first, then brackets)
            json_data = ProposalUtilities.extract_json_from_markdown(content.content)
            if not json_data:
                json_data = ProposalUtilities.extract_json_from_brackets(content.content)

            if json_data:
                # Validate using centralized validator
                validation_result = ComplianceValidator.validate_structure_compliance(json_data)

                if validation_result["is_valid"]:
                    logger.info("Successfully parsed and validated structure compliance JSON")
                    return {
                        "content": content.content,
                        "structured_data": validation_result["validated_data"],
                        "is_valid": True
                    }
                else:
                    logger.warning(f"JSON validation failed: {validation_result['errors']}")
                    # Return raw JSON if validation fails but JSON is parseable
                    return {
                        "content": content.content,
                        "structured_data": json_data,
                        "is_valid": False,
                        "validation_error": validation_result["errors"]
                    }
            else:
                logger.warning("Could not parse JSON from LLM response")
                return {
                    "content": content.content,
                    "is_valid": False,
                    "error": "Could not parse JSON from response"
                }

        except Exception as e:
            logger.error(f"Error processing structure compliance response: {e}")
            return {
                "content": content.content,
                "is_valid": False,
                "error": str(e)
            }