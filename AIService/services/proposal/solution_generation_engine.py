"""
Solution Generation Engine for Proposal Development

This engine generates unique, problem-specific solutions based on comprehensive
problem analysis rather than generic template responses. It creates targeted
solutions that directly address identified root causes and stakeholder needs.

Key Features:
1. Problem-Solution Mapping
2. Unique Solution Design
3. Multi-Perspective Solution Development
4. Solution Validation and Optimization
5. Innovation and Best Practice Integration
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from services.proposal.problem_analysis_framework import (
    ProblemAnalysisResult, ProblemStatement, SolutionRequirements, IndustryContext
)
from services.chat.web_search_service import WebSearchService
from services.research.semantic_scholar_service import SemanticScholarService
from llm_factory import get_llm

logger = logging.getLogger(__name__)


class SolutionApproach(Enum):
    """Different approaches to solution development"""
    INNOVATIVE = "innovative"
    PROVEN = "proven"
    HYBRID = "hybrid"
    TRANSFORMATIONAL = "transformational"
    INCREMENTAL = "incremental"


class SolutionComplexity(Enum):
    """Complexity levels of solutions"""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    ENTERPRISE = "enterprise"


@dataclass
class SolutionComponent:
    """Individual component of a solution"""
    component_id: str
    name: str
    description: str
    purpose: str
    addresses_problems: List[str]  # Problem IDs this component addresses
    technical_details: List[str]
    implementation_steps: List[str]
    benefits: List[str]
    risks: List[str]
    dependencies: List[str]
    success_criteria: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class UniqueSolution:
    """Comprehensive unique solution for identified problems"""
    solution_id: str
    title: str
    executive_summary: str
    approach: SolutionApproach
    complexity: SolutionComplexity
    target_problems: List[str]  # Problem IDs this solution addresses
    solution_components: List[SolutionComponent]
    innovation_elements: List[str]
    competitive_advantages: List[str]
    implementation_strategy: str
    timeline_phases: List[Dict[str, Any]]
    resource_requirements: Dict[str, Any]
    risk_mitigation: List[str]
    success_metrics: List[str]
    roi_justification: str
    stakeholder_benefits: Dict[str, List[str]]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class SolutionGenerationResult:
    """Result of solution generation process"""
    opportunity_id: str
    tenant_id: str
    generation_timestamp: str
    unique_solutions: List[UniqueSolution]
    solution_rationale: str
    innovation_score: float
    feasibility_score: float
    alignment_score: float
    overall_quality_score: float
    generation_methodology: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class SolutionGenerationEngine:
    """
    Advanced engine for generating unique, problem-specific solutions that go beyond
    generic responses to create innovative, targeted approaches to organizational challenges.
    """
    
    def __init__(
        self,
        llm_api_url: str = "http://ai.kontratar.com:11434"
    ):
        self.web_search_service = WebSearchService()
        self.semantic_scholar_service = SemanticScholarService()
        self.llm = get_llm(
            temperature=0.3,  # More creative for solution generation
            num_ctx=8000,
            base_url=llm_api_url
        )
        
        logger.info("SolutionGenerationEngine: Initialized with enhanced creativity settings")
    
    async def generate_unique_solutions(
        self,
        problem_analysis: ProblemAnalysisResult,
        max_solutions: int = 3
    ) -> SolutionGenerationResult:
        """
        Generate unique, problem-specific solutions based on comprehensive problem analysis.
        
        Args:
            problem_analysis: Comprehensive problem analysis result
            max_solutions: Maximum number of unique solutions to generate
            
        Returns:
            SolutionGenerationResult with unique, targeted solutions
        """
        logger.info(f"SOLUTION_GEN: Starting unique solution generation for {problem_analysis.opportunity_id}")
        
        try:
            # Step 1: Research innovative solutions and best practices
            innovation_research = await self._research_innovative_solutions(problem_analysis)
            
            # Step 2: Generate multiple solution approaches
            solution_approaches = await self._generate_solution_approaches(
                problem_analysis, innovation_research, max_solutions
            )
            
            # Step 3: Develop detailed solutions for each approach
            unique_solutions = []
            for i, approach in enumerate(solution_approaches):
                solution = await self._develop_detailed_solution(
                    problem_analysis, approach, innovation_research, i + 1
                )
                unique_solutions.append(solution)
            
            # Step 4: Calculate quality scores
            innovation_score = self._calculate_innovation_score(unique_solutions, innovation_research)
            feasibility_score = self._calculate_feasibility_score(unique_solutions, problem_analysis)
            alignment_score = self._calculate_alignment_score(unique_solutions, problem_analysis)
            overall_quality_score = (innovation_score + feasibility_score + alignment_score) / 3
            
            # Step 5: Generate solution rationale
            solution_rationale = await self._generate_solution_rationale(
                problem_analysis, unique_solutions, innovation_research
            )
            
            result = SolutionGenerationResult(
                opportunity_id=problem_analysis.opportunity_id,
                tenant_id=problem_analysis.tenant_id,
                generation_timestamp=problem_analysis.analysis_timestamp,
                unique_solutions=unique_solutions,
                solution_rationale=solution_rationale,
                innovation_score=innovation_score,
                feasibility_score=feasibility_score,
                alignment_score=alignment_score,
                overall_quality_score=overall_quality_score,
                generation_methodology=[
                    "Problem-Solution Mapping",
                    "Innovation Research",
                    "Multi-Approach Generation",
                    "Detailed Solution Development",
                    "Quality Assessment"
                ]
            )
            
            logger.info(f"SOLUTION_GEN: Generated {len(unique_solutions)} unique solutions with quality score {overall_quality_score:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"SOLUTION_GEN: Solution generation failed: {e}")
            raise Exception(f"Solution generation failed: {e}")
    
    async def _research_innovative_solutions(
        self,
        problem_analysis: ProblemAnalysisResult
    ) -> Dict[str, Any]:
        """Research innovative solutions and best practices for the identified problems."""
        logger.info("SOLUTION_GEN: Researching innovative solutions")
        
        research_results = {
            "innovative_approaches": [],
            "best_practices": [],
            "case_studies": [],
            "emerging_technologies": [],
            "academic_insights": []
        }
        
        try:
            # Extract key problem areas for research
            problem_keywords = []
            for problem in problem_analysis.problems_identified:
                problem_keywords.extend(self._extract_solution_keywords(problem.description))
            
            # Research innovative solutions via web search
            if self.web_search_service.is_available():
                for keyword in problem_keywords[:3]:  # Limit to top 3 keywords
                    search_query = f"""
                    INNOVATIVE SOLUTION RESEARCH: Find cutting-edge, innovative solutions and best practices for {keyword} challenges.
                    
                    Focus on:
                    1. Latest innovative approaches and technologies
                    2. Successful case studies and implementations
                    3. Emerging trends and future solutions
                    4. Best practices from leading organizations
                    5. Unique methodologies and frameworks
                    6. Competitive advantages and differentiators
                    
                    Look for solutions that go beyond traditional approaches and offer unique value.
                    """
                    
                    web_insights = []
                    async for chunk in self.web_search_service.search_and_stream(
                        query=search_query,
                        context_chunks=None,
                        conversation_history=None
                    ):
                        if chunk:
                            web_insights.append(chunk)
                    
                    research_results["innovative_approaches"].extend(web_insights)
            
            # Research academic solutions
            for keyword in problem_keywords[:2]:  # Limit academic research
                academic_research = await self.semantic_scholar_service.search_relevant_research(
                    topic=f"{keyword} innovative solutions",
                    limit=3
                )
                research_results["academic_insights"].append(academic_research)
            
            logger.info("SOLUTION_GEN: Innovation research completed")
            return research_results
            
        except Exception as e:
            logger.error(f"SOLUTION_GEN: Innovation research failed: {e}")
            return research_results
    
    def _extract_solution_keywords(self, problem_description: str) -> List[str]:
        """Extract keywords relevant to solution research."""
        desc_lower = problem_description.lower()
        
        # Solution-oriented keywords
        solution_keywords = []
        
        if "security" in desc_lower or "cyber" in desc_lower:
            solution_keywords.extend(["cybersecurity solutions", "security automation", "threat detection"])
        if "modernization" in desc_lower or "legacy" in desc_lower:
            solution_keywords.extend(["digital transformation", "system modernization", "cloud migration"])
        if "efficiency" in desc_lower or "process" in desc_lower:
            solution_keywords.extend(["process automation", "workflow optimization", "efficiency solutions"])
        if "data" in desc_lower or "analytics" in desc_lower:
            solution_keywords.extend(["data analytics", "business intelligence", "data management"])
        if "infrastructure" in desc_lower:
            solution_keywords.extend(["infrastructure modernization", "cloud infrastructure", "scalable architecture"])
        
        # Default keywords if none found
        if not solution_keywords:
            solution_keywords = ["innovative solutions", "best practices", "modern approaches"]
        
        return solution_keywords[:5]

    async def _generate_solution_approaches(
        self,
        problem_analysis: ProblemAnalysisResult,
        innovation_research: Dict[str, Any],
        max_solutions: int
    ) -> List[Dict[str, Any]]:
        """Generate multiple unique solution approaches."""
        logger.info(f"SOLUTION_GEN: Generating {max_solutions} solution approaches")

        try:
            system_prompt = """You are an Innovation Solution Architect specializing in creating unique, problem-specific solutions.

Your task is to generate multiple distinct solution approaches that directly address the identified problems with innovative, targeted solutions.

Each approach should be:
1. Unique and differentiated from others
2. Directly mapped to specific problems
3. Innovative yet feasible
4. Aligned with stakeholder needs
5. Informed by industry best practices
6. Technically sound and implementable

Generate approaches that vary in:
- Innovation level (proven vs cutting-edge)
- Implementation complexity (simple vs comprehensive)
- Technology focus (traditional vs emerging)
- Timeline (quick wins vs long-term transformation)
- Risk profile (conservative vs aggressive)

Return JSON array of solution approaches with:
- approach_name: Descriptive name
- approach_type: innovative/proven/hybrid/transformational/incremental
- complexity: simple/moderate/complex/enterprise
- key_innovations: List of innovative elements
- target_problems: Which problems this approach addresses
- core_concept: Central idea of the solution
- differentiators: What makes this approach unique
- implementation_philosophy: Overall approach to implementation"""

            # Prepare context
            problems_summary = []
            for problem in problem_analysis.problems_identified:
                problems_summary.append({
                    "id": problem.problem_id,
                    "title": problem.title,
                    "description": problem.description,
                    "category": problem.category.value,
                    "severity": problem.severity.value
                })

            innovation_summary = {
                "innovative_approaches": innovation_research.get("innovative_approaches", [])[:3],
                "best_practices": innovation_research.get("best_practices", [])[:3],
                "emerging_technologies": innovation_research.get("emerging_technologies", [])[:3]
            }

            user_prompt = f"""
            Organization: {problem_analysis.organization_name}

            Identified Problems:
            {json.dumps(problems_summary, indent=2)}

            Solution Requirements:
            {json.dumps(problem_analysis.solution_requirements.to_dict(), indent=2)}

            Innovation Research:
            {json.dumps(innovation_summary, indent=2)}

            Generate {max_solutions} distinct solution approaches that address these problems with unique, innovative solutions.
            Each approach should offer a different perspective and methodology.

            Return JSON array of solution approaches as specified.
            """

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await self.llm.ainvoke(messages)

            try:
                approaches = json.loads(response.content)
                if isinstance(approaches, list):
                    logger.info(f"SOLUTION_GEN: Generated {len(approaches)} solution approaches")
                    return approaches
                else:
                    logger.warning("LLM response not a list, creating fallback approaches")
                    return self._create_fallback_approaches(max_solutions)
            except json.JSONDecodeError:
                logger.warning("Failed to parse approaches JSON, creating fallback")
                return self._create_fallback_approaches(max_solutions)

        except Exception as e:
            logger.error(f"SOLUTION_GEN: Approach generation failed: {e}")
            return self._create_fallback_approaches(max_solutions)

    def _create_fallback_approaches(self, max_solutions: int) -> List[Dict[str, Any]]:
        """Create fallback solution approaches."""
        approaches = [
            {
                "approach_name": "Comprehensive Modernization Solution",
                "approach_type": "transformational",
                "complexity": "complex",
                "key_innovations": ["Integrated platform approach", "Automated workflows"],
                "target_problems": ["All identified problems"],
                "core_concept": "End-to-end modernization with integrated solutions",
                "differentiators": ["Holistic approach", "Future-ready architecture"],
                "implementation_philosophy": "Phased transformation with continuous improvement"
            },
            {
                "approach_name": "Targeted Quick-Win Solution",
                "approach_type": "incremental",
                "complexity": "moderate",
                "key_innovations": ["Rapid deployment", "Minimal disruption"],
                "target_problems": ["High-priority problems"],
                "core_concept": "Fast implementation of proven solutions",
                "differentiators": ["Speed to value", "Low risk"],
                "implementation_philosophy": "Agile implementation with immediate benefits"
            },
            {
                "approach_name": "Innovation-Driven Solution",
                "approach_type": "innovative",
                "complexity": "complex",
                "key_innovations": ["Cutting-edge technology", "Novel approaches"],
                "target_problems": ["Complex systemic problems"],
                "core_concept": "Leverage emerging technologies for breakthrough solutions",
                "differentiators": ["Technology leadership", "Competitive advantage"],
                "implementation_philosophy": "Innovation-first with calculated risks"
            }
        ]

        return approaches[:max_solutions]

    async def _develop_detailed_solution(
        self,
        problem_analysis: ProblemAnalysisResult,
        approach: Dict[str, Any],
        innovation_research: Dict[str, Any],
        solution_number: int
    ) -> UniqueSolution:
        """Develop a detailed unique solution based on the approach."""
        logger.info(f"SOLUTION_GEN: Developing detailed solution {solution_number}")

        try:
            # Generate solution components
            components = await self._generate_solution_components(
                problem_analysis, approach, innovation_research
            )

            # Generate implementation strategy
            implementation_strategy = await self._generate_implementation_strategy(
                problem_analysis, approach, components
            )

            # Generate timeline phases
            timeline_phases = await self._generate_timeline_phases(approach, components)

            # Create unique solution
            solution = UniqueSolution(
                solution_id=f"SOL_{solution_number:03d}",
                title=approach.get("approach_name", f"Solution {solution_number}"),
                executive_summary=await self._generate_executive_summary(
                    problem_analysis, approach, components
                ),
                approach=SolutionApproach(approach.get("approach_type", "hybrid")),
                complexity=SolutionComplexity(approach.get("complexity", "moderate")),
                target_problems=[p.problem_id for p in problem_analysis.problems_identified],
                solution_components=components,
                innovation_elements=approach.get("key_innovations", []),
                competitive_advantages=approach.get("differentiators", []),
                implementation_strategy=implementation_strategy,
                timeline_phases=timeline_phases,
                resource_requirements=self._estimate_resource_requirements(approach, components),
                risk_mitigation=self._identify_risk_mitigation(approach, components),
                success_metrics=self._define_solution_success_metrics(problem_analysis, approach),
                roi_justification=await self._generate_roi_justification(
                    problem_analysis, approach, components
                ),
                stakeholder_benefits=self._map_stakeholder_benefits(
                    problem_analysis, approach, components
                )
            )

            logger.info(f"SOLUTION_GEN: Detailed solution {solution_number} developed")
            return solution

        except Exception as e:
            logger.error(f"SOLUTION_GEN: Detailed solution development failed: {e}")
            # Return minimal solution
            return UniqueSolution(
                solution_id=f"SOL_{solution_number:03d}",
                title=approach.get("approach_name", f"Solution {solution_number}"),
                executive_summary="Comprehensive solution addressing identified organizational challenges",
                approach=SolutionApproach.HYBRID,
                complexity=SolutionComplexity.MODERATE,
                target_problems=[p.problem_id for p in problem_analysis.problems_identified],
                solution_components=[],
                innovation_elements=["Modern approach", "Best practices"],
                competitive_advantages=["Proven methodology", "Tailored solution"],
                implementation_strategy="Phased implementation with stakeholder engagement",
                timeline_phases=[],
                resource_requirements={"budget": "To be determined", "timeline": "12-18 months"},
                risk_mitigation=["Comprehensive planning", "Risk assessment"],
                success_metrics=["Improved efficiency", "Enhanced performance"],
                roi_justification="Cost-effective solution with measurable benefits",
                stakeholder_benefits={"management": ["Improved operations"], "users": ["Better experience"]}
            )

    async def _generate_solution_components(
        self,
        problem_analysis: ProblemAnalysisResult,
        approach: Dict[str, Any],
        innovation_research: Dict[str, Any]
    ) -> List[SolutionComponent]:
        """Generate detailed solution components."""
        logger.info("SOLUTION_GEN: Generating solution components")

        try:
            system_prompt = """You are a Solution Architecture Expert specializing in breaking down complex solutions into implementable components.

Your task is to design specific solution components that directly address the identified problems.

Each component should:
1. Address specific problems or requirements
2. Be technically feasible and implementable
3. Include detailed implementation steps
4. Specify clear benefits and success criteria
5. Identify dependencies and risks
6. Be aligned with the overall solution approach

Return JSON array of solution components with:
- component_id: Unique identifier
- name: Component name
- description: Detailed description
- purpose: Why this component is needed
- addresses_problems: List of problem IDs this addresses
- technical_details: Technical specifications and requirements
- implementation_steps: Step-by-step implementation guide
- benefits: Expected benefits and outcomes
- risks: Potential risks and challenges
- dependencies: Dependencies on other components or systems
- success_criteria: How to measure success"""

            context = {
                "problems": [p.to_dict() for p in problem_analysis.problems_identified],
                "approach": approach,
                "requirements": problem_analysis.solution_requirements.to_dict()
            }

            user_prompt = f"""
            Solution Context:
            {json.dumps(context, indent=2)}

            Design 3-5 specific solution components that implement the {approach.get('approach_name')} approach.
            Each component should directly address identified problems and requirements.

            Return JSON array of solution components as specified.
            """

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await self.llm.ainvoke(messages)

            try:
                components_data = json.loads(response.content)
                components = []

                for i, comp_data in enumerate(components_data):
                    component = SolutionComponent(
                        component_id=comp_data.get("component_id", f"COMP_{i+1:03d}"),
                        name=comp_data.get("name", f"Component {i+1}"),
                        description=comp_data.get("description", "Solution component"),
                        purpose=comp_data.get("purpose", "Address organizational needs"),
                        addresses_problems=comp_data.get("addresses_problems", []),
                        technical_details=comp_data.get("technical_details", []),
                        implementation_steps=comp_data.get("implementation_steps", []),
                        benefits=comp_data.get("benefits", []),
                        risks=comp_data.get("risks", []),
                        dependencies=comp_data.get("dependencies", []),
                        success_criteria=comp_data.get("success_criteria", [])
                    )
                    components.append(component)

                logger.info(f"SOLUTION_GEN: Generated {len(components)} solution components")
                return components

            except json.JSONDecodeError:
                logger.warning("Failed to parse components JSON, creating fallback")
                return self._create_fallback_components()

        except Exception as e:
            logger.error(f"SOLUTION_GEN: Component generation failed: {e}")
            return self._create_fallback_components()

    def _create_fallback_components(self) -> List[SolutionComponent]:
        """Create fallback solution components."""
        return [
            SolutionComponent(
                component_id="COMP_001",
                name="Core System Enhancement",
                description="Enhance core systems to address operational challenges",
                purpose="Improve system performance and reliability",
                addresses_problems=["PROB_001"],
                technical_details=["Modern architecture", "Scalable design"],
                implementation_steps=["Analysis", "Design", "Development", "Testing", "Deployment"],
                benefits=["Improved performance", "Enhanced reliability"],
                risks=["Implementation complexity", "Integration challenges"],
                dependencies=["Existing system compatibility"],
                success_criteria=["Performance metrics", "User satisfaction"]
            ),
            SolutionComponent(
                component_id="COMP_002",
                name="Process Optimization",
                description="Optimize business processes for efficiency",
                purpose="Streamline operations and reduce inefficiencies",
                addresses_problems=["PROB_002"],
                technical_details=["Workflow automation", "Process mapping"],
                implementation_steps=["Process analysis", "Redesign", "Automation", "Training"],
                benefits=["Increased efficiency", "Reduced costs"],
                risks=["Change resistance", "Training requirements"],
                dependencies=["Stakeholder buy-in"],
                success_criteria=["Process metrics", "Cost savings"]
            )
        ]

    async def _generate_implementation_strategy(
        self,
        problem_analysis: ProblemAnalysisResult,
        approach: Dict[str, Any],
        components: List[SolutionComponent]
    ) -> str:
        """Generate comprehensive implementation strategy."""
        strategy_elements = [
            f"Implementation Philosophy: {approach.get('implementation_philosophy', 'Systematic approach')}",
            f"Approach Type: {approach.get('approach_type', 'hybrid').title()} implementation",
            f"Complexity Level: {approach.get('complexity', 'moderate').title()} complexity solution",
            "Key Implementation Phases:",
            "1. Planning and Analysis Phase",
            "2. Design and Architecture Phase",
            "3. Development and Configuration Phase",
            "4. Testing and Validation Phase",
            "5. Deployment and Go-Live Phase",
            "6. Training and Change Management Phase",
            "7. Monitoring and Optimization Phase"
        ]

        return "\n".join(strategy_elements)

    async def _generate_timeline_phases(
        self,
        approach: Dict[str, Any],
        components: List[SolutionComponent]
    ) -> List[Dict[str, Any]]:
        """Generate implementation timeline phases."""
        complexity = approach.get("complexity", "moderate")

        if complexity == "simple":
            duration_months = 6
        elif complexity == "moderate":
            duration_months = 12
        elif complexity == "complex":
            duration_months = 18
        else:  # enterprise
            duration_months = 24

        phases = [
            {"phase": "Planning & Analysis", "duration_months": max(1, duration_months // 6), "deliverables": ["Requirements analysis", "Solution design"]},
            {"phase": "Development & Configuration", "duration_months": max(2, duration_months // 3), "deliverables": ["System development", "Component integration"]},
            {"phase": "Testing & Validation", "duration_months": max(1, duration_months // 6), "deliverables": ["System testing", "User acceptance testing"]},
            {"phase": "Deployment & Training", "duration_months": max(1, duration_months // 6), "deliverables": ["System deployment", "User training"]},
            {"phase": "Optimization & Support", "duration_months": max(1, duration_months // 6), "deliverables": ["Performance optimization", "Ongoing support"]}
        ]

        return phases

    async def _generate_executive_summary(
        self,
        problem_analysis: ProblemAnalysisResult,
        approach: Dict[str, Any],
        components: List[SolutionComponent]
    ) -> str:
        """Generate executive summary for the solution."""
        summary_parts = [
            f"Our {approach.get('approach_name', 'comprehensive solution')} addresses the critical challenges facing {problem_analysis.organization_name}.",
            f"This {approach.get('approach_type', 'hybrid')} approach leverages {', '.join(approach.get('key_innovations', ['modern methodologies']))} to deliver measurable results.",
            f"The solution consists of {len(components)} integrated components designed to {approach.get('core_concept', 'address organizational needs')}.",
            f"Key differentiators include {', '.join(approach.get('differentiators', ['proven approach', 'tailored solution']))}.",
            "This solution will deliver immediate value while positioning the organization for long-term success."
        ]

        return " ".join(summary_parts)

    def _estimate_resource_requirements(
        self,
        approach: Dict[str, Any],
        components: List[SolutionComponent]
    ) -> Dict[str, Any]:
        """Estimate resource requirements for the solution."""
        complexity = approach.get("complexity", "moderate")

        if complexity == "simple":
            return {
                "budget_range": "$100K - $500K",
                "timeline": "3-6 months",
                "team_size": "3-5 people",
                "technical_resources": "Standard development tools"
            }
        elif complexity == "moderate":
            return {
                "budget_range": "$500K - $2M",
                "timeline": "6-12 months",
                "team_size": "5-10 people",
                "technical_resources": "Modern development platform and tools"
            }
        elif complexity == "complex":
            return {
                "budget_range": "$2M - $10M",
                "timeline": "12-18 months",
                "team_size": "10-20 people",
                "technical_resources": "Enterprise-grade platform and infrastructure"
            }
        else:  # enterprise
            return {
                "budget_range": "$10M+",
                "timeline": "18-24 months",
                "team_size": "20+ people",
                "technical_resources": "Comprehensive enterprise solution stack"
            }

    def _identify_risk_mitigation(
        self,
        approach: Dict[str, Any],
        components: List[SolutionComponent]
    ) -> List[str]:
        """Identify risk mitigation strategies."""
        return [
            "Comprehensive project planning and risk assessment",
            "Phased implementation to minimize disruption",
            "Regular stakeholder communication and feedback",
            "Thorough testing and validation procedures",
            "Contingency planning for critical scenarios",
            "Change management and training programs",
            "Ongoing monitoring and performance tracking"
        ]

    def _define_solution_success_metrics(
        self,
        problem_analysis: ProblemAnalysisResult,
        approach: Dict[str, Any]
    ) -> List[str]:
        """Define success metrics for the solution."""
        base_metrics = [
            "Improved operational efficiency (measurable KPIs)",
            "Enhanced user satisfaction scores",
            "Reduced operational costs",
            "Increased system performance and reliability",
            "Better compliance adherence"
        ]

        # Add approach-specific metrics
        if approach.get("approach_type") == "innovative":
            base_metrics.append("Innovation adoption and technology leadership")
        elif approach.get("approach_type") == "transformational":
            base_metrics.append("Organizational transformation milestones")

        return base_metrics

    async def _generate_roi_justification(
        self,
        problem_analysis: ProblemAnalysisResult,
        approach: Dict[str, Any],
        components: List[SolutionComponent]
    ) -> str:
        """Generate ROI justification for the solution."""
        justification_parts = [
            "This solution delivers strong return on investment through multiple value streams:",
            "• Operational cost savings through improved efficiency and automation",
            "• Risk reduction by addressing critical organizational vulnerabilities",
            "• Enhanced productivity through streamlined processes and modern tools",
            "• Future cost avoidance by preventing escalation of current problems",
            "• Competitive advantage through innovative capabilities and improved service delivery",
            "The investment will typically pay for itself within 12-24 months through measurable operational improvements."
        ]

        return "\n".join(justification_parts)

    def _map_stakeholder_benefits(
        self,
        problem_analysis: ProblemAnalysisResult,
        approach: Dict[str, Any],
        components: List[SolutionComponent]
    ) -> Dict[str, List[str]]:
        """Map benefits to different stakeholder groups."""
        return {
            "management": [
                "Improved operational oversight and control",
                "Better decision-making through enhanced data and analytics",
                "Reduced operational risks and compliance concerns",
                "Clear ROI and measurable business value"
            ],
            "end_users": [
                "Enhanced user experience and interface",
                "Improved service quality and reliability",
                "Faster response times and better performance",
                "More intuitive and efficient workflows"
            ],
            "technical_staff": [
                "Modern, maintainable technology stack",
                "Improved tools and development capabilities",
                "Reduced technical debt and maintenance burden",
                "Enhanced system monitoring and diagnostics"
            ],
            "external_customers": [
                "Better service delivery and quality",
                "Improved response times and availability",
                "Enhanced communication and transparency",
                "More reliable and consistent service experience"
            ]
        }

    def _calculate_innovation_score(
        self,
        solutions: List[UniqueSolution],
        innovation_research: Dict[str, Any]
    ) -> float:
        """Calculate innovation score based on solution uniqueness."""
        score = 0.5  # Base score

        for solution in solutions:
            if solution.approach == SolutionApproach.INNOVATIVE:
                score += 0.2
            elif solution.approach == SolutionApproach.TRANSFORMATIONAL:
                score += 0.15

            if len(solution.innovation_elements) > 3:
                score += 0.1

        return min(score / len(solutions) if solutions else 0.5, 1.0)

    def _calculate_feasibility_score(
        self,
        solutions: List[UniqueSolution],
        problem_analysis: ProblemAnalysisResult
    ) -> float:
        """Calculate feasibility score based on solution practicality."""
        score = 0.7  # Base feasibility score

        for solution in solutions:
            if solution.complexity == SolutionComplexity.SIMPLE:
                score += 0.1
            elif solution.complexity == SolutionComplexity.ENTERPRISE:
                score -= 0.1

        return min(score / len(solutions) if solutions else 0.7, 1.0)

    def _calculate_alignment_score(
        self,
        solutions: List[UniqueSolution],
        problem_analysis: ProblemAnalysisResult
    ) -> float:
        """Calculate alignment score based on problem-solution mapping."""
        score = 0.8  # Base alignment score

        total_problems = len(problem_analysis.problems_identified)

        for solution in solutions:
            addressed_problems = len(solution.target_problems)
            if addressed_problems >= total_problems:
                score += 0.1

        return min(score / len(solutions) if solutions else 0.8, 1.0)

    async def _generate_solution_rationale(
        self,
        problem_analysis: ProblemAnalysisResult,
        solutions: List[UniqueSolution],
        innovation_research: Dict[str, Any]
    ) -> str:
        """Generate rationale explaining the solution approach."""
        rationale_parts = [
            f"Our solution approach for {problem_analysis.organization_name} is based on comprehensive analysis of {len(problem_analysis.problems_identified)} identified problems.",
            f"We have developed {len(solutions)} unique solution alternatives that address different aspects of the challenges.",
            "Each solution is designed to:",
            "• Directly address root causes rather than symptoms",
            "• Leverage industry best practices and innovative approaches",
            "• Provide measurable value and return on investment",
            "• Align with stakeholder needs and organizational constraints",
            "• Position the organization for long-term success",
            "Our recommendations are informed by extensive research and proven methodologies."
        ]

        return "\n".join(rationale_parts)
