"""
Trigger service for automatically initiating organizational context search when opportunities are created.
"""

import async<PERSON>
from typing import Op<PERSON>
from loguru import logger

from services.proposal.organizational_context_service import OrganizationalContextService


class OpportunityContextTrigger:
    """Service for triggering organizational context search on opportunity creation."""
    
    def __init__(self):
        self.context_service = OrganizationalContextService()
    
    async def trigger_context_search_for_sam_opportunity(
        self,
        notice_id: str,
        tenant_id: str = "default",
        organization_name: Optional[str] = None,
        additional_context: Optional[str] = None
    ) -> bool:
        """
        Trigger context search for a SAM opportunity.
        
        Args:
            notice_id: The SAM notice ID
            tenant_id: The tenant identifier (default for SAM)
            organization_name: Optional organization name
            additional_context: Optional additional search context
            
        Returns:
            bool: True if context search was triggered successfully
        """
        try:
            logger.info(f"Triggering context search for SAM opportunity: {notice_id}")
            
            # Trigger context search in background
            success = await self.context_service.trigger_context_search_for_opportunity(
                opportunity_id=notice_id,
                tenant_id=tenant_id,
                source="sam",
                organization_name=organization_name,
                additional_context=additional_context
            )
            
            if success:
                logger.info(f"Successfully triggered context search for SAM opportunity: {notice_id}")
            else:
                logger.warning(f"Failed to trigger context search for SAM opportunity: {notice_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error triggering context search for SAM opportunity {notice_id}: {e}")
            return False
    
    async def trigger_context_search_for_custom_opportunity(
        self,
        opportunity_id: str,
        tenant_id: str,
        organization_name: Optional[str] = None,
        additional_context: Optional[str] = None
    ) -> bool:
        """
        Trigger context search for a custom opportunity.
        
        Args:
            opportunity_id: The custom opportunity ID
            tenant_id: The tenant identifier
            organization_name: Optional organization name
            additional_context: Optional additional search context
            
        Returns:
            bool: True if context search was triggered successfully
        """
        try:
            logger.info(f"Triggering context search for custom opportunity: {opportunity_id}")
            
            # Trigger context search in background
            success = await self.context_service.trigger_context_search_for_opportunity(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                source="custom",
                organization_name=organization_name,
                additional_context=additional_context
            )
            
            if success:
                logger.info(f"Successfully triggered context search for custom opportunity: {opportunity_id}")
            else:
                logger.warning(f"Failed to trigger context search for custom opportunity: {opportunity_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error triggering context search for custom opportunity {opportunity_id}: {e}")
            return False
    
    def trigger_context_search_background(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        organization_name: Optional[str] = None,
        additional_context: Optional[str] = None
    ) -> None:
        """
        Trigger context search in background without waiting for completion.
        
        This method starts the context search as a background task and returns immediately.
        Use this when you don't want to block the opportunity creation process.
        
        Args:
            opportunity_id: The opportunity identifier
            tenant_id: The tenant identifier
            source: The source type (sam, custom, ebuy)
            organization_name: Optional organization name
            additional_context: Optional additional search context
        """
        try:
            logger.info(f"Starting background context search for {source} opportunity: {opportunity_id}")
            
            # Create background task
            async def background_search():
                try:
                    await self.context_service.trigger_context_search_for_opportunity(
                        opportunity_id=opportunity_id,
                        tenant_id=tenant_id,
                        source=source,
                        organization_name=organization_name,
                        additional_context=additional_context
                    )
                except Exception as e:
                    logger.error(f"Background context search failed for {opportunity_id}: {e}")
            
            # Start the background task
            try:
                loop = asyncio.get_event_loop()
                loop.create_task(background_search())
            except RuntimeError:
                # If no event loop is running, we can't start background task
                logger.warning(f"No event loop available for background context search: {opportunity_id}")
            
        except Exception as e:
            logger.error(f"Error starting background context search for {opportunity_id}: {e}")


# Global instance for easy import
opportunity_context_trigger = OpportunityContextTrigger()


# Convenience functions for easy use in controllers
async def trigger_sam_opportunity_context(
    notice_id: str,
    tenant_id: str = "default",
    organization_name: Optional[str] = None,
    additional_context: Optional[str] = None
) -> bool:
    """Convenience function to trigger context search for SAM opportunity."""
    return await opportunity_context_trigger.trigger_context_search_for_sam_opportunity(
        notice_id, tenant_id, organization_name, additional_context
    )


async def trigger_custom_opportunity_context(
    opportunity_id: str,
    tenant_id: str,
    organization_name: Optional[str] = None,
    additional_context: Optional[str] = None
) -> bool:
    """Convenience function to trigger context search for custom opportunity."""
    return await opportunity_context_trigger.trigger_context_search_for_custom_opportunity(
        opportunity_id, tenant_id, organization_name, additional_context
    )


def trigger_opportunity_context_background(
    opportunity_id: str,
    tenant_id: str,
    source: str,
    organization_name: Optional[str] = None,
    additional_context: Optional[str] = None
) -> None:
    """Convenience function to trigger background context search."""
    opportunity_context_trigger.trigger_context_search_background(
        opportunity_id, tenant_id, source, organization_name, additional_context
    )
