import asyncio
from typing import Any, Dict

from loguru import logger
from services.chroma.chroma_service import ChromaService
from database import get_kontratar_db
from services.llm.llm_factory import get_llm
from services.proposal.compliance_schemas import ComplianceValidator


class ContentComplianceService:
    """
    Service for generating content compliance context and LLM output using ChromaDB and an LLM.
    """
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.max_tokens = 3096
        self.chroma_service = ChromaService(embedding_api_url, None)
        # Use the LLM factory to get the configured LLM
        self.llm = get_llm(
            temperature=0,
            # Pass additional parameters for Ollama if needed
            num_ctx=6000,
            num_predict=self.max_tokens,
            base_url=llm_api_url  # For backward compatibility with Ollama
        )

    async def generate_content_compliance(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        is_rfp: bool = True,
    ) -> Dict[str, Any]:
        """
        Generate content compliance output using ChromaDB and LLM.
        Returns a dict with 'content' (LLM output) and 'context' (list of cleaned chunks).
        """
        logger.info(f"Starting generate_content_compliance for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}, is_rfp={is_rfp}")
        chroma_query = '''
            List the statements, sections, factors or tasks that the contractor should propose
            or include in their response as well as the different volumes to be submitted and the neccessary content for each volume.
            Get All Tasks/Factors needed to be shown in this response.
        '''
        
        # Initialize variables
        requirements_context = []
        context_str = ""

        async for db in get_kontratar_db():
            max_chunks = 6
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.debug(f"Querying ChromaDB with collection_name={collection_name}, chroma_query={chroma_query.strip()}, max_chunks={max_chunks}")
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            context_str = "\n\n".join(requirements_context)
            logger.debug(f"Retrieved {len(requirements_context)} relevant context chunks for content compliance.")
            break

        logger.info(f"Context for LLM:\n{context_str}")

        message = "This is an RFP, Show me what is expected in each volume" if is_rfp else "This is an RFI"
        system_prompt = '''
            **Role:**
            You are a senior government proposal compliance expert with 20+ years of experience in federal contracting, specializing in RFP/RFI analysis and compliance requirements extraction. You have successfully guided hundreds of contractors through complex government procurement processes.

            **Critical Mission:**
            Extract and identify ALL mandatory content requirements from government solicitations with absolute precision. Missing even one requirement could result in proposal rejection and loss of multi-million dollar contract opportunities.

            **Task:**
            Analyze the complete RFP/RFI documentation and identify every single piece of NECESSARY and REQUIRED content that must appear in the contractor's response. You are NOT creating the response itself - you are creating a comprehensive compliance checklist that ensures nothing is missed.

            **IMPORTANT: You MUST return your response in the following JSON format:**
            ```json
            {{
              "content_compliance": [
                {{
                  "volume_title": "Volume I",
                  "content": "Detailed content requirements for Volume I...",
                  "page_limit": number (optional),
                  "evaluation_criteria": ["criterion1", "criterion2"] (optional),
                  "mandatory_sections": ["section1", "section2"] (optional)
                }},
                {{
                  "volume_title": "Volume II",
                  "content": "Detailed content requirements for Volume II...",
                  "page_limit": number (optional),
                  "evaluation_criteria": ["criterion1", "criterion2"] (optional),
                  "mandatory_sections": ["section1", "section2"] (optional)
                }}
              ]
            }}
            ```

            **Analysis Framework:**
            - ALL complete solicitation information and attachments are provided within <context>
            - Extract requirements with surgical precision - no assumptions or interpretations
            - For RFPs: Organize requirements by specific volumes as mandated in the solicitation
            - For RFIs: Organize by response sections or categories as specified
            - Each volume should have its own specific content requirements

            **Mandatory Extraction Requirements:**
            1. **COMPLIANCE FOUNDATION**: Extract every requirement based solely on information in <context> - no external assumptions
            2. **EVALUATION FACTORS**: If explicit TASKS, FACTORS, or EVALUATION CRITERIA are mentioned, extract them with extreme detail including:
               - Specific performance metrics and standards
               - Technical capabilities and demonstrations required
               - Past performance requirements and evaluation criteria
               - Scoring methodologies and weighting factors
            3. **PERSONNEL REQUIREMENTS**: Extract all personnel-related requirements including:
               - Key personnel qualifications and experience requirements
               - Organizational charts and staffing plans
               - Security clearance requirements
               - Training and certification mandates
            4. **TECHNICAL APPROACHES**: Identify all required PLANS and APPROACHES including:
               - Technical approach and methodology
               - Management plans and organizational structure
               - Security plans and compliance measures
               - Quality assurance and control plans
               - Risk management and mitigation strategies
            5. **SUBMISSION REQUIREMENTS**: Extract all formatting, submission, and administrative requirements:
               - Page limits and formatting specifications
               - Required certifications and registrations
               - Submission deadlines and methods
               - Required forms and documentation
            6. **PRICING AND COST**: Identify all cost-related requirements:
               - Pricing structure and breakdown requirements
               - Cost justification and supporting documentation
               - Wage determination compliance
               - Financial capability demonstrations

            **Output Standards:**
            - This is a COMPLIANCE CHECKLIST, not a proposal response
            - NO summarization - extract complete detailed requirements
            - NO placeholders, assumptions, or generic statements
            - NO meta-commentary or explanatory phrases
            - Use exact terminology from the solicitation
            - Maintain complete traceability to source requirements
        '''

        user_prompt = f'''
        <context>
            {context_str}
        </context>

        **SOLICITATION TYPE**: {message}

        **EXTRACTION DIRECTIVE**:
        Analyze the complete solicitation documentation provided in the context above and extract ALL mandatory content requirements that must appear in the contractor's response. This is a critical compliance analysis - missing requirements could result in proposal rejection.

        **OUTPUT FORMAT REQUIREMENT**:
        You MUST return your response in the following JSON format:
        ```json
        {{
          "content_compliance": [
            {{
              "volume_title": "Volume I",
              "content": "Detailed content requirements for Volume I including all technical requirements, page limits, formatting requirements, etc.",
              "page_limit": number (if specified in solicitation),
              "evaluation_criteria": ["specific evaluation factors for this volume"],
              "mandatory_sections": ["required section names for this volume"]
            }},
            {{
              "volume_title": "Volume II",
              "content": "Detailed content requirements for Volume II including all pricing requirements, cost breakdowns, etc.",
              "page_limit": number (if specified in solicitation),
              "evaluation_criteria": ["specific evaluation factors for this volume"],
              "mandatory_sections": ["required section names for this volume"]
            }}
          ]
        }}
        ```

        **Field Requirements:**
        - "volume_title": Use exact volume names from solicitation
        - "content": Comprehensive description of all requirements for this volume
        - "page_limit": Include if specified in solicitation (optional field)
        - "evaluation_criteria": List specific evaluation factors/criteria for this volume (optional)
        - "mandatory_sections": List required section names/titles for this volume (optional)
        - Each volume should contain ONLY the requirements specific to that volume
        - Do NOT duplicate content between volumes
        - If only one volume exists, return only that volume in the array

        **REQUIRED OUTPUT**:
        Create a comprehensive compliance checklist organized by volume (for RFPs) or section (for RFIs) that includes:
        - Every mandatory technical requirement and capability demonstration
        - All evaluation factors and scoring criteria with specific details
        - Complete personnel requirements including qualifications and clearances
        - All required plans, approaches, and methodologies
        - Submission requirements, formatting, and administrative mandates
        - Pricing structure and cost documentation requirements
        - Any special certifications, registrations, or compliance requirements

        **CRITICAL CONSTRAINTS**:
        - Use ONLY information explicitly stated in the context above
        - Extract complete requirements without summarization or interpretation
        - Maintain exact terminology and specifications from the solicitation
        - Ensure no requirement is missed or overlooked
        '''
        messages = [
            ("system", system_prompt),
            ("human", user_prompt)
        ]
        content = self.llm.invoke(messages)
        logger.info("LLM invocation successful for content compliance.")

        logger.debug(f"LLM content compliance output: {content.content[:500]}{'...' if len(content.content) > 500 else ''}")

        # Try to parse and validate JSON response
        try:
            from services.proposal.utilities import ProposalUtilities

            # Extract JSON from the response (try markdown first, then brackets)
            json_data = ProposalUtilities.extract_json_from_markdown(content.content)
            if not json_data:
                json_data = ProposalUtilities.extract_json_from_brackets(content.content)

            if json_data and "content_compliance" in json_data:
                # Validate using centralized validator
                validation_result = ComplianceValidator.validate_content_compliance(json_data)

                if validation_result["is_valid"]:
                    logger.info("Successfully parsed and validated content compliance JSON")
                    return {
                        "content": content.content,
                        "context": requirements_context,
                        "structured_data": validation_result["validated_data"],
                        "is_valid": True
                    }
                else:
                    logger.warning(f"JSON validation failed: {validation_result['errors']}")
                    # Return raw JSON if validation fails but JSON is parseable
                    return {
                        "content": content.content,
                        "context": requirements_context,
                        "structured_data": json_data,
                        "is_valid": False,
                        "validation_error": validation_result["errors"]
                    }
            else:
                logger.warning("Could not parse structured JSON, falling back to text format")
                return {
                    "content": content.content,
                    "context": requirements_context,
                    "is_valid": False,
                    "error": "Could not parse JSON from response"
                }
        except Exception as e:
            logger.warning(f"Error parsing JSON response: {e}, falling back to text format")
            return {
                "content": content.content,
                "context": requirements_context,
                "is_valid": False,
                "error": str(e)
            }

    async def generate_sow_tasks(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
    ) -> str:
        """
        Generate specific program tasks outlined in RFP using ChromaDB and LLM.
        Returns a string which is the final output
        """
        logger.info(f"Starting generate_sow_tasks for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        chroma_query = '''
            List the tasks/program tasks that the contractor must respond to
        '''
        
        # Initialize variables
        context_str = ""

        async for db in get_kontratar_db():
            max_chunks = 5
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            logger.debug(f"Querying ChromaDB with collection_name={collection_name}, chroma_query={chroma_query.strip()}, max_chunks={max_chunks}")
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            requirements_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            context_str = "\n\n".join(requirements_context)
            logger.debug(f"Retrieved {len(requirements_context)} relevant context chunks for SOW tasks.")
            break

        system_prompt = '''
            **Role:**
            You are a senior government contracting analyst with 20+ years of experience in federal procurement and Statement of Work (SOW) analysis. You specialize in extracting and detailing specific program tasks and performance requirements from complex government solicitations.

            **Critical Mission:**
            Extract and detail every specific task, program requirement, and performance obligation outlined in the Statement of Work with absolute precision. Each task must be understood completely to ensure proper proposal response and contract performance.

            **Task Analysis Requirements:**
            1. **TASK IDENTIFICATION**: Identify every distinct task, program element, and performance requirement specified in the SOW
            2. **DETAILED BREAKDOWN**: For each task, extract:
               - Complete task description and objectives
               - Specific performance standards and metrics
               - Required deliverables and outcomes
               - Timeline and milestone requirements
               - Quality standards and acceptance criteria
               - Any subtasks or component activities
            3. **PERFORMANCE SPECIFICATIONS**: Detail what constitutes successful task completion including:
               - Technical specifications and standards
               - Compliance requirements and regulations
               - Integration requirements with existing systems
               - Reporting and documentation requirements
            4. **OPERATIONAL CONTEXT**: Explain how each task fits within the overall program objectives and government mission

            **Output Standards:**
            - Extract information solely from the provided context
            - Provide comprehensive detail for each task - no summarization
            - Use exact terminology and specifications from the SOW
            - NO placeholders, assumptions, or generic statements
            - NO meta-commentary or explanatory phrases
            - Maintain complete traceability to source requirements
        '''

        user_prompt = f'''
        <context>
            {context_str}
        </context>

        **ANALYSIS DIRECTIVE**:
        Extract and detail every specific task, program requirement, and performance obligation outlined in the Statement of Work provided in the context above.

        **REQUIRED OUTPUT**:
        For each task identified in the SOW, provide:
        1. **Task Title and Description**: Complete task name and detailed description
        2. **Performance Requirements**: Specific standards, metrics, and success criteria
        3. **Deliverables**: All required outputs, reports, and deliverable items
        4. **Technical Specifications**: Any technical requirements, standards, or compliance mandates
        5. **Timeline Requirements**: Milestones, deadlines, and performance schedules
        6. **Subtasks and Activities**: Detailed breakdown of component activities and sub-requirements
        7. **Quality Standards**: Acceptance criteria and quality assurance requirements
        8. **Integration Requirements**: How the task interfaces with existing systems or other tasks

        **CRITICAL CONSTRAINTS**:
        - Extract information ONLY from the context provided above
        - Provide complete detail for each task - no summarization or abbreviation
        - Use exact terminology and specifications from the SOW
        - Ensure no task or requirement is missed or overlooked
        - Return ONLY the task analysis - no introductory or concluding statements
        '''
        messages = [
            ("system", system_prompt),
            ("user", user_prompt)
        ]
        logger.info("Invoking LLM for SOW tasks.")
        try:
            result = self.llm.invoke(messages)
            logger.info("LLM invocation successful for SOW tasks.")
        except Exception as e:
            logger.error(f"LLM invocation failed for SOW tasks: {e}")
            raise

        logger.debug(f"LLM SOW tasks output: {str(result.content)[:500]}{'...' if len(str(result.content)) > 500 else ''}")
        return str(result.content)

    async def generate(self, opportunity_id: str, tenant_id: str, source: str) -> str:
        sow_tasks = await self.generate_sow_tasks(opportunity_id, tenant_id, source)
        volumes_content = await self.generate_content_compliance(opportunity_id, tenant_id, source)

        return f"""
        {volumes_content["content"]}
        {sow_tasks}
        """

