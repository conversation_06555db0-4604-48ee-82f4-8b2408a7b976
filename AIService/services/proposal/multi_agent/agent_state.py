"""
Agent State Management

Defines the state structures and data models used throughout the multi-agent
proposal generation system for maintaining context and coordination.
"""

from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import json
from datetime import datetime


class SectionType(Enum):
    """Types of proposal sections"""
    COVER_LETTER = "cover_letter"
    EXECUTIVE_SUMMARY = "executive_summary"
    TECHNICAL_APPROACH = "technical_approach"
    MANAGEMENT_PLAN = "management_plan"
    PAST_PERFORMANCE = "past_performance"
    PRICING = "pricing"
    COMPLIANCE = "compliance"
    APPENDIX = "appendix"
    CUSTOM = "custom"


class ComplexityLevel(Enum):
    """Complexity levels for content generation"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AgentRole(Enum):
    """Roles of different agents in the system"""
    COORDINATOR = "coordinator"
    CONTEXT_RETRIEVAL = "context_retrieval"
    COVER_LETTER_SPECIALIST = "cover_letter_specialist"
    TECHNICAL_SPECIALIST = "technical_specialist"
    MANAGEMENT_SPECIALIST = "management_specialist"
    COMPLIANCE_AGENT = "compliance_agent"
    QUALITY_ASSURANCE = "quality_assurance"
    PROBLEM_ANALYST = "problem_analyst"
    SOLUTION_DESIGNER = "solution_designer"
    SOLUTION_VALIDATOR = "solution_validator"


@dataclass
class AgentResult:
    """Result from an agent operation"""
    agent_role: AgentRole
    success: bool
    content: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    processing_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'agent_role': self.agent_role.value,
            'success': self.success,
            'content': self.content,
            'metadata': self.metadata,
            'error_message': self.error_message,
            'processing_time': self.processing_time,
            'timestamp': self.timestamp.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentResult':
        """Create from dictionary"""
        return cls(
            agent_role=AgentRole(data['agent_role']),
            success=data['success'],
            content=data.get('content'),
            metadata=data.get('metadata', {}),
            error_message=data.get('error_message'),
            processing_time=data.get('processing_time', 0.0),
            timestamp=datetime.fromisoformat(data['timestamp'])
        )


@dataclass
class ContextData:
    """Context data retrieved for content generation"""
    opportunity_context: List[str] = field(default_factory=list)
    client_context: List[str] = field(default_factory=list)
    technical_context: List[str] = field(default_factory=list)
    compliance_context: List[str] = field(default_factory=list)
    past_performance_context: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'opportunity_context': self.opportunity_context,
            'client_context': self.client_context,
            'technical_context': self.technical_context,
            'compliance_context': self.compliance_context,
            'past_performance_context': self.past_performance_context
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ContextData':
        """Create from dictionary"""
        return cls(
            opportunity_context=data.get('opportunity_context', []),
            client_context=data.get('client_context', []),
            technical_context=data.get('technical_context', []),
            compliance_context=data.get('compliance_context', []),
            past_performance_context=data.get('past_performance_context', [])
        )


@dataclass
class WorkflowPlan:
    """Plan for executing the content generation workflow"""
    primary_specialist: AgentRole
    supporting_agents: List[AgentRole] = field(default_factory=list)
    complexity_level: ComplexityLevel = ComplexityLevel.MEDIUM
    estimated_time: float = 0.0
    special_instructions: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'primary_specialist': self.primary_specialist.value,
            'supporting_agents': [agent.value for agent in self.supporting_agents],
            'complexity_level': self.complexity_level.value,
            'estimated_time': self.estimated_time,
            'special_instructions': self.special_instructions
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowPlan':
        """Create from dictionary"""
        return cls(
            primary_specialist=AgentRole(data['primary_specialist']),
            supporting_agents=[AgentRole(agent) for agent in data.get('supporting_agents', [])],
            complexity_level=ComplexityLevel(data.get('complexity_level', 'medium')),
            estimated_time=data.get('estimated_time', 0.0),
            special_instructions=data.get('special_instructions', [])
        )


class DraftState:
    """
    Central state management for the draft generation process.
    
    This class maintains all the context, progress, and results throughout
    the multi-agent workflow.
    """
    
    def __init__(self, 
                 opportunity_id: str,
                 tenant_id: str,
                 section_type: Union[str, SectionType],
                 section_content: str = "",
                 client_short_name: str = ""):
        """Initialize draft state"""
        self.opportunity_id = opportunity_id
        self.tenant_id = tenant_id
        self.section_type = SectionType(section_type) if isinstance(section_type, str) else section_type
        self.section_content = section_content
        self.client_short_name = client_short_name
        
        # Workflow state
        self.context_data: Optional[ContextData] = None
        self.workflow_plan: Optional[WorkflowPlan] = None
        self.complexity_level: ComplexityLevel = ComplexityLevel.MEDIUM
        
        # Results tracking
        self.agent_results: Dict[AgentRole, AgentResult] = {}
        self.final_content: Optional[str] = None
        self.quality_score: Optional[float] = None
        
        # Metadata
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        self.processing_metadata: Dict[str, Any] = {}
    
    def update_context(self, context_data: ContextData) -> None:
        """Update context data"""
        self.context_data = context_data
        self.updated_at = datetime.now()
    
    def set_workflow_plan(self, plan: WorkflowPlan) -> None:
        """Set the workflow execution plan"""
        self.workflow_plan = plan
        self.complexity_level = plan.complexity_level
        self.updated_at = datetime.now()
    
    def add_agent_result(self, result: AgentResult) -> None:
        """Add result from an agent"""
        self.agent_results[result.agent_role] = result
        self.updated_at = datetime.now()
    
    def set_final_content(self, content: str, quality_score: Optional[float] = None) -> None:
        """Set the final generated content"""
        self.final_content = content
        self.quality_score = quality_score
        self.updated_at = datetime.now()
    
    def get_agent_result(self, agent_role: AgentRole) -> Optional[AgentResult]:
        """Get result from a specific agent"""
        return self.agent_results.get(agent_role)
    
    def is_complete(self) -> bool:
        """Check if the draft generation is complete"""
        return self.final_content is not None
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get a summary of current progress"""
        completed_agents = [role.value for role, result in self.agent_results.items() if result.success]
        failed_agents = [role.value for role, result in self.agent_results.items() if not result.success]
        
        return {
            'opportunity_id': self.opportunity_id,
            'section_type': self.section_type.value,
            'complexity_level': self.complexity_level.value,
            'completed_agents': completed_agents,
            'failed_agents': failed_agents,
            'is_complete': self.is_complete(),
            'quality_score': self.quality_score,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert entire state to dictionary for serialization"""
        return {
            'opportunity_id': self.opportunity_id,
            'tenant_id': self.tenant_id,
            'section_type': self.section_type.value,
            'section_content': self.section_content,
            'client_short_name': self.client_short_name,
            'context_data': self.context_data.to_dict() if self.context_data else None,
            'workflow_plan': self.workflow_plan.to_dict() if self.workflow_plan else None,
            'complexity_level': self.complexity_level.value,
            'agent_results': {role.value: result.to_dict() for role, result in self.agent_results.items()},
            'final_content': self.final_content,
            'quality_score': self.quality_score,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'processing_metadata': self.processing_metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DraftState':
        """Create DraftState from dictionary"""
        state = cls(
            opportunity_id=data['opportunity_id'],
            tenant_id=data['tenant_id'],
            section_type=data['section_type'],
            section_content=data.get('section_content', ''),
            client_short_name=data.get('client_short_name', '')
        )
        
        # Restore context data
        if data.get('context_data'):
            state.context_data = ContextData.from_dict(data['context_data'])
        
        # Restore workflow plan
        if data.get('workflow_plan'):
            state.workflow_plan = WorkflowPlan.from_dict(data['workflow_plan'])
        
        # Restore other fields
        state.complexity_level = ComplexityLevel(data.get('complexity_level', 'medium'))
        state.final_content = data.get('final_content')
        state.quality_score = data.get('quality_score')
        state.processing_metadata = data.get('processing_metadata', {})
        
        # Restore agent results
        for role_str, result_data in data.get('agent_results', {}).items():
            role = AgentRole(role_str)
            state.agent_results[role] = AgentResult.from_dict(result_data)
        
        # Restore timestamps
        if data.get('created_at'):
            state.created_at = datetime.fromisoformat(data['created_at'])
        if data.get('updated_at'):
            state.updated_at = datetime.fromisoformat(data['updated_at'])
        
        return state
