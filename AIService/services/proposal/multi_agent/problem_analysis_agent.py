"""
Problem Analysis Agent for Multi-Agent System

This agent specializes in deep problem analysis and understanding, using the
Problem Analysis Framework to identify root causes, stakeholder impacts,
and solution requirements.
"""

import logging
from typing import Dict, Any, List, Optional

from .base_agent import BaseAgent
from .agent_state import DraftState, AgentResult, AgentRole
from services.proposal.problem_analysis_framework import (
    ProblemAnalysisFramework, ResearchType, ResearchDepth
)

logger = logging.getLogger(__name__)


class ProblemAnalysisAgent(BaseAgent):
    """
    Specialized agent for conducting comprehensive problem analysis.
    
    Responsibilities:
    - Deep problem investigation and root cause analysis
    - Stakeholder impact assessment
    - Industry context analysis
    - Solution requirements identification
    - Problem categorization and prioritization
    """
    
    def __init__(self):
        """Initialize the problem analysis agent"""
        super().__init__(AgentRole.PROBLEM_ANALYST)
        self.problem_framework = ProblemAnalysisFramework()
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Conduct comprehensive problem analysis for the opportunity.
        
        Args:
            state: Current draft state with opportunity context
            
        Returns:
            AgentResult with problem analysis insights
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            logger.info(f"ProblemAnalysisAgent: Starting problem analysis for {state.opportunity_id}")
            
            # Extract opportunity details from state
            opportunity_details = self._extract_opportunity_details(state)
            
            # Conduct comprehensive problem analysis
            problem_analysis = await self.problem_framework.analyze_opportunity_problems(
                opportunity_id=state.opportunity_id,
                tenant_id=state.tenant_id,
                source=getattr(state, 'source', 'custom'),
                organization_name=opportunity_details.get('organization_name', 'Organization'),
                opportunity_title=opportunity_details.get('title', 'Opportunity'),
                opportunity_description=opportunity_details.get('description', state.section_content),
                additional_context=state.section_content
            )
            
            # Generate problem analysis content for the section
            content = await self._generate_problem_analysis_content(
                problem_analysis, state.section_type, state.section_content
            )
            
            # Prepare result metadata
            metadata = {
                'problems_identified': len(problem_analysis.problems_identified),
                'confidence_score': problem_analysis.confidence_score,
                'analysis_methodology': problem_analysis.analysis_methodology,
                'key_findings': problem_analysis.key_findings[:3],  # Top 3 findings
                'strategic_implications': problem_analysis.strategic_implications[:3],
                'problem_categories': [p.category.value for p in problem_analysis.problems_identified],
                'severity_distribution': self._analyze_severity_distribution(problem_analysis.problems_identified)
            }
            
            # Store problem analysis in state for other agents
            state.add_context('problem_analysis', problem_analysis.to_dict())
            
            logger.info(f"ProblemAnalysisAgent: Completed analysis with {len(problem_analysis.problems_identified)} problems identified")
            
            return self._create_success_result(content=content, metadata=metadata)
            
        except Exception as e:
            logger.error(f"ProblemAnalysisAgent: Problem analysis failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    def _extract_opportunity_details(self, state: DraftState) -> Dict[str, str]:
        """Extract opportunity details from state."""
        # Try to extract from existing context
        context = getattr(state, 'context', {})
        
        return {
            'organization_name': context.get('organization_name', 'Target Organization'),
            'title': context.get('opportunity_title', 'Government Opportunity'),
            'description': context.get('opportunity_description', state.section_content)
        }
    
    async def _generate_problem_analysis_content(
        self,
        problem_analysis,
        section_type: str,
        section_content: str
    ) -> str:
        """Generate content based on problem analysis results."""
        try:
            system_prompt = """You are a Problem Analysis Content Generator specializing in translating comprehensive problem analysis into clear, actionable proposal content.

Your task is to create compelling proposal content that demonstrates deep understanding of the organization's challenges and positions your solution as the ideal response.

Content should:
1. Demonstrate deep understanding of root causes, not just symptoms
2. Show awareness of stakeholder impacts and organizational context
3. Connect problems to solution opportunities
4. Use evidence-based insights from research
5. Position your organization as uniquely qualified to address these challenges
6. Be professional, authoritative, and solution-focused

Generate content that shows you truly understand what the organization is facing and why they need this solution."""
            
            # Prepare problem analysis summary
            problems_summary = []
            for problem in problem_analysis.problems_identified:
                problems_summary.append({
                    'title': problem.title,
                    'category': problem.category.value,
                    'severity': problem.severity.value,
                    'root_causes': problem.root_causes,
                    'current_impact': problem.current_impact,
                    'stakeholders_affected': [s.value for s in problem.affected_stakeholders]
                })
            
            analysis_context = {
                'organization': problem_analysis.organization_name,
                'problems_identified': problems_summary,
                'key_findings': problem_analysis.key_findings,
                'strategic_implications': problem_analysis.strategic_implications,
                'industry_context': problem_analysis.industry_context.to_dict(),
                'confidence_score': problem_analysis.confidence_score
            }
            
            user_prompt = f"""
            Section Type: {section_type}
            Section Requirements: {section_content}
            
            Problem Analysis Results:
            {analysis_context}
            
            Generate compelling proposal content that demonstrates deep understanding of the organization's challenges and positions our solution approach as the ideal response to their specific needs.
            
            Focus on showing that we understand:
            - The real problems they're facing (root causes, not symptoms)
            - Why these problems exist and what's driving them
            - How these problems impact different stakeholders
            - What success looks like for addressing these challenges
            - Why our approach is uniquely suited to solve these specific problems
            
            Make the content authoritative, evidence-based, and solution-focused.
            """
            
            messages = self._create_messages(system_prompt, user_prompt)
            response = await self._call_llm(messages)
            
            return response
            
        except Exception as e:
            logger.error(f"ProblemAnalysisAgent: Content generation failed: {e}")
            return f"Comprehensive analysis of organizational challenges and solution requirements for {section_type}."
    
    def _analyze_severity_distribution(self, problems) -> Dict[str, int]:
        """Analyze the distribution of problem severities."""
        distribution = {}
        for problem in problems:
            severity = problem.severity.value
            distribution[severity] = distribution.get(severity, 0) + 1
        return distribution
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get problem analysis agent specific instructions"""
        return [
            "Conduct thorough problem investigation using multiple data sources",
            "Focus on root causes rather than surface-level symptoms",
            "Analyze stakeholder impacts and organizational context",
            "Use evidence-based insights to support problem identification",
            "Connect problems to solution opportunities and requirements",
            "Demonstrate deep understanding of organizational challenges",
            "Position solutions as direct responses to identified problems"
        ]


class SolutionDesignAgent(BaseAgent):
    """
    Specialized agent for designing unique, problem-specific solutions.
    
    Responsibilities:
    - Generate innovative solution approaches
    - Design solution components that address specific problems
    - Ensure solution feasibility and effectiveness
    - Create competitive differentiation
    - Develop implementation strategies
    """
    
    def __init__(self):
        """Initialize the solution design agent"""
        super().__init__(AgentRole.SOLUTION_DESIGNER)
        # Import here to avoid circular imports
        from services.proposal.solution_generation_engine import SolutionGenerationEngine
        self.solution_engine = SolutionGenerationEngine()
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Design unique solutions based on problem analysis.
        
        Args:
            state: Current draft state with problem analysis context
            
        Returns:
            AgentResult with solution design content
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            logger.info(f"SolutionDesignAgent: Starting solution design for {state.opportunity_id}")
            
            # Get problem analysis from state context
            problem_analysis_data = state.get_context('problem_analysis')
            if not problem_analysis_data:
                logger.warning("SolutionDesignAgent: No problem analysis found in state")
                return await self._generate_generic_solution_content(state)
            
            # Convert problem analysis data back to object
            from services.proposal.problem_analysis_framework import ProblemAnalysisResult
            problem_analysis = self._reconstruct_problem_analysis(problem_analysis_data)
            
            # Generate unique solutions
            solution_generation = await self.solution_engine.generate_unique_solutions(
                problem_analysis=problem_analysis,
                max_solutions=2  # Generate 2 solution approaches for variety
            )
            
            # Generate solution design content for the section
            content = await self._generate_solution_design_content(
                solution_generation, state.section_type, state.section_content
            )
            
            # Prepare result metadata
            metadata = {
                'solutions_generated': len(solution_generation.unique_solutions),
                'innovation_score': solution_generation.innovation_score,
                'feasibility_score': solution_generation.feasibility_score,
                'alignment_score': solution_generation.alignment_score,
                'overall_quality_score': solution_generation.overall_quality_score,
                'solution_approaches': [sol.approach.value for sol in solution_generation.unique_solutions],
                'complexity_levels': [sol.complexity.value for sol in solution_generation.unique_solutions]
            }
            
            # Store solution generation in state for validation
            state.add_context('solution_generation', solution_generation.to_dict())
            
            logger.info(f"SolutionDesignAgent: Completed design with quality score {solution_generation.overall_quality_score:.2f}")
            
            return self._create_success_result(content=content, metadata=metadata)
            
        except Exception as e:
            logger.error(f"SolutionDesignAgent: Solution design failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    def _reconstruct_problem_analysis(self, problem_analysis_data: Dict[str, Any]):
        """Reconstruct ProblemAnalysisResult from dictionary data."""
        # This is a simplified reconstruction - in a full implementation,
        # you would properly reconstruct all the dataclass objects
        from services.proposal.problem_analysis_framework import ProblemAnalysisResult
        
        # For now, create a minimal object with the essential data
        class MockProblemAnalysis:
            def __init__(self, data):
                self.opportunity_id = data.get('opportunity_id', '')
                self.tenant_id = data.get('tenant_id', '')
                self.organization_name = data.get('organization_name', '')
                self.analysis_timestamp = data.get('analysis_timestamp', '')
                self.problems_identified = data.get('problems_identified', [])
                self.solution_requirements = data.get('solution_requirements', {})
                self.industry_context = data.get('industry_context', {})
                self.confidence_score = data.get('confidence_score', 0.5)
                self.key_findings = data.get('key_findings', [])
                self.strategic_implications = data.get('strategic_implications', [])
                self.recommendations = data.get('recommendations', [])
        
        return MockProblemAnalysis(problem_analysis_data)
    
    async def _generate_generic_solution_content(self, state: DraftState) -> AgentResult:
        """Generate generic solution content when problem analysis is not available."""
        content = f"Innovative solution approach for {state.section_type} addressing organizational requirements and challenges."
        
        metadata = {
            'fallback_mode': True,
            'reason': 'No problem analysis available'
        }
        
        return self._create_success_result(content=content, metadata=metadata)
    
    async def _generate_solution_design_content(
        self,
        solution_generation,
        section_type: str,
        section_content: str
    ) -> str:
        """Generate content based on solution design results."""
        try:
            system_prompt = """You are a Solution Design Content Generator specializing in creating compelling proposal content that showcases innovative, problem-specific solutions.

Your task is to create proposal content that demonstrates unique solution approaches that directly address identified problems with innovative yet feasible solutions.

Content should:
1. Present unique, differentiated solution approaches
2. Show clear connection between problems and solution components
3. Highlight innovation elements and competitive advantages
4. Demonstrate feasibility and implementation readiness
5. Include specific technical details and methodologies
6. Show measurable value and ROI potential
7. Be authoritative, detailed, and solution-focused

Generate content that positions your solution as uniquely qualified and innovative while being practical and implementable."""
            
            # Prepare solution summary
            solutions_summary = []
            for solution in solution_generation.unique_solutions:
                solutions_summary.append({
                    'title': solution.title,
                    'approach': solution.approach.value,
                    'complexity': solution.complexity.value,
                    'executive_summary': solution.executive_summary,
                    'innovation_elements': solution.innovation_elements,
                    'competitive_advantages': solution.competitive_advantages,
                    'implementation_strategy': solution.implementation_strategy,
                    'components': [comp.name for comp in solution.solution_components]
                })
            
            solution_context = {
                'solutions': solutions_summary,
                'innovation_score': solution_generation.innovation_score,
                'feasibility_score': solution_generation.feasibility_score,
                'alignment_score': solution_generation.alignment_score,
                'solution_rationale': solution_generation.solution_rationale
            }
            
            user_prompt = f"""
            Section Type: {section_type}
            Section Requirements: {section_content}
            
            Solution Design Results:
            {solution_context}
            
            Generate compelling proposal content that showcases our unique solution approach and demonstrates why our solution is the ideal choice for addressing the organization's specific challenges.
            
            Focus on:
            - Unique solution approaches and innovation elements
            - Clear problem-solution mapping and effectiveness
            - Competitive advantages and differentiators
            - Technical feasibility and implementation readiness
            - Measurable value and success criteria
            - Why our approach is superior to alternatives
            
            Make the content detailed, authoritative, and solution-focused.
            """
            
            messages = self._create_messages(system_prompt, user_prompt)
            response = await self._call_llm(messages)
            
            return response
            
        except Exception as e:
            logger.error(f"SolutionDesignAgent: Content generation failed: {e}")
            return f"Innovative solution design and implementation approach for {section_type}."
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get solution design agent specific instructions"""
        return [
            "Design unique, problem-specific solutions rather than generic approaches",
            "Ensure clear mapping between problems and solution components",
            "Incorporate innovation elements while maintaining feasibility",
            "Develop competitive advantages and differentiators",
            "Create detailed implementation strategies and timelines",
            "Focus on measurable value and ROI justification",
            "Demonstrate technical expertise and solution readiness"
        ]
