"""
Solution Validation Agent for Multi-Agent System

This agent specializes in validating that proposed solutions actually address
identified problems and ensures solution quality, feasibility, and alignment.
"""

import logging
from typing import Dict, Any, List, Optional

from .base_agent import BaseAgent
from .agent_state import DraftState, AgentResult, AgentRole
from services.proposal.problem_solution_validator import ProblemSolutionValidator

logger = logging.getLogger(__name__)


class SolutionValidationAgent(BaseAgent):
    """
    Specialized agent for validating solutions against problems and requirements.
    
    Responsibilities:
    - Validate problem-solution mapping
    - Assess solution effectiveness and feasibility
    - Check stakeholder alignment
    - Evaluate risk assessment
    - Validate success criteria
    - Provide improvement recommendations
    """
    
    def __init__(self):
        """Initialize the solution validation agent"""
        super().__init__(AgentRole.SOLUTION_VALIDATOR)
        self.validator = ProblemSolutionValidator()
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Validate solutions against identified problems and requirements.
        
        Args:
            state: Current draft state with problem analysis and solution design context
            
        Returns:
            AgentResult with validation results and improved content
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            logger.info(f"SolutionValidationAgent: Starting solution validation for {state.opportunity_id}")
            
            # Get problem analysis and solution generation from state context
            problem_analysis_data = state.get_context('problem_analysis')
            solution_generation_data = state.get_context('solution_generation')
            
            if not problem_analysis_data or not solution_generation_data:
                logger.warning("SolutionValidationAgent: Missing problem analysis or solution generation data")
                return await self._generate_basic_validation_content(state)
            
            # Reconstruct objects for validation
            problem_analysis = self._reconstruct_problem_analysis(problem_analysis_data)
            solution_generation = self._reconstruct_solution_generation(solution_generation_data)
            
            # Perform comprehensive validation
            validation_report = await self.validator.validate_solutions(
                problem_analysis=problem_analysis,
                solution_generation=solution_generation
            )
            
            # Generate validation-informed content
            content = await self._generate_validation_content(
                validation_report, state.section_type, state.section_content
            )
            
            # Prepare result metadata
            metadata = {
                'validation_result': validation_report.overall_result.value,
                'overall_score': validation_report.overall_score,
                'category_scores': validation_report.category_scores,
                'issues_found': len(validation_report.validation_issues),
                'approved_solutions': len(validation_report.approved_solutions),
                'rejected_solutions': len(validation_report.rejected_solutions),
                'critical_issues': len([issue for issue in validation_report.validation_issues if issue.severity == "critical"]),
                'validation_categories': list(validation_report.category_scores.keys())
            }
            
            # Store validation report in state
            state.add_context('validation_report', validation_report.to_dict())
            
            # Determine if validation passed
            validation_passed = validation_report.overall_result.value in ['pass', 'warning']
            
            if validation_passed:
                logger.info(f"SolutionValidationAgent: Validation passed with score {validation_report.overall_score:.2f}")
                return self._create_success_result(content=content, metadata=metadata)
            else:
                logger.warning(f"SolutionValidationAgent: Validation failed with score {validation_report.overall_score:.2f}")
                # Return content with validation warnings
                return self._create_success_result(content=content, metadata=metadata)
            
        except Exception as e:
            logger.error(f"SolutionValidationAgent: Solution validation failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    def _reconstruct_problem_analysis(self, problem_analysis_data: Dict[str, Any]):
        """Reconstruct problem analysis object from dictionary data."""
        # Simplified reconstruction for validation purposes
        class MockProblemAnalysis:
            def __init__(self, data):
                self.opportunity_id = data.get('opportunity_id', '')
                self.tenant_id = data.get('tenant_id', '')
                self.organization_name = data.get('organization_name', '')
                self.problems_identified = self._reconstruct_problems(data.get('problems_identified', []))
                self.solution_requirements = self._reconstruct_solution_requirements(data.get('solution_requirements', {}))
                self.confidence_score = data.get('confidence_score', 0.5)
            
            def _reconstruct_problems(self, problems_data):
                # Simplified problem reconstruction
                class MockProblem:
                    def __init__(self, problem_data):
                        self.problem_id = problem_data.get('problem_id', '')
                        self.title = problem_data.get('title', '')
                        self.description = problem_data.get('description', '')
                        self.severity = MockSeverity(problem_data.get('severity', 'medium'))
                        self.category = MockCategory(problem_data.get('category', 'operational_efficiency'))
                
                class MockSeverity:
                    def __init__(self, value):
                        self.value = value
                
                class MockCategory:
                    def __init__(self, value):
                        self.value = value
                
                return [MockProblem(p) for p in problems_data]
            
            def _reconstruct_solution_requirements(self, requirements_data):
                class MockSolutionRequirements:
                    def __init__(self, data):
                        self.functional_requirements = data.get('functional_requirements', [])
                        self.compliance_requirements = data.get('compliance_requirements', [])
                        self.technical_specifications = data.get('technical_specifications', [])
                    
                    def to_dict(self):
                        return {
                            'functional_requirements': self.functional_requirements,
                            'compliance_requirements': self.compliance_requirements,
                            'technical_specifications': self.technical_specifications
                        }
                
                return MockSolutionRequirements(requirements_data)
        
        return MockProblemAnalysis(problem_analysis_data)
    
    def _reconstruct_solution_generation(self, solution_generation_data: Dict[str, Any]):
        """Reconstruct solution generation object from dictionary data."""
        class MockSolutionGeneration:
            def __init__(self, data):
                self.opportunity_id = data.get('opportunity_id', '')
                self.tenant_id = data.get('tenant_id', '')
                self.unique_solutions = self._reconstruct_solutions(data.get('unique_solutions', []))
                self.innovation_score = data.get('innovation_score', 0.5)
                self.feasibility_score = data.get('feasibility_score', 0.5)
                self.alignment_score = data.get('alignment_score', 0.5)
                self.overall_quality_score = data.get('overall_quality_score', 0.5)
            
            def _reconstruct_solutions(self, solutions_data):
                class MockSolution:
                    def __init__(self, solution_data):
                        self.solution_id = solution_data.get('solution_id', '')
                        self.title = solution_data.get('title', '')
                        self.target_problems = solution_data.get('target_problems', [])
                        self.solution_components = self._reconstruct_components(solution_data.get('solution_components', []))
                        self.approach = MockApproach(solution_data.get('approach', 'hybrid'))
                        self.complexity = MockComplexity(solution_data.get('complexity', 'moderate'))
                        self.innovation_elements = solution_data.get('innovation_elements', [])
                        self.competitive_advantages = solution_data.get('competitive_advantages', [])
                        self.timeline_phases = solution_data.get('timeline_phases', [])
                        self.risk_mitigation = solution_data.get('risk_mitigation', [])
                        self.success_metrics = solution_data.get('success_metrics', [])
                        self.stakeholder_benefits = solution_data.get('stakeholder_benefits', {})
                    
                    def _reconstruct_components(self, components_data):
                        class MockComponent:
                            def __init__(self, comp_data):
                                self.component_id = comp_data.get('component_id', '')
                                self.name = comp_data.get('name', '')
                                self.description = comp_data.get('description', '')
                                self.addresses_problems = comp_data.get('addresses_problems', [])
                                self.technical_details = comp_data.get('technical_details', [])
                                self.implementation_steps = comp_data.get('implementation_steps', [])
                                self.benefits = comp_data.get('benefits', [])
                                self.risks = comp_data.get('risks', [])
                                self.dependencies = comp_data.get('dependencies', [])
                                self.success_criteria = comp_data.get('success_criteria', [])
                        
                        return [MockComponent(c) for c in components_data]
                
                class MockApproach:
                    def __init__(self, value):
                        self.value = value
                
                class MockComplexity:
                    def __init__(self, value):
                        self.value = value
                
                return [MockSolution(s) for s in solutions_data]
        
        return MockSolutionGeneration(solution_generation_data)
    
    async def _generate_basic_validation_content(self, state: DraftState) -> AgentResult:
        """Generate basic validation content when full validation data is not available."""
        content = f"Quality-assured solution approach for {state.section_type} with comprehensive validation and risk mitigation."
        
        metadata = {
            'validation_mode': 'basic',
            'reason': 'Missing problem analysis or solution generation data'
        }
        
        return self._create_success_result(content=content, metadata=metadata)
    
    async def _generate_validation_content(
        self,
        validation_report,
        section_type: str,
        section_content: str
    ) -> str:
        """Generate content based on validation results."""
        try:
            system_prompt = """You are a Solution Validation Content Generator specializing in creating proposal content that demonstrates validated, high-quality solutions.

Your task is to create proposal content that showcases solutions that have been thoroughly validated against identified problems and requirements.

Content should:
1. Emphasize solution validation and quality assurance
2. Highlight problem-solution alignment and effectiveness
3. Address any validation concerns proactively
4. Demonstrate risk mitigation and feasibility
5. Show stakeholder alignment and success criteria
6. Include quality metrics and validation results
7. Be confident, authoritative, and quality-focused

Generate content that positions your solution as thoroughly validated, low-risk, and highly effective."""
            
            # Prepare validation summary
            validation_summary = {
                'overall_result': validation_report.overall_result.value,
                'overall_score': validation_report.overall_score,
                'category_scores': validation_report.category_scores,
                'approved_solutions': len(validation_report.approved_solutions),
                'total_solutions': len(validation_report.approved_solutions) + len(validation_report.rejected_solutions),
                'recommendations': validation_report.recommendations[:3],  # Top 3 recommendations
                'validation_strengths': self._identify_validation_strengths(validation_report),
                'quality_indicators': self._extract_quality_indicators(validation_report)
            }
            
            user_prompt = f"""
            Section Type: {section_type}
            Section Requirements: {section_content}
            
            Validation Results:
            {validation_summary}
            
            Generate compelling proposal content that demonstrates our solution has been thoroughly validated and represents a low-risk, high-value approach.
            
            Focus on:
            - Solution validation and quality assurance processes
            - Problem-solution alignment and effectiveness validation
            - Risk mitigation and feasibility confirmation
            - Stakeholder alignment and success criteria validation
            - Quality metrics and validation scores
            - Why our validated approach minimizes risk and maximizes success
            
            Make the content confident, quality-focused, and validation-oriented.
            """
            
            messages = self._create_messages(system_prompt, user_prompt)
            response = await self._call_llm(messages)
            
            return response
            
        except Exception as e:
            logger.error(f"SolutionValidationAgent: Content generation failed: {e}")
            return f"Validated and quality-assured solution approach for {section_type}."
    
    def _identify_validation_strengths(self, validation_report) -> List[str]:
        """Identify validation strengths from the report."""
        strengths = []
        
        if validation_report.overall_score >= 0.8:
            strengths.append("High overall validation score")
        
        for category, score in validation_report.category_scores.items():
            if score >= 0.8:
                strengths.append(f"Strong {category.replace('_', ' ')} validation")
        
        if len(validation_report.approved_solutions) > len(validation_report.rejected_solutions):
            strengths.append("Majority of solutions passed validation")
        
        critical_issues = [issue for issue in validation_report.validation_issues if issue.severity == "critical"]
        if not critical_issues:
            strengths.append("No critical validation issues identified")
        
        return strengths[:5]  # Top 5 strengths
    
    def _extract_quality_indicators(self, validation_report) -> List[str]:
        """Extract quality indicators from validation results."""
        indicators = []
        
        indicators.append(f"Overall validation score: {validation_report.overall_score:.1%}")
        indicators.append(f"Solutions approved: {len(validation_report.approved_solutions)}")
        
        if validation_report.category_scores:
            best_category = max(validation_report.category_scores.items(), key=lambda x: x[1])
            indicators.append(f"Strongest validation area: {best_category[0].replace('_', ' ')} ({best_category[1]:.1%})")
        
        return indicators
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get solution validation agent specific instructions"""
        return [
            "Validate all solutions against identified problems and requirements",
            "Ensure problem-solution mapping is clear and effective",
            "Assess solution feasibility and implementation readiness",
            "Verify stakeholder alignment and success criteria",
            "Identify and address validation issues proactively",
            "Provide quality assurance and risk mitigation",
            "Generate content that demonstrates validated, low-risk solutions"
        ]
