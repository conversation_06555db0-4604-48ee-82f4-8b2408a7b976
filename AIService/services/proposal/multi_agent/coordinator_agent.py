"""
Coordinator Agent

The central orchestrator for the multi-agent proposal generation system.
Analyzes requirements, creates workflow plans, and coordinates specialist agents.
"""

import logging
from typing import Dict, Any, List
from langchain_core.messages import BaseMessage

from .base_agent import BaseAgent
from .agent_state import (
    DraftState, AgentResult, AgentRole, SectionType, 
    ComplexityLevel, WorkflowPlan, ContextData
)

logger = logging.getLogger(__name__)


class CoordinatorAgent(BaseAgent):
    """
    Coordinates the multi-agent workflow for proposal generation.
    
    Responsibilities:
    - Analyze section requirements and complexity
    - Create workflow execution plans
    - Determine appropriate specialist agents
    - Provide coordination and oversight
    """
    
    def __init__(self):
        """Initialize the coordinator agent"""
        super().__init__(AgentRole.COORDINATOR)
    
    async def process(self, state: DraftState) -> AgentResult:
        """
        Analyze the section and create a workflow plan.
        
        Args:
            state: Current draft state
            
        Returns:
            AgentResult with workflow analysis and plan
        """
        if not self._validate_state(state):
            return self._create_failure_result("Invalid draft state provided")
        
        try:
            # Analyze the section requirements
            analysis_result = await self._analyze_section(state)
            
            # Create workflow plan based on analysis
            workflow_plan = self._create_workflow_plan(state)
            
            # Update state with the plan
            state.set_workflow_plan(workflow_plan)
            
            # Prepare result metadata
            metadata = {
                'analysis': analysis_result,
                'workflow_plan': workflow_plan.to_dict(),
                'section_type': state.section_type.value,
                'complexity_level': workflow_plan.complexity_level.value
            }
            
            return self._create_success_result(
                content=f"Workflow plan created for {state.section_type.value}",
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"CoordinatorAgent: Analysis failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    async def _analyze_section(self, state: DraftState) -> Dict[str, Any]:
        """
        Analyze section requirements using LLM.
        
        Args:
            state: Draft state containing section information
            
        Returns:
            Analysis results including complexity and requirements
        """
        section_type = state.section_type.value
        section_content = state.section_content or "No specific content provided"
        
        system_prompt = f"""You are an expert proposal analyst specializing in {section_type} sections.

Analyze the provided section requirements and determine:
1. Complexity level (low, medium, high, critical)
2. Key requirements and deliverables
3. Technical depth needed
4. Compliance considerations
5. Recommended approach

Provide a structured analysis that will guide content generation."""

        user_content = f"""Section Type: {section_type}

Section Requirements:
{section_content}

Opportunity ID: {state.opportunity_id}
Client: {state.client_short_name}

Please analyze these requirements and provide:
1. Complexity assessment with justification
2. Key deliverables and requirements
3. Technical considerations
4. Compliance requirements
5. Recommended content approach
6. Special instructions for content generation

Format your response as a structured analysis."""

        try:
            messages = self._create_messages(system_prompt, user_content)
            analysis_text = await self._call_llm(messages)
            
            # Parse complexity level from analysis
            complexity_level = self._extract_complexity_level(analysis_text)
            
            analysis_result = {
                "complexity_level": complexity_level.value,
                "analysis_text": analysis_text,
                "section_type": section_type,
                "key_requirements": self._extract_key_requirements(section_content),
                "special_instructions": self._get_special_instructions(section_type)
            }
            
            return analysis_result

        except Exception as e:
            logger.error(f"CoordinatorAgent: LLM analysis failed: {e}")
            # NO FALLBACK - fail cleanly to ensure high quality
            raise e
    
    def _create_workflow_plan(self, state: DraftState) -> WorkflowPlan:
        """Create a workflow plan based on section analysis"""
        section_type = state.section_type
        complexity = state.complexity_level.value if state.complexity_level else "medium"
        
        # Determine primary specialist
        primary_specialist = self._get_primary_specialist(section_type)
        
        # Determine supporting agents
        supporting_agents = self._get_supporting_agents(section_type)
        
        # Estimate processing time
        estimated_time = self._estimate_processing_time(complexity, section_type)
        
        # Get special instructions
        special_instructions = self._get_special_instructions(section_type.value)
        
        return WorkflowPlan(
            primary_specialist=primary_specialist,
            supporting_agents=supporting_agents,
            complexity_level=ComplexityLevel(complexity) if isinstance(complexity, str) else complexity,
            estimated_time=estimated_time,
            special_instructions=special_instructions
        )
    
    def _get_primary_specialist(self, section_type: SectionType) -> AgentRole:
        """Determine the primary specialist for a section type"""
        specialist_mapping = {
            SectionType.COVER_LETTER: AgentRole.COVER_LETTER_SPECIALIST,
            SectionType.EXECUTIVE_SUMMARY: AgentRole.COVER_LETTER_SPECIALIST,
            SectionType.TECHNICAL_APPROACH: AgentRole.TECHNICAL_SPECIALIST,
            SectionType.MANAGEMENT_PLAN: AgentRole.MANAGEMENT_SPECIALIST,
            SectionType.PAST_PERFORMANCE: AgentRole.MANAGEMENT_SPECIALIST,
            SectionType.PRICING: AgentRole.TECHNICAL_SPECIALIST,
            SectionType.COMPLIANCE: AgentRole.COMPLIANCE_AGENT,
            SectionType.APPENDIX: AgentRole.TECHNICAL_SPECIALIST,
            SectionType.CUSTOM: AgentRole.TECHNICAL_SPECIALIST  # Default
        }
        
        return specialist_mapping.get(section_type, AgentRole.TECHNICAL_SPECIALIST)
    
    def _get_supporting_agents(self, section_type: SectionType) -> List[AgentRole]:
        """Determine supporting agents for a section type"""
        # All sections benefit from context retrieval
        supporting = [AgentRole.CONTEXT_RETRIEVAL]

        # Add problem analysis for technical and solution-focused sections
        if section_type in [SectionType.TECHNICAL_APPROACH, SectionType.EXECUTIVE_SUMMARY, SectionType.CUSTOM]:
            supporting.append(AgentRole.PROBLEM_ANALYST)
            supporting.append(AgentRole.SOLUTION_DESIGNER)
            supporting.append(AgentRole.SOLUTION_VALIDATOR)

        # Add compliance for most sections
        if section_type != SectionType.COMPLIANCE:
            supporting.append(AgentRole.COMPLIANCE_AGENT)

        # Add quality assurance for all sections
        supporting.append(AgentRole.QUALITY_ASSURANCE)

        return supporting
    
    def _estimate_processing_time(self, complexity: str, section_type: SectionType) -> float:
        """Estimate processing time based on complexity and section type"""
        base_times = {
            SectionType.COVER_LETTER: 30.0,
            SectionType.EXECUTIVE_SUMMARY: 45.0,
            SectionType.TECHNICAL_APPROACH: 90.0,
            SectionType.MANAGEMENT_PLAN: 60.0,
            SectionType.PAST_PERFORMANCE: 45.0,
            SectionType.PRICING: 30.0,
            SectionType.COMPLIANCE: 60.0,
            SectionType.APPENDIX: 30.0,
            SectionType.CUSTOM: 60.0
        }
        
        complexity_multipliers = {
            "low": 0.7,
            "medium": 1.0,
            "high": 1.5,
            "critical": 2.0
        }
        
        base_time = base_times.get(section_type, 60.0)
        multiplier = complexity_multipliers.get(complexity, 1.0)
        
        return base_time * multiplier
    
    def _get_special_instructions(self, section_type: str) -> List[str]:
        """Get special instructions for specific section types"""
        instructions_map = {
            "cover_letter": [
                "Focus on executive-level messaging",
                "Highlight key differentiators",
                "Maintain professional tone",
                "Include compelling value proposition"
            ],
            "technical_approach": [
                "Provide detailed technical solutions",
                "Include implementation methodology",
                "Address technical risks and mitigations",
                "Demonstrate technical expertise"
            ],
            "management_plan": [
                "Detail project management approach",
                "Include organizational structure",
                "Address resource allocation",
                "Provide timeline and milestones"
            ],
            "compliance": [
                "Ensure all requirements are addressed",
                "Provide compliance matrix",
                "Include regulatory considerations",
                "Document adherence to standards"
            ]
        }
        
        return instructions_map.get(section_type, [
            "Generate comprehensive content",
            "Ensure accuracy and relevance",
            "Maintain professional quality",
            "Address all requirements"
        ])
    
    def _extract_complexity_level(self, analysis_text: str) -> ComplexityLevel:
        """Extract complexity level from LLM analysis"""
        text_lower = analysis_text.lower()
        
        if "critical" in text_lower or "very high" in text_lower:
            return ComplexityLevel.CRITICAL
        elif "high" in text_lower:
            return ComplexityLevel.HIGH
        elif "low" in text_lower:
            return ComplexityLevel.LOW
        else:
            return ComplexityLevel.MEDIUM
    
    def _get_agent_specific_instructions(self) -> List[str]:
        """Get coordinator-specific instructions"""
        return [
            "Analyze section requirements thoroughly",
            "Create comprehensive workflow plans",
            "Consider all stakeholder needs",
            "Ensure optimal resource allocation",
            "Coordinate effectively between specialists"
        ]
