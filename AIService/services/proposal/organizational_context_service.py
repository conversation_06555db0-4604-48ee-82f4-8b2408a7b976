"""
Service for managing organizational context for opportunities.
"""

import json
import asyncio
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from loguru import logger

from services.chat.web_search_service import WebSearchService
from controllers.kontratar.opps_table_controller import OppsTableController
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from database import get_customer_db, get_kontratar_db


class OrganizationalContextService:
    """Service for managing organizational context for opportunities."""
    
    def __init__(self):
        self.web_search_service = WebSearchService()
        self.sam_controller = OppsTableController()
        self.custom_controller = CustomOpportunitiesController()
    
    async def trigger_context_search_for_opportunity(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        organization_name: Optional[str] = None,
        additional_context: Optional[str] = None
    ) -> bool:
        """
        Trigger organizational context search for a newly created opportunity.
        """
        try:
            logger.info(f"Triggering context search for opportunity {opportunity_id}, source {source}")
            
            # Check if web search is available
            if not self.web_search_service.is_available():
                logger.warning("Web search service not available - skipping context search")
                return False
            
            # Get opportunity details
            try:
                opportunity_record = await self._get_opportunity_record(
                    opportunity_id, tenant_id, source
                )
                if not opportunity_record:
                    logger.error(f"Opportunity not found: {opportunity_id}")
                    return False
            except Exception as e:
                logger.error(f"Failed to retrieve opportunity details: {e}")
                return False
            
            # Extract organization information
            if not organization_name:
                organization_name = self._extract_organization_name(opportunity_record)
            
            # Build search query
            search_query = self._build_investigative_search_query(
                opportunity_record, organization_name, additional_context
            )
            
            # Perform web search
            logger.info(f"Performing context search for organization: {organization_name}")
            search_results = []
            
            async for chunk in self.web_search_service.search_and_stream(
                query=search_query,
                context_chunks=None,
                conversation_history=None
            ):
                if chunk:
                    search_results.append(chunk)
            
            if not search_results:
                logger.warning("Web search returned no results")
                return False
            
            # Combine search results
            full_search_response = "".join(search_results)
            
            # Extract insights
            key_insights, recommended_approach = self._extract_insights(full_search_response)

            # Create full context data (store complete response in database)
            context_data = {
                "opportunity_id": opportunity_id,
                "tenant_id": tenant_id,
                "organization_context": full_search_response,
                "search_summary": f"Conducted deep investigative research to uncover the real underlying reasons why {organization_name} posted this opportunity - revealing strategic drivers, root causes, and hidden motivations",
                "key_insights": key_insights,
                "recommended_approach": recommended_approach,
                "search_date": int(asyncio.get_event_loop().time())
            }

            # Store full context in database
            success = await self._store_context_in_database(
                opportunity_id, tenant_id, source, context_data
            )

            if success:
                logger.info(f"Successfully stored organizational context for {opportunity_id}")
            else:
                logger.error(f"Failed to store organizational context for {opportunity_id}")

            return success
            
        except Exception as e:
            logger.error(f"Error during context search for {opportunity_id}: {e}")
            return False
    
    async def get_organizational_context(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve stored organizational context for an opportunity.
        """
        try:
            if source.lower() == "sam":
                async for db in get_kontratar_db():
                    query = text("""
                        SELECT organizational_context
                        FROM kontratar_main.oppstable
                        WHERE notice_id = :opportunity_id
                    """)
                    result = await db.execute(query, {"opportunity_id": opportunity_id})
                    row = result.fetchone()
                    if row and row[0]:
                        return json.loads(row[0])
                    break
            elif source.lower() == "custom":
                async for db in get_customer_db():
                    query = text("""
                        SELECT organizational_context
                        FROM opportunity.custom_oppstable
                        WHERE opportunity_id = :opportunity_id
                    """)
                    result = await db.execute(query, {"opportunity_id": opportunity_id})
                    row = result.fetchone()
                    if row and row[0]:
                        return json.loads(row[0])
                    break

            return None
            
        except Exception as e:
            logger.error(f"Error retrieving organizational context for {opportunity_id}: {e}")
            return None

    async def _get_opportunity_record(self, opportunity_id: str, tenant_id: str, source: str):
        """Get opportunity record from appropriate controller."""
        try:
            if source.lower() == "sam":
                async for db in get_kontratar_db():
                    record = await self.sam_controller.get_by_notice_id(db, opportunity_id)
                    return record
            elif source.lower() == "custom":
                async for db in get_customer_db():
                    record = await self.custom_controller.get_main_info_by_opportunity_id(db, opportunity_id)
                    return record
            return None
        except Exception as e:
            logger.error(f"Error retrieving opportunity record: {e}")
            return None

    def _extract_organization_name(self, opportunity_record) -> str:
        """Extract organization name from opportunity record."""
        if hasattr(opportunity_record, 'office') and opportunity_record.office:
            return opportunity_record.office
        elif hasattr(opportunity_record, 'agency') and opportunity_record.agency:
            return opportunity_record.agency
        elif hasattr(opportunity_record, 'department') and opportunity_record.department:
            return opportunity_record.department
        else:
            return "Government Agency"
    
    def _build_investigative_search_query(
        self,
        opportunity_record,
        organization_name: str,
        additional_context: Optional[str]
    ) -> str:
        """Build the investigative search query."""
        opportunity_title = getattr(opportunity_record, 'title', 'Government Opportunity')
        opportunity_description = getattr(opportunity_record, 'description', '')
        
        return f"""
        DEEP INVESTIGATIVE RESEARCH: Uncover the real underlying reasons why "{organization_name}" posted this specific opportunity: "{opportunity_title}".
        
        Go beyond surface-level information. Investigate and reveal:
        
         ROOT CAUSE ANALYSIS:
        1. What specific problems, failures, or gaps triggered this RFP?
        2. What recent incidents, audits, or performance issues drove this need?
        3. What regulatory pressures, compliance failures, or mandates are behind this?
        4. What competitive threats or market changes forced this action?
        
         STRATEGIC IMPERATIVES:
        1. What strategic initiatives or transformation goals does this support?
        2. What budget pressures or funding opportunities created urgency?
        3. What leadership changes or new priorities drove this direction?
        4. What external pressures (Congress, IG reports, media) influenced this?
        
         HIDDEN MOTIVATIONS:
        1. What are they NOT saying in the RFP that's driving this need?
        2. What previous solutions failed and why?
        3. What internal politics or organizational dynamics are at play?
        4. What success would really look like from their perspective?
        
        Additional context: {additional_context or 'No additional context provided'}
        Opportunity details: {opportunity_description[:500] if opportunity_description else 'No description available'}
        
        OBJECTIVE: Provide deep insights that reveal the TRUE reasons this opportunity exists.
        """
    
    def _extract_insights(self, search_response: str) -> tuple:
        """Extract key insights and recommended approach from search response."""
        key_insights = []
        recommended_approach = ""
        
        lines = search_response.split('\n')
        
        # Enhanced keyword detection for deeper insights
        root_cause_keywords = ['problem', 'failure', 'gap', 'incident', 'audit', 'compliance', 'pressure', 'threat', 'crisis']
        strategic_keywords = ['initiative', 'transformation', 'budget', 'funding', 'leadership', 'mandate', 'directive']
        hidden_keywords = ['underlying', 'real reason', 'behind', 'driving', 'motivated', 'triggered', 'caused']
        success_keywords = ['success', 'outcome', 'metric', 'measure', 'goal', 'objective', 'critical']
        
        for line in lines:
            line = line.strip()
            if line and len(line) > 20:
                line_lower = line.lower()
                
                if any(keyword in line_lower for keyword in root_cause_keywords + hidden_keywords):
                    key_insights.insert(0, f" ROOT CAUSE: {line}")
                elif any(keyword in line_lower for keyword in strategic_keywords):
                    key_insights.append(f" STRATEGIC: {line}")
                elif any(keyword in line_lower for keyword in success_keywords):
                    key_insights.append(f"🚨 SUCCESS FACTOR: {line}")
                elif any(keyword in line_lower for keyword in ['recommend', 'suggest', 'approach', 'strategy', 'should']):
                    if not recommended_approach:
                        recommended_approach = line
                    else:
                        recommended_approach += " " + line
        
        if not key_insights:
            key_insights = [" Deep investigative research completed - review full context for insights"]
        
        if not recommended_approach:
            recommended_approach = "Position proposal to address underlying problems and strategic imperatives."
        
        return key_insights[:15], recommended_approach[:1500]
    
    async def _store_context_in_database(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        context_data: Dict[str, Any]
    ) -> bool:
        """Store the organizational context in the appropriate database table."""
        try:
            context_json = json.dumps(context_data)

            if source.lower() == "sam":
                async for db in get_kontratar_db():
                    # Use direct SQL update for SAM opportunities
                    query = text("""
                        UPDATE kontratar_main.oppstable
                        SET organizational_context = :context
                        WHERE notice_id = :opportunity_id
                    """)
                    result = await db.execute(query, {
                        "context": context_json,
                        "opportunity_id": opportunity_id
                    })
                    await db.commit()
                    return result.rowcount > 0
            elif source.lower() == "custom":
                async for db in get_customer_db():
                    # Use direct SQL update for custom opportunities
                    query = text("""
                        UPDATE opportunity.custom_oppstable
                        SET organizational_context = :context
                        WHERE opportunity_id = :opportunity_id
                    """)
                    result = await db.execute(query, {
                        "context": context_json,
                        "opportunity_id": opportunity_id
                    })
                    await db.commit()
                    return result.rowcount > 0

            return False

        except Exception as e:
            logger.error(f"Error storing context in database: {e}")
            return False
