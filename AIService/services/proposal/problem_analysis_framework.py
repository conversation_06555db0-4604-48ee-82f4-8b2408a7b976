"""
Problem Analysis Framework for Proposal Generation

This framework provides comprehensive problem analysis capabilities to understand
the deeper issues that organizations face, enabling the generation of targeted,
unique solutions rather than generic responses.

Key Components:
1. Root Cause Analysis Engine
2. Stakeholder Impact Assessment
3. Problem Classification System
4. Solution Requirements Identification
5. Industry Context Analysis
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from loguru import logger
from datetime import datetime

from services.chat.web_search_service import WebSearchService
from services.research.semantic_scholar_service import SemanticScholarService
from services.proposal.chroma_service import ChromaService
from database import get_customer_db, get_kontratar_db
from llm_factory import get_llm


class ProblemCategory(Enum):
    """Categories of organizational problems"""
    OPERATIONAL_EFFICIENCY = "operational_efficiency"
    TECHNOLOGY_MODERNIZATION = "technology_modernization"
    COMPLIANCE_REGULATORY = "compliance_regulatory"
    SECURITY_RISK = "security_risk"
    CAPACITY_SCALING = "capacity_scaling"
    COST_OPTIMIZATION = "cost_optimization"
    PROCESS_IMPROVEMENT = "process_improvement"
    INNOVATION_TRANSFORMATION = "innovation_transformation"
    WORKFORCE_CAPABILITY = "workforce_capability"
    INFRASTRUCTURE_UPGRADE = "infrastructure_upgrade"


class ProblemSeverity(Enum):
    """Severity levels of identified problems"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class StakeholderType(Enum):
    """Types of stakeholders affected by problems"""
    END_USERS = "end_users"
    MANAGEMENT = "management"
    TECHNICAL_STAFF = "technical_staff"
    EXTERNAL_CUSTOMERS = "external_customers"
    REGULATORY_BODIES = "regulatory_bodies"
    BUDGET_AUTHORITIES = "budget_authorities"


@dataclass
class ProblemStatement:
    """Structured representation of an identified problem"""
    problem_id: str
    title: str
    description: str
    category: ProblemCategory
    severity: ProblemSeverity
    root_causes: List[str]
    affected_stakeholders: List[StakeholderType]
    current_impact: str
    future_consequences: str
    success_metrics: List[str]
    constraints: List[str]
    timeline_urgency: str
    budget_implications: str
    regulatory_requirements: List[str]
    technical_dependencies: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return asdict(self)


@dataclass
class SolutionRequirements:
    """Requirements for solutions to address identified problems"""
    functional_requirements: List[str]
    non_functional_requirements: List[str]
    technical_specifications: List[str]
    compliance_requirements: List[str]
    integration_requirements: List[str]
    performance_criteria: List[str]
    security_requirements: List[str]
    scalability_requirements: List[str]
    maintenance_requirements: List[str]
    training_requirements: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return asdict(self)


@dataclass
class IndustryContext:
    """Industry-specific context for problem analysis"""
    industry_trends: List[str]
    common_challenges: List[str]
    emerging_solutions: List[str]
    regulatory_landscape: List[str]
    technology_evolution: List[str]
    competitive_pressures: List[str]
    best_practices: List[str]
    case_studies: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return asdict(self)


@dataclass
class ProblemAnalysisResult:
    """Complete result of problem analysis"""
    opportunity_id: str
    tenant_id: str
    organization_name: str
    analysis_timestamp: str
    problems_identified: List[ProblemStatement]
    solution_requirements: SolutionRequirements
    industry_context: IndustryContext
    confidence_score: float
    analysis_methodology: List[str]
    data_sources: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return asdict(self)


class ProblemAnalysisFramework:
    """
    Comprehensive framework for analyzing organizational problems and generating
    targeted solution requirements for proposal generation.
    """
    
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434"
    ):
        self.web_search_service = WebSearchService()
        self.semantic_scholar_service = SemanticScholarService()
        self.chroma_service = ChromaService(embedding_api_url, None)
        self.llm = get_llm(
            temperature=0.1,  # Slightly creative for problem analysis
            num_ctx=8000,
            base_url=llm_api_url
        )
        
        logger.info("ProblemAnalysisFramework: Initialized with enhanced research capabilities")
    
    async def analyze_opportunity_problems(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        organization_name: str,
        opportunity_title: str,
        opportunity_description: str,
        additional_context: Optional[str] = None
    ) -> ProblemAnalysisResult:
        """
        Perform comprehensive problem analysis for an opportunity.
        
        Args:
            opportunity_id: Unique identifier for the opportunity
            tenant_id: Tenant identifier
            source: Source of the opportunity (custom, sam, etc.)
            organization_name: Name of the organization posting the opportunity
            opportunity_title: Title of the opportunity
            opportunity_description: Description of the opportunity
            additional_context: Additional context for analysis
            
        Returns:
            ProblemAnalysisResult with comprehensive problem analysis
        """
        logger.info(f"PROBLEM_ANALYSIS: Starting comprehensive analysis for {opportunity_id}")
        
        try:
            # Step 1: Extract initial problems from opportunity documents
            initial_problems = await self._extract_problems_from_documents(
                opportunity_id, tenant_id, source, opportunity_title, opportunity_description
            )
            
            # Step 2: Conduct deep investigative research
            research_insights = await self._conduct_investigative_research(
                organization_name, opportunity_title, opportunity_description, additional_context
            )
            
            # Step 3: Perform root cause analysis
            root_cause_analysis = await self._perform_root_cause_analysis(
                initial_problems, research_insights, organization_name
            )
            
            # Step 4: Analyze stakeholder impacts
            stakeholder_analysis = await self._analyze_stakeholder_impacts(
                root_cause_analysis, organization_name, opportunity_description
            )
            
            # Step 5: Generate industry context
            industry_context = await self._generate_industry_context(
                organization_name, root_cause_analysis
            )
            
            # Step 6: Identify solution requirements
            solution_requirements = await self._identify_solution_requirements(
                root_cause_analysis, stakeholder_analysis, industry_context
            )
            
            # Step 7: Synthesize comprehensive problem statements
            problem_statements = await self._synthesize_problem_statements(
                root_cause_analysis, stakeholder_analysis, solution_requirements
            )
            
            # Step 8: Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                initial_problems, research_insights, problem_statements
            )
            
            # Create comprehensive result
            result = ProblemAnalysisResult(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                organization_name=organization_name,
                analysis_timestamp=datetime.now().isoformat(),
                problems_identified=problem_statements,
                solution_requirements=solution_requirements,
                industry_context=industry_context,
                confidence_score=confidence_score,
                analysis_methodology=[
                    "Document Analysis",
                    "Investigative Web Research", 
                    "Root Cause Analysis",
                    "Stakeholder Impact Assessment",
                    "Industry Context Analysis",
                    "Academic Research Integration"
                ],
                data_sources=[
                    "RFP/Solicitation Documents",
                    "Web Search Results",
                    "Academic Research Papers",
                    "Industry Reports",
                    "Organizational Context"
                ],
                recommendations=await self._generate_strategic_recommendations(
                    problem_statements, solution_requirements, industry_context
                )
            )
            
            logger.info(f"PROBLEM_ANALYSIS: Completed analysis with {len(problem_statements)} problems identified")
            return result
            
        except Exception as e:
            logger.error(f"PROBLEM_ANALYSIS: Analysis failed for {opportunity_id}: {e}")
            raise Exception(f"Problem analysis failed: {e}")

    async def _extract_problems_from_documents(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        opportunity_title: str,
        opportunity_description: str
    ) -> List[str]:
        """Extract initial problems from opportunity documents using ChromaDB."""
        logger.info("PROBLEM_ANALYSIS: Extracting problems from documents")

        try:
            # Query ChromaDB for problem-related content
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id

            problem_queries = [
                "problems challenges issues difficulties pain points",
                "current state limitations deficiencies gaps",
                "requirements needs objectives goals",
                "why this opportunity posted reasons motivation",
                "organizational challenges operational issues"
            ]

            context_chunks = []
            async for db in get_kontratar_db():
                for query in problem_queries:
                    chunks = await self.chroma_service.query_collection(
                        db, collection_name, query, max_results=5
                    )
                    context_chunks.extend(chunks)
                break

            if not context_chunks:
                logger.warning("No context chunks found, using opportunity description")
                context_chunks = [opportunity_description]

            # Use LLM to extract problems from context
            system_prompt = """You are a Problem Analysis Expert specializing in identifying organizational challenges and pain points.

Your task is to extract and identify specific problems, challenges, and pain points from the provided context.

Focus on:
1. Explicit problems mentioned in the text
2. Implicit challenges that can be inferred
3. Current state limitations and gaps
4. Operational difficulties and inefficiencies
5. Compliance or regulatory challenges
6. Technology or infrastructure issues
7. Process or workflow problems
8. Resource or capacity constraints

Return a JSON list of problem statements, each being a clear, specific description of an identified problem."""

            user_prompt = f"""
            Opportunity Title: {opportunity_title}

            Context to analyze:
            {' '.join(context_chunks[:3000])}  # Limit context size

            Extract all identifiable problems, challenges, and pain points from this context.
            Return as JSON array of strings, each describing a specific problem.
            """

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await self.llm.ainvoke(messages)

            # Parse JSON response
            try:
                problems = json.loads(response.content)
                if isinstance(problems, list):
                    logger.info(f"PROBLEM_ANALYSIS: Extracted {len(problems)} initial problems")
                    return problems
                else:
                    logger.warning("LLM response not a list, extracting manually")
                    return [response.content]
            except json.JSONDecodeError:
                logger.warning("Failed to parse JSON, using raw response")
                return [response.content]

        except Exception as e:
            logger.error(f"PROBLEM_ANALYSIS: Document extraction failed: {e}")
            return [f"Analysis of {opportunity_title} requirements and challenges"]

    async def _conduct_investigative_research(
        self,
        organization_name: str,
        opportunity_title: str,
        opportunity_description: str,
        additional_context: Optional[str] = None
    ) -> Dict[str, Any]:
        """Conduct deep investigative research to understand underlying problems."""
        logger.info(f"PROBLEM_ANALYSIS: Conducting investigative research for {organization_name}")

        research_results = {
            "web_search_insights": [],
            "academic_research": [],
            "industry_analysis": [],
            "organizational_context": ""
        }

        try:
            # Deep investigative web search
            if self.web_search_service.is_available():
                search_query = f"""
                DEEP INVESTIGATIVE ANALYSIS: Uncover the real underlying problems and challenges that led {organization_name} to post this opportunity: "{opportunity_title}".

                Research Focus:
                1. What specific operational, technical, or organizational problems is {organization_name} facing?
                2. What recent incidents, audits, or failures might have triggered this need?
                3. What industry-wide challenges is {organization_name} experiencing?
                4. What regulatory or compliance pressures are driving this requirement?
                5. What strategic initiatives or transformations is {organization_name} pursuing?
                6. What budget or resource constraints are influencing this decision?
                7. What competitive pressures or market changes are impacting {organization_name}?

                Investigate recent news, reports, budget documents, strategic plans, and industry analyses related to {organization_name} and similar organizations.
                """

                web_insights = []
                async for chunk in self.web_search_service.search_and_stream(
                    query=search_query,
                    context_chunks=[opportunity_description] if opportunity_description else None,
                    conversation_history=additional_context
                ):
                    if chunk:
                        web_insights.append(chunk)

                research_results["web_search_insights"] = web_insights
                research_results["organizational_context"] = "".join(web_insights)

            # Academic research for problem domain
            problem_keywords = self._extract_problem_keywords(opportunity_title, opportunity_description)
            for keyword in problem_keywords[:3]:  # Limit to top 3 keywords
                academic_research = await self.semantic_scholar_service.search_relevant_research(
                    topic=f"{keyword} challenges solutions",
                    limit=3
                )
                research_results["academic_research"].append(academic_research)

            logger.info("PROBLEM_ANALYSIS: Investigative research completed")
            return research_results

        except Exception as e:
            logger.error(f"PROBLEM_ANALYSIS: Investigative research failed: {e}")
            return research_results

    def _extract_problem_keywords(self, title: str, description: str) -> List[str]:
        """Extract key problem-related keywords from title and description."""
        text = f"{title} {description}".lower()

        # Common problem domain keywords
        problem_domains = [
            "cybersecurity", "security", "infrastructure", "modernization",
            "compliance", "audit", "risk", "efficiency", "automation",
            "cloud", "digital transformation", "data management", "analytics",
            "network", "system", "software", "hardware", "maintenance",
            "training", "workforce", "capacity", "scalability", "performance"
        ]

        found_keywords = []
        for domain in problem_domains:
            if domain in text:
                found_keywords.append(domain)

        return found_keywords[:5]  # Return top 5 matches

    async def _perform_root_cause_analysis(
        self,
        initial_problems: List[str],
        research_insights: Dict[str, Any],
        organization_name: str
    ) -> Dict[str, Any]:
        """Perform root cause analysis to understand deeper issues."""
        logger.info("PROBLEM_ANALYSIS: Performing root cause analysis")

        try:
            # Combine all research data
            context_data = []
            context_data.extend(initial_problems)
            context_data.extend(research_insights.get("web_search_insights", []))

            # Add academic research summaries
            for research in research_insights.get("academic_research", []):
                if research.get("summary"):
                    context_data.append(research["summary"])

            combined_context = " ".join(context_data)[:4000]  # Limit context size

            system_prompt = """You are a Root Cause Analysis Expert specializing in organizational problem diagnosis.

Your task is to perform deep root cause analysis to identify the fundamental underlying issues that are driving the surface-level problems.

Use the "5 Whys" methodology and systems thinking to:
1. Identify immediate causes of each problem
2. Trace back to intermediate causes
3. Discover root causes and systemic issues
4. Understand interconnections between problems
5. Identify contributing factors and enablers

Return a structured JSON analysis with:
- "surface_problems": List of visible/stated problems
- "intermediate_causes": List of underlying causes
- "root_causes": List of fundamental systemic issues
- "contributing_factors": List of factors that enable or worsen problems
- "problem_interconnections": How problems relate to each other
- "systemic_issues": Broader organizational or industry-wide issues"""

            user_prompt = f"""
            Organization: {organization_name}

            Context and Research Data:
            {combined_context}

            Perform comprehensive root cause analysis to understand why these problems exist and what fundamental issues are driving them.

            Return structured JSON analysis as specified.
            """

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await self.llm.ainvoke(messages)

            try:
                root_cause_analysis = json.loads(response.content)
                logger.info("PROBLEM_ANALYSIS: Root cause analysis completed")
                return root_cause_analysis
            except json.JSONDecodeError:
                logger.warning("Failed to parse root cause JSON, using structured fallback")
                return {
                    "surface_problems": initial_problems,
                    "intermediate_causes": ["Analysis of underlying operational challenges"],
                    "root_causes": ["Systemic organizational or technical limitations"],
                    "contributing_factors": ["Resource constraints", "Process inefficiencies"],
                    "problem_interconnections": "Problems are interconnected through operational dependencies",
                    "systemic_issues": ["Need for modernization and process improvement"]
                }

        except Exception as e:
            logger.error(f"PROBLEM_ANALYSIS: Root cause analysis failed: {e}")
            return {
                "surface_problems": initial_problems,
                "intermediate_causes": ["Operational challenges requiring analysis"],
                "root_causes": ["Fundamental organizational needs"],
                "contributing_factors": ["Various operational factors"],
                "problem_interconnections": "Multiple interconnected challenges",
                "systemic_issues": ["Organizational improvement opportunities"]
            }

    async def _analyze_stakeholder_impacts(
        self,
        root_cause_analysis: Dict[str, Any],
        organization_name: str,
        opportunity_description: str
    ) -> Dict[str, Any]:
        """Analyze how problems impact different stakeholders."""
        logger.info("PROBLEM_ANALYSIS: Analyzing stakeholder impacts")

        try:
            system_prompt = """You are a Stakeholder Impact Analysis Expert.

Your task is to analyze how the identified problems affect different stakeholder groups within and around the organization.

Consider these stakeholder types:
- End Users (citizens, customers, beneficiaries)
- Management (executives, program managers, decision makers)
- Technical Staff (IT, engineers, specialists)
- External Customers (other agencies, partners)
- Regulatory Bodies (oversight agencies, auditors)
- Budget Authorities (appropriators, financial oversight)

For each relevant stakeholder group, identify:
1. How they are impacted by the problems
2. What pain points they experience
3. What outcomes they need
4. What success looks like for them
5. What constraints they face

Return structured JSON with stakeholder impact analysis."""

            problems_context = json.dumps(root_cause_analysis, indent=2)

            user_prompt = f"""
            Organization: {organization_name}
            Opportunity Context: {opportunity_description[:1000]}

            Root Cause Analysis Results:
            {problems_context}

            Analyze how these problems impact different stakeholder groups and what each group needs for success.

            Return JSON with stakeholder impact analysis.
            """

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await self.llm.ainvoke(messages)

            try:
                stakeholder_analysis = json.loads(response.content)
                logger.info("PROBLEM_ANALYSIS: Stakeholder impact analysis completed")
                return stakeholder_analysis
            except json.JSONDecodeError:
                logger.warning("Failed to parse stakeholder JSON, using structured fallback")
                return {
                    "end_users": {"impacts": ["Service disruptions"], "needs": ["Reliable services"]},
                    "management": {"impacts": ["Operational inefficiencies"], "needs": ["Improved performance"]},
                    "technical_staff": {"impacts": ["Increased workload"], "needs": ["Better tools and processes"]},
                    "external_customers": {"impacts": ["Service quality issues"], "needs": ["Consistent service delivery"]},
                    "regulatory_bodies": {"impacts": ["Compliance concerns"], "needs": ["Regulatory adherence"]},
                    "budget_authorities": {"impacts": ["Cost overruns"], "needs": ["Cost-effective solutions"]}
                }

        except Exception as e:
            logger.error(f"PROBLEM_ANALYSIS: Stakeholder analysis failed: {e}")
            return {"analysis_error": str(e)}

    async def _generate_industry_context(
        self,
        organization_name: str,
        root_cause_analysis: Dict[str, Any]
    ) -> IndustryContext:
        """Generate industry-specific context for the problems."""
        logger.info("PROBLEM_ANALYSIS: Generating industry context")

        try:
            # Extract industry from organization name or use government as default
            industry = self._identify_industry(organization_name)

            system_prompt = f"""You are an Industry Analysis Expert specializing in {industry} sector challenges and solutions.

Your task is to provide comprehensive industry context for the identified problems.

Analyze:
1. Current industry trends affecting organizations like this
2. Common challenges faced across the {industry} sector
3. Emerging solutions and best practices in the industry
4. Regulatory landscape and compliance requirements
5. Technology evolution and modernization trends
6. Competitive pressures and market dynamics
7. Proven best practices and success stories
8. Relevant case studies and lessons learned

Return structured analysis focusing on how industry context relates to the specific problems identified."""

            problems_summary = json.dumps(root_cause_analysis.get("root_causes", []), indent=2)

            user_prompt = f"""
            Organization: {organization_name}
            Industry: {industry}

            Identified Root Causes:
            {problems_summary}

            Provide comprehensive industry context that helps understand these problems and potential solutions.
            Focus on industry-specific insights that would inform solution development.
            """

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await self.llm.ainvoke(messages)

            # Parse response into structured format
            context_text = response.content

            # Extract structured information (simplified approach)
            industry_context = IndustryContext(
                industry_trends=self._extract_list_items(context_text, "trends"),
                common_challenges=self._extract_list_items(context_text, "challenges"),
                emerging_solutions=self._extract_list_items(context_text, "solutions"),
                regulatory_landscape=self._extract_list_items(context_text, "regulatory"),
                technology_evolution=self._extract_list_items(context_text, "technology"),
                competitive_pressures=self._extract_list_items(context_text, "competitive"),
                best_practices=self._extract_list_items(context_text, "best practices"),
                case_studies=self._extract_list_items(context_text, "case studies")
            )

            logger.info("PROBLEM_ANALYSIS: Industry context generated")
            return industry_context

        except Exception as e:
            logger.error(f"PROBLEM_ANALYSIS: Industry context generation failed: {e}")
            return IndustryContext(
                industry_trends=["Digital transformation", "Modernization initiatives"],
                common_challenges=["Legacy system limitations", "Resource constraints"],
                emerging_solutions=["Cloud adoption", "Process automation"],
                regulatory_landscape=["Compliance requirements", "Security standards"],
                technology_evolution=["Modern platforms", "Integration capabilities"],
                competitive_pressures=["Efficiency demands", "Service quality expectations"],
                best_practices=["Phased implementation", "Stakeholder engagement"],
                case_studies=["Similar organizational transformations"]
            )

    def _identify_industry(self, organization_name: str) -> str:
        """Identify industry sector from organization name."""
        org_lower = organization_name.lower()

        if any(term in org_lower for term in ["department", "agency", "bureau", "office", "government"]):
            return "Government/Public Sector"
        elif any(term in org_lower for term in ["defense", "military", "army", "navy", "air force"]):
            return "Defense/Military"
        elif any(term in org_lower for term in ["health", "medical", "hospital"]):
            return "Healthcare"
        elif any(term in org_lower for term in ["education", "school", "university"]):
            return "Education"
        elif any(term in org_lower for term in ["transportation", "transit", "highway"]):
            return "Transportation"
        else:
            return "Government/Public Sector"  # Default for government opportunities

    def _extract_list_items(self, text: str, keyword: str) -> List[str]:
        """Extract list items related to a keyword from text."""
        lines = text.split('\n')
        items = []

        # Look for lines containing the keyword and extract bullet points or numbered items
        keyword_found = False
        for line in lines:
            line = line.strip()
            if keyword.lower() in line.lower():
                keyword_found = True
                continue

            if keyword_found:
                # Stop if we hit another section
                if any(section in line.lower() for section in ["trends", "challenges", "solutions", "regulatory", "technology", "competitive", "practices", "studies"]):
                    if keyword.lower() not in line.lower():
                        break

                # Extract bullet points or numbered items
                if line.startswith(('-', '•', '*')) or (len(line) > 0 and line[0].isdigit()):
                    item = line.lstrip('-•*0123456789. ').strip()
                    if item and len(item) > 10:  # Filter out very short items
                        items.append(item)

        # If no items found, return generic items
        if not items:
            items = [f"Industry-specific {keyword} requiring analysis"]

        return items[:5]  # Limit to 5 items per category

    async def _identify_solution_requirements(
        self,
        root_cause_analysis: Dict[str, Any],
        stakeholder_analysis: Dict[str, Any],
        industry_context: IndustryContext
    ) -> SolutionRequirements:
        """Identify comprehensive solution requirements based on problem analysis."""
        logger.info("PROBLEM_ANALYSIS: Identifying solution requirements")

        try:
            system_prompt = """You are a Solution Requirements Expert specializing in translating problem analysis into comprehensive solution specifications.

Your task is to analyze the problem analysis results and generate detailed solution requirements that address the root causes and stakeholder needs.

Generate requirements in these categories:
1. Functional Requirements - What the solution must do
2. Non-Functional Requirements - Performance, reliability, usability criteria
3. Technical Specifications - Technology, architecture, integration needs
4. Compliance Requirements - Regulatory, security, audit requirements
5. Integration Requirements - How solution connects with existing systems
6. Performance Criteria - Measurable success metrics
7. Security Requirements - Data protection, access control, audit trails
8. Scalability Requirements - Growth, volume, expansion capabilities
9. Maintenance Requirements - Support, updates, lifecycle management
10. Training Requirements - User education, change management needs

Return structured JSON with detailed requirements in each category."""

            analysis_context = {
                "root_causes": root_cause_analysis.get("root_causes", []),
                "stakeholder_needs": stakeholder_analysis,
                "industry_context": industry_context.to_dict()
            }

            user_prompt = f"""
            Problem Analysis Results:
            {json.dumps(analysis_context, indent=2)}

            Based on this comprehensive analysis, generate detailed solution requirements that address the identified problems and stakeholder needs.

            Ensure requirements are:
            - Specific and measurable
            - Directly linked to identified problems
            - Aligned with stakeholder needs
            - Informed by industry best practices
            - Technically feasible
            - Compliance-aware

            Return structured JSON with requirements in all specified categories.
            """

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            response = await self.llm.ainvoke(messages)

            try:
                requirements_data = json.loads(response.content)

                # Create SolutionRequirements object
                solution_requirements = SolutionRequirements(
                    functional_requirements=requirements_data.get("functional_requirements", []),
                    non_functional_requirements=requirements_data.get("non_functional_requirements", []),
                    technical_specifications=requirements_data.get("technical_specifications", []),
                    compliance_requirements=requirements_data.get("compliance_requirements", []),
                    integration_requirements=requirements_data.get("integration_requirements", []),
                    performance_criteria=requirements_data.get("performance_criteria", []),
                    security_requirements=requirements_data.get("security_requirements", []),
                    scalability_requirements=requirements_data.get("scalability_requirements", []),
                    maintenance_requirements=requirements_data.get("maintenance_requirements", []),
                    training_requirements=requirements_data.get("training_requirements", [])
                )

                logger.info("PROBLEM_ANALYSIS: Solution requirements identified")
                return solution_requirements

            except json.JSONDecodeError:
                logger.warning("Failed to parse requirements JSON, using structured fallback")
                return self._create_fallback_requirements()

        except Exception as e:
            logger.error(f"PROBLEM_ANALYSIS: Solution requirements identification failed: {e}")
            return self._create_fallback_requirements()

    def _create_fallback_requirements(self) -> SolutionRequirements:
        """Create fallback solution requirements."""
        return SolutionRequirements(
            functional_requirements=[
                "Address identified operational challenges",
                "Improve system performance and reliability",
                "Enhance user experience and accessibility"
            ],
            non_functional_requirements=[
                "High availability and reliability",
                "Scalable architecture",
                "User-friendly interface"
            ],
            technical_specifications=[
                "Modern, secure technology stack",
                "API-based integration capabilities",
                "Cloud-ready architecture"
            ],
            compliance_requirements=[
                "Meet applicable regulatory standards",
                "Ensure data privacy and security",
                "Maintain audit trails"
            ],
            integration_requirements=[
                "Integrate with existing systems",
                "Support data migration",
                "Maintain operational continuity"
            ],
            performance_criteria=[
                "Improved operational efficiency",
                "Reduced processing time",
                "Enhanced user satisfaction"
            ],
            security_requirements=[
                "Robust access controls",
                "Data encryption",
                "Security monitoring"
            ],
            scalability_requirements=[
                "Support growth in users and data",
                "Flexible resource allocation",
                "Modular expansion capabilities"
            ],
            maintenance_requirements=[
                "Regular updates and patches",
                "Ongoing technical support",
                "Performance monitoring"
            ],
            training_requirements=[
                "User training programs",
                "Technical documentation",
                "Change management support"
            ]
        )

    async def _synthesize_problem_statements(
        self,
        root_cause_analysis: Dict[str, Any],
        stakeholder_analysis: Dict[str, Any],
        solution_requirements: SolutionRequirements
    ) -> List[ProblemStatement]:
        """Synthesize comprehensive problem statements from analysis results."""
        logger.info("PROBLEM_ANALYSIS: Synthesizing problem statements")

        try:
            # Extract root causes for problem statement creation
            root_causes = root_cause_analysis.get("root_causes", [])
            surface_problems = root_cause_analysis.get("surface_problems", [])

            problem_statements = []

            # Create problem statements for each major root cause
            for i, root_cause in enumerate(root_causes[:5]):  # Limit to 5 major problems
                problem_id = f"PROB_{i+1:03d}"

                # Determine problem category
                category = self._categorize_problem(root_cause)

                # Determine severity
                severity = self._assess_problem_severity(root_cause, stakeholder_analysis)

                # Identify affected stakeholders
                affected_stakeholders = self._identify_affected_stakeholders(root_cause, stakeholder_analysis)

                # Create comprehensive problem statement
                problem_statement = ProblemStatement(
                    problem_id=problem_id,
                    title=self._generate_problem_title(root_cause),
                    description=root_cause,
                    category=category,
                    severity=severity,
                    root_causes=[root_cause],
                    affected_stakeholders=affected_stakeholders,
                    current_impact=self._assess_current_impact(root_cause, stakeholder_analysis),
                    future_consequences=self._assess_future_consequences(root_cause),
                    success_metrics=self._define_success_metrics(root_cause, solution_requirements),
                    constraints=self._identify_constraints(root_cause),
                    timeline_urgency=self._assess_timeline_urgency(severity),
                    budget_implications=self._assess_budget_implications(root_cause),
                    regulatory_requirements=solution_requirements.compliance_requirements[:3],
                    technical_dependencies=solution_requirements.technical_specifications[:3]
                )

                problem_statements.append(problem_statement)

            logger.info(f"PROBLEM_ANALYSIS: Synthesized {len(problem_statements)} problem statements")
            return problem_statements

        except Exception as e:
            logger.error(f"PROBLEM_ANALYSIS: Problem statement synthesis failed: {e}")
            # Return minimal problem statement
            return [
                ProblemStatement(
                    problem_id="PROB_001",
                    title="Organizational Challenge Analysis",
                    description="Comprehensive analysis of organizational challenges and requirements",
                    category=ProblemCategory.OPERATIONAL_EFFICIENCY,
                    severity=ProblemSeverity.MEDIUM,
                    root_causes=["Operational efficiency requirements"],
                    affected_stakeholders=[StakeholderType.MANAGEMENT, StakeholderType.END_USERS],
                    current_impact="Operational challenges affecting service delivery",
                    future_consequences="Continued inefficiencies without intervention",
                    success_metrics=["Improved operational efficiency", "Enhanced service quality"],
                    constraints=["Budget limitations", "Timeline requirements"],
                    timeline_urgency="Medium priority implementation",
                    budget_implications="Cost-effective solution required",
                    regulatory_requirements=["Compliance with applicable standards"],
                    technical_dependencies=["Integration with existing systems"]
                )
            ]

    def _categorize_problem(self, problem_description: str) -> ProblemCategory:
        """Categorize a problem based on its description."""
        desc_lower = problem_description.lower()

        if any(term in desc_lower for term in ["security", "cyber", "breach", "vulnerability"]):
            return ProblemCategory.SECURITY_RISK
        elif any(term in desc_lower for term in ["compliance", "regulatory", "audit", "standard"]):
            return ProblemCategory.COMPLIANCE_REGULATORY
        elif any(term in desc_lower for term in ["technology", "system", "software", "hardware", "modernization"]):
            return ProblemCategory.TECHNOLOGY_MODERNIZATION
        elif any(term in desc_lower for term in ["infrastructure", "network", "server", "upgrade"]):
            return ProblemCategory.INFRASTRUCTURE_UPGRADE
        elif any(term in desc_lower for term in ["process", "workflow", "procedure", "improvement"]):
            return ProblemCategory.PROCESS_IMPROVEMENT
        elif any(term in desc_lower for term in ["capacity", "scale", "volume", "growth"]):
            return ProblemCategory.CAPACITY_SCALING
        elif any(term in desc_lower for term in ["cost", "budget", "expense", "optimization"]):
            return ProblemCategory.COST_OPTIMIZATION
        elif any(term in desc_lower for term in ["workforce", "training", "skill", "capability"]):
            return ProblemCategory.WORKFORCE_CAPABILITY
        elif any(term in desc_lower for term in ["innovation", "transformation", "digital"]):
            return ProblemCategory.INNOVATION_TRANSFORMATION
        else:
            return ProblemCategory.OPERATIONAL_EFFICIENCY

    def _assess_problem_severity(self, problem_description: str, stakeholder_analysis: Dict[str, Any]) -> ProblemSeverity:
        """Assess the severity of a problem."""
        desc_lower = problem_description.lower()

        # High severity indicators
        if any(term in desc_lower for term in ["critical", "urgent", "failure", "breach", "crisis"]):
            return ProblemSeverity.CRITICAL
        elif any(term in desc_lower for term in ["significant", "major", "important", "compliance"]):
            return ProblemSeverity.HIGH
        elif any(term in desc_lower for term in ["minor", "small", "limited"]):
            return ProblemSeverity.LOW
        else:
            return ProblemSeverity.MEDIUM

    def _identify_affected_stakeholders(self, problem_description: str, stakeholder_analysis: Dict[str, Any]) -> List[StakeholderType]:
        """Identify stakeholders affected by a problem."""
        desc_lower = problem_description.lower()
        affected = []

        if any(term in desc_lower for term in ["user", "citizen", "customer", "public"]):
            affected.append(StakeholderType.END_USERS)
        if any(term in desc_lower for term in ["management", "executive", "leadership"]):
            affected.append(StakeholderType.MANAGEMENT)
        if any(term in desc_lower for term in ["technical", "it", "engineer", "developer"]):
            affected.append(StakeholderType.TECHNICAL_STAFF)
        if any(term in desc_lower for term in ["compliance", "audit", "regulatory"]):
            affected.append(StakeholderType.REGULATORY_BODIES)
        if any(term in desc_lower for term in ["budget", "cost", "financial"]):
            affected.append(StakeholderType.BUDGET_AUTHORITIES)

        # Default to management and end users if none identified
        if not affected:
            affected = [StakeholderType.MANAGEMENT, StakeholderType.END_USERS]

        return affected

    def _assess_current_impact(self, problem_description: str, stakeholder_analysis: Dict[str, Any]) -> str:
        """Assess current impact of the problem."""
        return f"Current operational challenges related to: {problem_description[:100]}..."

    def _assess_future_consequences(self, problem_description: str) -> str:
        """Assess future consequences if problem is not addressed."""
        return f"Continued challenges and potential escalation of issues related to: {problem_description[:100]}..."

    def _define_success_metrics(self, problem_description: str, solution_requirements: SolutionRequirements) -> List[str]:
        """Define success metrics for addressing the problem."""
        return [
            "Improved operational efficiency",
            "Enhanced system performance",
            "Increased user satisfaction",
            "Reduced operational costs",
            "Better compliance adherence"
        ]

    def _identify_constraints(self, problem_description: str) -> List[str]:
        """Identify constraints for addressing the problem."""
        return [
            "Budget limitations",
            "Timeline requirements",
            "Technical compatibility",
            "Regulatory compliance",
            "Operational continuity"
        ]

    def _assess_timeline_urgency(self, severity: ProblemSeverity) -> str:
        """Assess timeline urgency based on severity."""
        if severity == ProblemSeverity.CRITICAL:
            return "Immediate action required"
        elif severity == ProblemSeverity.HIGH:
            return "High priority implementation"
        elif severity == ProblemSeverity.MEDIUM:
            return "Medium priority implementation"
        else:
            return "Low priority implementation"

    def _assess_budget_implications(self, problem_description: str) -> str:
        """Assess budget implications of addressing the problem."""
        return "Cost-effective solution required with clear ROI justification"

    def _generate_problem_title(self, problem_description: str) -> str:
        """Generate a concise title for the problem."""
        # Extract key terms and create a title
        words = problem_description.split()[:8]  # Take first 8 words
        title = " ".join(words)
        if len(title) > 60:
            title = title[:57] + "..."
        return title.title()

    def _calculate_confidence_score(
        self,
        initial_problems: List[str],
        research_insights: Dict[str, Any],
        problem_statements: List[ProblemStatement]
    ) -> float:
        """Calculate confidence score for the analysis."""
        score = 0.5  # Base score

        # Add points for data availability
        if initial_problems:
            score += 0.1
        if research_insights.get("web_search_insights"):
            score += 0.2
        if research_insights.get("academic_research"):
            score += 0.1
        if len(problem_statements) > 0:
            score += 0.1

        return min(score, 1.0)  # Cap at 1.0

    async def _generate_strategic_recommendations(
        self,
        problem_statements: List[ProblemStatement],
        solution_requirements: SolutionRequirements,
        industry_context: IndustryContext
    ) -> List[str]:
        """Generate strategic recommendations based on analysis."""
        recommendations = [
            "Implement phased approach to address critical problems first",
            "Ensure stakeholder engagement throughout solution implementation",
            "Leverage industry best practices and proven solutions",
            "Establish clear success metrics and monitoring mechanisms",
            "Plan for change management and user training",
            "Consider long-term scalability and maintenance requirements"
        ]

        # Add specific recommendations based on problem categories
        categories = [ps.category for ps in problem_statements]
        if ProblemCategory.SECURITY_RISK in categories:
            recommendations.append("Prioritize security requirements and compliance")
        if ProblemCategory.TECHNOLOGY_MODERNIZATION in categories:
            recommendations.append("Focus on modern, scalable technology solutions")
        if ProblemCategory.PROCESS_IMPROVEMENT in categories:
            recommendations.append("Emphasize process optimization and automation")

        return recommendations[:10]  # Limit to 10 recommendations
