from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from loguru import logger
from services.simulation.simulation_service import SimulationService
from services.scheduler_service.schedule_lock_service import ScheduleLockService

class SimulationSchedulerService:
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = False
        self.simulation_service = SimulationService()
        self.lock_service = ScheduleLockService("SIMULATION_SCHEDULER_LOCK", "SIMULATION_SCHEDULER")

    async def process_simulation_queue(self):
        if not self.is_enabled:
            logger.info("Simulation scheduler is disabled, skipping processing")
            return
        acquired = await self.lock_service.try_acquire_lock()
        if not acquired:
            logger.info("Simulation scheduler lock not acquired, skipping this run")
            return
        try:
            await self.simulation_service.process_simulation_queue(limit=5)
        finally:
            await self.lock_service.release_lock()

    def start(self, interval_seconds: int = 120):
        if self.is_running:
            logger.warning("Simulation scheduler is already running")
            return
        self.scheduler.add_job(
            self.process_simulation_queue,
            IntervalTrigger(seconds=interval_seconds),
            id="process_simulation_queue",
            name="Process Simulation Queue"
        )
        self.scheduler.start()
        self.is_running = True
        logger.info(f"Simulation scheduler started with {interval_seconds} second interval")

    def stop(self):
        if not self.is_running:
            logger.warning("Simulation scheduler is not running")
            return
        self.scheduler.shutdown()
        self.is_running = False
        logger.info("Simulation scheduler stopped")

    def enable(self):
        self.is_enabled = True
        logger.info("Simulation scheduler enabled")

    def disable(self):
        self.is_enabled = False
        logger.info("Simulation scheduler disabled")
        
    def is_scheduler_enabled(self) -> bool:
        """Check if the simulation scheduler is enabled"""
        return self.is_enabled

    def get_status(self):
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "jobs": [
                {
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                    "trigger": str(job.trigger)
                }
                for job in self.scheduler.get_jobs()
            ]
        }