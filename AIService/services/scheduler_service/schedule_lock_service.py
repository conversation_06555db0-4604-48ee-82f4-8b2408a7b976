from datetime import datetime
from sqlalchemy import select, update, insert
from database import get_customer_db
from loguru import logger
import uuid



from models.kontratar_models import Lock

# using tenant Id field for backward compatibility
INSTANCE_ID = uuid.uuid4().hex

class ScheduleLockService:
    def __init__(self, lock_id: str, lock_type: str, timeout_seconds: int = 14400):
        self.lock_id = lock_id
        self.lock_type = lock_type
        self.timeout_seconds = timeout_seconds

    async def try_acquire_lock(self):
        async for db in get_customer_db():
            now = datetime.utcnow()
            # Check if lock exists
            result = await db.execute(
            select(Lock).where(Lock.lock_id == self.lock_id)
            )
            lock = result.scalar_one_or_none()
            if not lock:
                # Create and acquire lock
                new_lock = Lock(
                    lock_id=self.lock_id,
                    is_processing=True,
                    lock_acquired_at=now,
                    type=self.lock_type,
                    tenant_id=INSTANCE_ID
                )
                db.add(new_lock)
                await db.commit()
                logger.info(f"Lock {self.lock_id} acquired for the first time by {INSTANCE_ID}")
                return True
            # Check if lock is held and not stale
            if lock.is_processing and (now - lock.lock_acquired_at).total_seconds() < self.timeout_seconds:
                logger.info(f"Lock {self.lock_id} is already held {lock.tenant_id}, skipping job")
                return False
            # Acquire lock
            await db.execute(
                update(Lock).where(Lock.lock_id == self.lock_id).values(
                    is_processing=True,
                    lock_acquired_at=now,
                    tenant_id=INSTANCE_ID
                )
            )
            await db.commit()
            logger.info(f"Lock {self.lock_id} acquired")
            return True

    async def release_lock(self):
        async for db in get_customer_db():
            await db.execute(
                update(Lock)
                .where(Lock.lock_id == self.lock_id, Lock.tenant_id == INSTANCE_ID)
                .values(is_processing=False, lock_acquired_at=datetime.utcnow())
            )
            await db.commit()
            logger.info(f"Lock {self.lock_id} released")
            
            
    async def release_all_locks_for_instance():
        async for db in get_customer_db():
            await db.execute(
                update(Lock)
                .where(Lock.tenant_id == INSTANCE_ID)
                .values(is_processing=False, lock_acquired_at=datetime.utcnow())
            )
            await db.commit()
            logger.info(f"All locks released for instance {INSTANCE_ID}")
            break