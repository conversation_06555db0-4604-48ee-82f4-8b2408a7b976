import asyncio
from typing import AsyncGenerator, List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from langchain_core.messages import BaseMessage, HumanMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain.schema import Document

from services.llm.llm_factory import get_llm
from services.chroma.chroma_service import ChromaService
from services.chat.thread_service import ThreadService
from services.chat.memory_service import MemoryService
from services.chat.web_search_service import WebSearchService
from services.chat.title_generation_service import TitleGenerationService
from models.chat_models import ChatThread, ChatMessage
from schemas.chat_schemas import ChatRequest
from database import get_kontratar_db
from loguru import logger


class EnhancedChatService:
    """Enhanced chat service with thread management, memory, and streaming support"""
    
    def __init__(self):
        # Configure LLM with streaming enabled
        self.llm = get_llm(
            temperature=0.0,
            # Add streaming-specific parameters
            streaming=True,
            # For Ollama models - these help with streaming
            num_predict=2048,  # Max tokens to generate
            repeat_penalty=1.1,
            top_k=40,
            top_p=0.9,
        )
        
        self.chroma_service = ChromaService("http://ai.kontratar.com:5000", None)
        self.memory_service = MemoryService()
        self.web_search_service = WebSearchService()
        self.title_service = TitleGenerationService()
        
        # System prompt for government solicitation assistance
        self.system_prompt = """You are a Government Solicitation Expert Assistant.

Your role is to help users understand and respond to government opportunities and solicitations.

Guidelines:
1. Provide clear, accurate, and professional responses
2. Base your answers on the provided context when available
3. If you don't have enough information, clearly state this
4. Use formal language appropriate for government documents
5. Structure your responses logically with clear sections when needed
6. Remember the conversation context and refer to previous discussions when relevant

Always maintain a helpful and professional tone while ensuring accuracy in your responses."""

    def format_sse_message(self, data: str, event: str = "message", id: Optional[str] = None) -> str:
        """Format data as Server-Sent Events message"""
        message = ""
        if id:
            message += f"id: {id}\n"
        if event:
            message += f"event: {event}\n"
        # Handle multiline data
        for line in data.splitlines():
            message += f"data: {line}\n"
        message += "\n"  # Double newline to end the message
        return message

    async def chat(
        self,
        db: AsyncSession,
        request: ChatRequest,
        tenant_id: str,
        streaming: bool = True,
        sse_format: bool = False
    ) -> AsyncGenerator[str, None]:
        """
        Main chat method that handles streaming responses
        """
        try:
            # Get or create thread
            thread = await self._get_or_create_thread(
                db, request, tenant_id
            )

            # Add user message to thread
            user_message = await ThreadService.add_message(
                db=db,
                thread_id=thread.id,
                role="user",
                content=request.message,
                tenant_id=tenant_id,
                metadata={"opportunity_id": request.opportunity_id, "source": request.source}
            )
            
            if not user_message:
                raise Exception("Failed to save user message")
            
            # Get conversation history
            messages = await ThreadService.get_thread_messages(
                db, thread.id, tenant_id
            )

            # Get relevant context from ChromaDB
            context_chunks = await self._get_relevant_context(
                request.message,
                request.opportunity_id or "",
                tenant_id,
                request.source or "",
                request.max_chunks
            )

            # Prepare conversation for LLM
            conversation_messages = await self.memory_service.trim_chat_history(
                messages[:-1],
                opportunity_context=f"Opportunity ID: {request.opportunity_id}" if request.opportunity_id else None
            )

            # Check if web search is requested
            if request.web_search and self.web_search_service.is_available():
                async for chunk in self._stream_web_search_response(
                    db, thread, user_message, conversation_messages,
                    request.message, context_chunks, tenant_id, sse_format
                ):
                    yield chunk
            else:
                async for chunk in self._stream_response(
                    db, thread, user_message, conversation_messages,
                    request.message, context_chunks, tenant_id, sse_format
                ):
                    yield chunk
                
        except Exception as e:
            logger.error(f"Error in chat service: {e}")
            if sse_format:
                yield self.format_sse_message(f"Error: {str(e)}", event="error")
            else:
                yield f"Error: {str(e)}"

    async def _get_or_create_thread(
        self,
        db: AsyncSession,
        request: ChatRequest,
        tenant_id: str
    ) -> ChatThread:
        """Get existing thread or create a new one"""
        if request.thread_id:
            thread = await ThreadService.get_thread(
                db, request.thread_id, tenant_id
            )
            if thread:
                return thread

        from schemas.chat_schemas import ThreadCreateRequest

        title = await self.title_service.generate_title(request.message)

        thread_request = ThreadCreateRequest(
            title=title,
            opportunity_id=request.opportunity_id,
            tenant_id=tenant_id,
            source=request.source
        )

        return await ThreadService.create_thread(
            db, thread_request
        )

    async def _get_relevant_context(
        self,
        question: str,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        max_chunks: int
    ) -> List[str]:
        """Get relevant context chunks from ChromaDB"""
        try:
            async for db in get_kontratar_db():
                collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id

                if not collection_name.strip():
                    return []

                relevant_chunks = await self.chroma_service.get_relevant_chunks(
                    db, collection_name, question, n_results=max_chunks
                )

                # Clean chunks
                cleaned_chunks = [
                    chunk.replace("\n", " ").replace("\t", " ")
                    for chunk in relevant_chunks
                ]

                logger.info(f"Retrieved {len(cleaned_chunks)} relevant chunks")
                return cleaned_chunks

        except Exception as e:
            logger.error(f"Error getting relevant context: {e}")
            return []

    async def _stream_response(
        self,
        db: AsyncSession,
        thread: ChatThread,
        user_message: ChatMessage,
        conversation_messages: List[BaseMessage],
        current_question: str,
        context_chunks: List[str],
        tenant_id: str,
        sse_format: bool = False
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response with improved chunk handling"""
        try:
            # Create documents from context
            documents = [Document(page_content=chunk) for chunk in context_chunks]
            
            # Create the prompt template
            prompt = ChatPromptTemplate.from_messages([
                ("system", self.system_prompt),
                MessagesPlaceholder(variable_name="chat_history"),
                ("human", "{input}"),
                ("system", "Context: {context}")
            ])
            
            # Create the chain
            document_chain = create_stuff_documents_chain(self.llm, prompt)
            
            # Stream the response with better chunking
            response_content = ""
            chunk_buffer = ""
            word_count = 0
            last_yield_time = asyncio.get_event_loop().time()
            message_id = 0
            
            logger.info("Starting streaming response generation")
            
            async for chunk in document_chain.astream({
                "input": current_question,
                "context": documents,
                "chat_history": conversation_messages
            }):
                if chunk:
                    chunk_buffer += chunk
                    response_content += chunk
                    word_count += len(chunk.split())
                    current_time = asyncio.get_event_loop().time()
                    
                    # Yield chunks based on multiple conditions for better streaming experience
                    should_yield = (
                        len(chunk_buffer) >= 15 or  # Minimum chunk size
                        word_count >= 3 or  # After a few words
                        any(punct in chunk_buffer for punct in ['.', '!', '?', '\n', ',']) or  # Sentence/clause endings
                        chunk_buffer.endswith(' ') or  # Word boundaries
                        (current_time - last_yield_time) >= 0.1  # Time-based yield (100ms)
                    )
                    
                    if should_yield and chunk_buffer.strip():
                        logger.debug(f"Yielding chunk: '{chunk_buffer[:50]}...'")
                        if sse_format:
                            yield self.format_sse_message(chunk_buffer, event="chunk", id=str(message_id))
                            message_id += 1
                        else:
                            yield chunk_buffer
                        chunk_buffer = ""
                        word_count = 0
                        last_yield_time = current_time
                        
                        # Small delay to make streaming more visible and prevent overwhelming the client
                        await asyncio.sleep(0.03)
            
            # Send any remaining content
            if chunk_buffer.strip():
                logger.debug(f"Yielding final chunk: '{chunk_buffer[:50]}...'")
                if sse_format:
                    yield self.format_sse_message(chunk_buffer, event="chunk", id=str(message_id))
                else:
                    yield chunk_buffer
            
            logger.info(f"Streaming completed. Total response length: {len(response_content)}")
            
            # Save assistant response to database
            await ThreadService.add_message(
                db=db,
                thread_id=thread.id,
                role="assistant",
                content=response_content,
                tenant_id=tenant_id,
                context_chunks=context_chunks,
                metadata={"streaming": True, "total_length": len(response_content)}
            )
            
        except Exception as e:
            logger.error(f"Error in streaming response: {e}")
            if sse_format:
                yield self.format_sse_message(f"Error: {str(e)}", event="error")
            else:
                yield f"Error: {str(e)}"

    async def _stream_web_search_response(
        self,
        db: AsyncSession,
        thread: ChatThread,
        user_message: ChatMessage,
        conversation_messages: List[BaseMessage],
        current_question: str,
        context_chunks: List[str],
        tenant_id: str,
        sse_format: bool = False
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response with web search"""
        try:
            # Create conversation history string for web search
            conversation_history = ""
            if conversation_messages:
                history_parts = []
                for msg in conversation_messages[-5:]:  # Last 5 messages for context
                    if hasattr(msg, 'content'):
                        role = "User" if msg.__class__.__name__ == "HumanMessage" else "Assistant"
                        history_parts.append(f"{role}: {msg.content[:200]}...")
                conversation_history = "\n".join(history_parts)

            logger.info("Starting web search response")

            # Stream web search response with better chunking
            response_content = ""
            chunk_buffer = ""
            message_id = 0
            
            async for chunk in self.web_search_service.search_and_stream(
                query=current_question,
                context_chunks=context_chunks,
                conversation_history=conversation_history
            ):
                if chunk:
                    chunk_buffer += chunk
                    response_content += chunk
                    
                    # Similar chunking logic as regular streaming
                    if (len(chunk_buffer) >= 15 or 
                        any(punct in chunk_buffer for punct in ['.', '!', '?', '\n']) or
                        chunk_buffer.endswith(' ')):
                        
                        if sse_format:
                            yield self.format_sse_message(chunk_buffer, event="chunk", id=str(message_id))
                            message_id += 1
                        else:
                            yield chunk_buffer
                        chunk_buffer = ""
                        await asyncio.sleep(0.03)

            # Send any remaining content
            if chunk_buffer.strip():
                if sse_format:
                    yield self.format_sse_message(chunk_buffer, event="chunk", id=str(message_id))
                else:
                    yield chunk_buffer

            # Save assistant response to database
            await ThreadService.add_message(
                db=db,
                thread_id=thread.id,
                role="assistant",
                content=response_content,
                tenant_id=tenant_id,
                context_chunks=context_chunks,
                metadata={"streaming": True, "web_search": True}
            )

            logger.info("Web search response completed")

        except Exception as e:
            logger.error(f"Error in web search streaming response: {e}")
            if sse_format:
                yield self.format_sse_message(f"Error: {str(e)}", event="error")
            else:
                yield f"Error: {str(e)}"

    @staticmethod
    def validate_message(message: str) -> bool:
        """Validate chat message"""
        if not message or not message.strip():
            return False
        
        if len(message) > 10000:  # Max message length
            return False
            
        return True