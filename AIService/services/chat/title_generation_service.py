from typing import Optional
from services.llm.llm_factory import get_llm
from loguru import logger


class TitleGenerationService:
    """Service for generating meaningful chat thread titles"""
    
    def __init__(self):
        self.llm = get_llm(temperature=0.3)
    
    async def generate_title(self, message: str) -> str:
        """
        Generate a meaningful title based on the user's first message
        """
        try:
            prompt = self._create_title_prompt(message)
            
            response = await self.llm.ainvoke([("human", prompt)])
            title = response.content.strip()
            
            title = self._clean_title(title)
            
            logger.info(f"Generated title: '{title}' for message: '{message[:50]}...'")
            return title
            
        except Exception as e:
            logger.error(f"Error generating title: {e}")
            # Fallback to a simple title based on the message
            return self._create_fallback_title(message, opportunity_id)
    
    def _create_title_prompt(self, message: str) -> str:
        """Create a prompt for title generation"""
        
        
        prompt = f"""Generate a concise, meaningful title (3-6 words) for a government contracting conversation{message}.

User's first message: "{message}"

Requirements:
- Keep it under 50 characters
- Make it descriptive and professional
- Focus on the main topic or question
- Use title case
- No quotes or special characters
- Be specific to government contracting context

Examples:
- "RFP Requirements Analysis"
- "Cybersecurity Compliance Questions"
- "Proposal Submission Guidelines"
- "Contract Terms Clarification"
- "Technical Specifications Review"

Generate only the title, nothing else:"""
        
        return prompt
    
    def _clean_title(self, title: str) -> str:
        """Clean and validate the generated title"""
        # Remove quotes and extra whitespace
        title = title.strip().strip('"').strip("'")
        
        # Limit length
        if len(title) > 50:
            title = title[:47] + "..."
        
        # Ensure it's not empty
        if not title:
            title = "Government Contracting Chat"
        
        # Capitalize first letter if needed
        if title and title[0].islower():
            title = title[0].upper() + title[1:]
        
        return title
    
    def _create_fallback_title(self, message: str, opportunity_id: Optional[str] = None) -> str:
        """Create a fallback title when LLM generation fails"""
        
        # Extract key words from the message
        message_lower = message.lower()
        
        # Common government contracting keywords
        keywords = {
            'rfp': 'RFP',
            'rfi': 'RFI',
            'proposal': 'Proposal',
            'contract': 'Contract',
            'requirement': 'Requirements',
            'compliance': 'Compliance',
            'security': 'Security',
            'cybersecurity': 'Cybersecurity',
            'technical': 'Technical',
            'specification': 'Specifications',
            'submission': 'Submission',
            'deadline': 'Deadline',
            'evaluation': 'Evaluation',
            'award': 'Award',
            'procurement': 'Procurement',
            'solicitation': 'Solicitation'
        }
        
        # Find matching keywords
        found_keywords = []
        for keyword, display in keywords.items():
            if keyword in message_lower:
                found_keywords.append(display)
        
        # Create title based on found keywords
        if found_keywords:
            if len(found_keywords) == 1:
                title = f"{found_keywords[0]} Discussion"
            else:
                title = f"{found_keywords[0]} & {found_keywords[1]}"
        else:
            # Generic title based on opportunity
            if opportunity_id:
                title = f"Chat about {opportunity_id[:10]}"
            else:
                title = "Government Contracting Chat"
        
        return title[:50]  # Ensure length limit
    
    def update_title_if_generic(self, current_title: str, message: str) -> str:
        """Update title if it's too generic and we have a better message"""
        
        generic_titles = [
            "Government Contracting Chat",
            "Chat about",
            "New Chat",
            "Untitled Chat"
        ]
        
        # Check if current title is generic
        is_generic = any(generic in current_title for generic in generic_titles)
        
        if is_generic and len(message) > 10:
            # Try to create a better title
            return self._create_fallback_title(message)
        
        return current_title
