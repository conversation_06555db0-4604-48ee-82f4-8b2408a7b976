from typing import List, Dict, Any, Optional
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from services.llm.llm_factory import get_llm
from services.chroma.chroma_service import ChromaService
from models.chat_models import ChatMessage
from loguru import logger


class MemoryService:
    """Service for managing chat memory and history trimming"""
    
    def __init__(self):
        self.llm = get_llm(temperature=0.0)
        self.chroma_service = ChromaService("http://ai.kontratar.com:5000", None)
        
        # Configuration
        self.max_messages = 20
        self.keep_recent = 10
        self.summary_threshold = 15
    
    def should_trim_history(self, messages: List[ChatMessage]) -> bool:
        """Check if chat history should be trimmed"""
        return len(messages) > self.max_messages
    
    async def trim_chat_history(
        self, 
        messages: List[ChatMessage],
        opportunity_context: Optional[str] = None
    ) -> List[BaseMessage]:
        """
        Trim chat history by summarizing older messages and keeping recent ones
        Returns a list of LangChain messages ready for the LLM
        """
        if not self.should_trim_history(messages):
            return self._convert_to_langchain_messages(messages)
        
        logger.info(f"Trimming chat history: {len(messages)} messages")
        
        # Sort messages by creation date
        sorted_messages = sorted(messages, key=lambda x: x.created_date)
        
        # Keep recent messages
        recent_messages = sorted_messages[-self.keep_recent:]
        
        # Messages to summarize
        messages_to_summarize = sorted_messages[:-self.keep_recent]
        
        if messages_to_summarize:
            # Create summary of older messages
            summary = await self._create_conversation_summary(
                messages_to_summarize, 
                opportunity_context
            )
            
            # Create system message with summary
            system_message = SystemMessage(content=f"""Previous conversation summary:
{summary}

Continue the conversation based on this context and the recent messages below.""")
            
            # Convert recent messages to LangChain format
            recent_langchain_messages = self._convert_to_langchain_messages(recent_messages)
            
            # Combine summary with recent messages
            return [system_message] + recent_langchain_messages
        
        return self._convert_to_langchain_messages(recent_messages)
    
    async def _create_conversation_summary(
        self, 
        messages: List[ChatMessage],
        opportunity_context: Optional[str] = None
    ) -> str:
        """Create a summary of conversation messages"""
        try:
            # Prepare conversation text
            conversation_text = self._format_messages_for_summary(messages)
            
            # Create summarization prompt
            summary_prompt = f"""Summarize the following conversation between a user and an AI assistant about government solicitations. 
Focus on key topics discussed, important questions asked, and relevant information provided.
Keep the summary concise but informative (2-3 paragraphs maximum).

{f"Opportunity Context: {opportunity_context}" if opportunity_context else ""}

Conversation:
{conversation_text}

Summary:"""
            
            # Get summary from LLM
            response = await self.llm.ainvoke([HumanMessage(content=summary_prompt)])
            summary = response.content.strip()
            
            logger.info(f"Created conversation summary: {len(summary)} characters")
            return summary
            
        except Exception as e:
            logger.error(f"Error creating conversation summary: {e}")
            # Fallback to simple truncation
            return self._create_simple_summary(messages)
    
    def _format_messages_for_summary(self, messages: List[ChatMessage]) -> str:
        """Format messages for summarization"""
        formatted_messages = []
        
        for msg in messages:
            role_label = "User" if msg.role == "user" else "Assistant"
            formatted_messages.append(f"{role_label}: {msg.content}")
        
        return "\n\n".join(formatted_messages)
    
    def _create_simple_summary(self, messages: List[ChatMessage]) -> str:
        """Create a simple summary when LLM summarization fails"""
        user_messages = [msg for msg in messages if msg.role == "user"]
        assistant_messages = [msg for msg in messages if msg.role == "assistant"]
        
        summary = f"Previous conversation included {len(user_messages)} user questions and {len(assistant_messages)} assistant responses. "
        
        if user_messages:
            # Include first and last user questions
            first_question = user_messages[0].content[:100] + "..." if len(user_messages[0].content) > 100 else user_messages[0].content
            summary += f"Topics discussed included: {first_question}"
            
            if len(user_messages) > 1:
                last_question = user_messages[-1].content[:100] + "..." if len(user_messages[-1].content) > 100 else user_messages[-1].content
                summary += f" and {last_question}"
        
        return summary
    
    def _convert_to_langchain_messages(self, messages: List[ChatMessage]) -> List[BaseMessage]:
        """Convert ChatMessage objects to LangChain message format"""
        langchain_messages = []
        
        for msg in messages:
            if msg.role == "user":
                langchain_messages.append(HumanMessage(content=msg.content))
            elif msg.role == "assistant":
                langchain_messages.append(AIMessage(content=msg.content))
            elif msg.role == "system":
                langchain_messages.append(SystemMessage(content=msg.content))
        
        return langchain_messages
    
    async def store_conversation_context(
        self,
        tenant_id: str,
        thread_id: str,
        messages: List[ChatMessage],
        opportunity_id: Optional[str] = None
    ) -> None:
        """
        Store conversation context in ChromaDB for future retrieval
        This is optional and can be used for cross-conversation memory
        """
        try:
            if len(messages) < 3:  # Only store if we have meaningful conversation
                return
            
            # Create conversation summary for storage
            summary = await self._create_conversation_summary(messages)
            
            # Create collection name for thread memories
            collection_name = f"{tenant_id}_thread_memories"
            
            
            logger.info(f"Would store conversation context for thread {thread_id} in ChromaDB")
            
        except Exception as e:
            logger.error(f"Error storing conversation context: {e}")
    
    def get_context_window_size(self) -> int:
        """Get the current context window size for the LLM"""
        return 4096    
    def estimate_token_count(self, messages: List[BaseMessage]) -> int:
        """Rough estimation of token count for messages"""
        total_chars = sum(len(msg.content) for msg in messages)
        return total_chars // 4
