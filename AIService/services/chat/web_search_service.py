import json
from typing import Async<PERSON>enerator, <PERSON>, Optional
from config import settings
from loguru import logger


class WebSearchService:
    """Service for web search using Gemini with Google Search"""
    
    def __init__(self):
        self.api_key = settings.gemini_api_key
        if not self.api_key:
            logger.warning("GEMINI_API_KEY not configured - web search will be disabled")
    
    async def search_and_stream(
        self,
        query: str,
        context_chunks: Optional[List[str]] = None,
        conversation_history: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """
        Perform web search and stream the response using Gemini
        """
        if not self.api_key:
            yield "Web search is not available - GEMINI_API_KEY not configured"
            return
        
        try:
            from google import genai
            from google.genai.types import Tool, GenerateContentConfig, GoogleSearch
            
            client = genai.Client(api_key=self.api_key)
            model_id = "gemini-2.0-flash"
            google_search_tool = Tool(google_search=GoogleSearch())
            
            # Create enhanced prompt with context
            enhanced_prompt = self._create_enhanced_prompt(
                query, context_chunks, conversation_history
            )
            
            # Prepare contents
            contents = [{"text": enhanced_prompt}]
            
            logger.info(f"Starting web search for query: {query[:100]}...")
            
            response = client.models.generate_content(
                model=model_id,
                contents=contents,
                config=GenerateContentConfig(
                    tools=[google_search_tool],
                    response_modalities=["TEXT"],
                )
            )
            
            # Stream the response
            full_reply = ""
            for part in response.candidates[0].content.parts:
                text = part.text
                full_reply += text
                yield text
                
            logger.info(f"Web search completed. Response length: {len(full_reply)} characters")
            
        except ImportError:
            error_msg = "Google GenAI library not installed. Please install: pip install google-genai"
            logger.error(error_msg)
            yield f"Error: {error_msg}"
        except Exception as e:
            error_msg = f"Web search error: {str(e)}"
            logger.error(error_msg)
            yield f"Error: {error_msg}"
    
    def _create_enhanced_prompt(
        self,
        query: str,
        context_chunks: Optional[List[str]] = None,
        conversation_history: Optional[str] = None
    ) -> str:
        """Create an enhanced prompt with context and conversation history"""
        
        prompt_parts = []
        
        # System instruction
        prompt_parts.append("""You are a Government Solicitation Expert Assistant with web search capabilities.

Your role is to help users understand and respond to government opportunities and solicitations by combining:
1. Relevant document context (if provided)
2. Current web search results
3. Your expertise in government contracting

Guidelines:
- Provide comprehensive, accurate, and up-to-date information
- Cite web sources when using information from search results
- Combine document context with web search findings when both are available
- Use professional language appropriate for government documents
- Structure your responses clearly with sections when needed
- If web search provides conflicting information with document context, acknowledge both sources""")
        
        # Add conversation history if available
        if conversation_history:
            prompt_parts.append(f"\nConversation History:\n{conversation_history}")
        
        # Add document context if available
        if context_chunks:
            context_text = "\n\n".join(context_chunks)
            prompt_parts.append(f"\nRelevant Document Context:\n{context_text}")
        
        # Add the current query
        prompt_parts.append(f"\nUser Question: {query}")
        
        # Instructions for response
        prompt_parts.append("""
Please search the web for current information related to this question and provide a comprehensive response that:
1. Incorporates relevant web search findings
2. References the document context if provided
3. Provides actionable insights for government contracting
4. Cites sources appropriately""")
        
        return "\n".join(prompt_parts)
    
    def is_available(self) -> bool:
        """Check if web search is available"""
        return bool(self.api_key)
    
    async def test_connection(self) -> bool:
        """Test if the web search service is working"""
        if not self.api_key:
            return False
        
        try:
            from google import genai
            client = genai.Client(api_key=self.api_key)
            # Simple test query
            response = client.models.generate_content(
                model="gemini-2.0-flash",
                contents=[{"text": "Hello, this is a test."}]
            )
            return bool(response.candidates)
        except Exception as e:
            logger.error(f"Web search test failed: {e}")
            return False
