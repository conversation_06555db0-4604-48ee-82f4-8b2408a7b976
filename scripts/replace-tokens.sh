#!/bin/bash
set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"  # Parent directory (project root)
TOKEN_REPLACEMENT_DIR="${ROOT_DIR}/PythonTokenReplacement/src"
# Properties directory containing central config .ini files (any name ending with .ini)
PROPERTIES_DIR="${ROOT_DIR}/properties"
# Resolved path to central config .ini (determined at runtime)
CENTRAL_ENV_INI=""

# Services are discovered automatically by scanning for directories that contain a .env.ini

# Global variables for command line options
ENV_OVERRIDES=()
VERBOSE=false
NO_BACKUP=false
# Optional config override (set via -c/--config). Must be initialized for set -u.
CONFIG_FILE_ARG=""

# Function to show usage
show_usage() {
    cat <<EOF
Usage: $0 [SERVICE_NAME|all] [OPTIONS]

SERVICE_NAME: Name of a service directory (e.g., AIService)
all:          Process all services that contain a .env.ini file

Options:
  -E name=value    Add environment override (repeatable)
  -c FILE          Config .ini (absolute path or file inside properties/, e.g., dev.ini)
  -v               Verbose output
  --no-backup      Skip creating backups of existing .env files

Notes:
  - Central config is resolved from properties/ (auto-picks single *.ini if present)
  - Each service must contain a .env.ini template

Examples:
  $0 AIService
  $0 all -c dev.ini -v
  $0 AIService -E ENVIRONMENT=production --no-backup
EOF
}

# Parse command line options
parse_options() {
    ENV_OVERRIDES=()
    VERBOSE=false
    NO_BACKUP=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -E)
                if [[ -z "$2" || "$2" == -* ]]; then
                    echo "Error: -E requires a value in format name=value"
                    exit 1
                fi
                ENV_OVERRIDES+=("$2")
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -c|--config)
                if [[ -z "$2" || "$2" == -* ]]; then
                    echo "Error: -c|--config requires a value (file name in properties/ or absolute path to .ini)"
                    exit 1
                fi
                CONFIG_FILE_ARG="$2"
                shift 2
                ;;
            --no-backup)
                NO_BACKUP=true
                shift
                ;;
            -*) 
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
            *)
                # This is the service name, stop parsing options
                break
                ;;
        esac
    done
    
    # Return remaining arguments
    echo "$@"
}

# Resolve central configuration .ini file
# Priority:
#   1) If -c/--config provided: use absolute path or file inside ${PROPERTIES_DIR}
#   2) If ${PROPERTIES_DIR}/.env.ini exists: use it (backward compatibility)
#   3) If exactly one *.ini exists in ${PROPERTIES_DIR}: use it
#   4) Otherwise: error and ask user to specify -c
resolve_central_ini() {
    local override="$1"

    # If override provided
    if [[ -n "$override" ]]; then
        if [[ "$override" = /* ]]; then
            # Absolute path
            if [[ -f "$override" ]]; then
                echo "$override"
                return 0
            else
                echo "Error: Specified config file not found: $override" >&2
                return 1
            fi
        else
            # Treat as file under properties dir
            local candidate="${PROPERTIES_DIR}/${override}"
            if [[ -f "$candidate" ]]; then
                echo "$candidate"
                return 0
            else
                echo "Error: Specified config file not found in properties/: $candidate" >&2
                return 1
            fi
        fi
    fi

    # Backward compatibility: prefer .env.ini if present
    if [[ -f "${PROPERTIES_DIR}/.env.ini" ]]; then
        echo "${PROPERTIES_DIR}/.env.ini"
        return 0
    fi

    # Auto-detect any single *.ini in properties
    mapfile -t ini_files < <(find "${PROPERTIES_DIR}" -maxdepth 1 -type f -name "*.ini" | sort)
    if [[ ${#ini_files[@]} -eq 1 ]]; then
        echo "${ini_files[0]}"
        return 0
    fi

    echo "Error: Could not determine central config .ini in ${PROPERTIES_DIR}." >&2
    echo "Specify with -c <file-in-properties> or -c /absolute/path/to/file.ini" >&2
    echo "Candidates:" >&2
    printf '  - %s\n' "${ini_files[@]}" >&2 || true
    return 1
}

# Function to check if a service has required files
check_service_files() {
    local service_dir="$1"
    local env_template="${service_dir}/.env.ini"
    
    # Only check for .env.ini since we use centralized properties/*.ini
    if [ -f "$env_template" ]; then
        return 0  # Template file exists
    else
        return 1  # Missing template file
    fi
}

# Function to discover available services
discover_services() {
    # Find first-level directories containing a .env.ini, exclude non-service dirs
    local found
    mapfile -t found < <(find "$ROOT_DIR" -maxdepth 2 -mindepth 2 -type f -name ".env.ini" -printf '%h\n' 2>/dev/null | sed 's|.*/||' | sort -u)
    local services=()
    for name in "${found[@]}"; do
        case "$name" in
            properties|scripts|PythonTokenReplacement|.git|.github) continue ;;
        esac
        services+=("$name")
    done
    echo "${services[@]}"
}

# Function to build command line arguments for Python utility
build_python_args() {
    local service_name="$1"
    local template_file="$2"
    local output_file="$3"
    
    local args=()
    args+=("-c" "$CENTRAL_ENV_INI")
    args+=("-t" "$template_file")
    args+=("-o" "$output_file")
    args+=("-s" "$service_name")
    
    # Add environment overrides
    for override in "${ENV_OVERRIDES[@]}"; do
        args+=("-E" "$override")
    done
    
    # Add verbose flag
    if [ "$VERBOSE" = true ]; then
        args+=("-v")
    fi
    
    # Add no-backup flag
    if [ "$NO_BACKUP" = true ]; then
        args+=("--no-backup")
    fi
    
    echo "${args[@]}"
}

# Function to process a single service
process_service() {
    local service_name="$1"
    local service_dir="${ROOT_DIR}/${service_name}"
    
    # Input files - using centralized properties/*.ini
    local env_template_file="${service_dir}/.env.ini"
    local env_output_file="${service_dir}/.env"
    
    echo "==> Processing: $service_name"
    echo "    Config: $CENTRAL_ENV_INI"
    
    if [ ${#ENV_OVERRIDES[@]} -gt 0 ]; then
        echo "Environment overrides: ${ENV_OVERRIDES[*]}"
    fi
    echo ""
    
    # Check if centralized config file exists
    if [ ! -f "$CENTRAL_ENV_INI" ]; then
        echo "Error: Central configuration file not found: $CENTRAL_ENV_INI"
        return 1
    fi

    # Check if template file exists
    if [ ! -f "$env_template_file" ]; then
        echo "Error: Template file not found: $env_template_file"
        return 1
    fi

    # Build Python command arguments
    local python_args
    read -ra python_args <<< "$(build_python_args "$service_name" "$env_template_file" "$env_output_file")"
    
    # Run token replacement
    echo "    Template: $env_template_file"
    echo "    Output:   $env_output_file"
    
    if [ "$VERBOSE" = true ]; then
        echo "Command: python3 main.py ${python_args[*]}"
    fi
    echo ""

    cd "$TOKEN_REPLACEMENT_DIR"
    if python3 main.py "${python_args[@]}"; then
        echo "    ✅ Generated: $env_output_file"
        
        # Check if backup was created
        local backup_dir="${service_dir}/backup"
        if [ -d "$backup_dir" ] && [ "$NO_BACKUP" != true ]; then
            local latest_backup
            latest_backup=$(find "$backup_dir" -name ".env-*" -type f | sort | tail -1 2>/dev/null || echo "")
            if [ -n "$latest_backup" ]; then
                echo "    Backup: $latest_backup"
            fi
        fi
        return 0
    else
        echo "❌ Token replacement failed for $service_name"
        return 1
    fi
}

# Main script logic
main() {
    # Parse command line options
    local remaining_args
    read -ra remaining_args <<< "$(parse_options "$@")"
    local target_service="${remaining_args[0]}"

    # Resolve central config ini now that options are parsed
    CENTRAL_ENV_INI="$(resolve_central_ini "$CONFIG_FILE_ARG")" || exit 1
    
    # Discover available services
    local available_services=($(discover_services))
    
    if [ ${#available_services[@]} -eq 0 ]; then
        echo "❌ No services found with .env.ini files."
        echo ""
        echo "To set up a service for token replacement, ensure it has:"
        echo "  - .env.ini file (template with &token; placeholders)"
        echo ""
        echo "The centralized configuration file will be used from:"
        echo "  - $CENTRAL_ENV_INI"
        echo ""
        echo "Template files support:"
        echo "  - &token; syntax for variable substitution"
        echo "  - #if VARIABLE=value ... #endif conditional blocks"
        echo "  - #else clauses within conditional blocks"
        exit 1
    fi
    
    # If no argument provided, show available services
    if [ -z "$target_service" ]; then
        echo "Found ${#available_services[@]} service(s) with .env.ini: ${available_services[*]}"
        echo "Config: $CENTRAL_ENV_INI"
        show_usage
        exit 0
    fi
    
    # Handle "all" parameter
    if [ "$target_service" = "all" ]; then
        local success_count=0
        local total_count=${#available_services[@]}
        printf 'Processing %d service(s): %s\n' "$total_count" "${available_services[*]}"
        for service in "${available_services[@]}"; do
            process_service "$service" && ((success_count++)) || true
        done
        printf 'Summary: %d/%d succeeded\n' "$success_count" "$total_count"
        [ $success_count -eq $total_count ]
        exit $?
    fi
    
    # Check if specified service exists and is valid
    local service_found=false
    for service in "${available_services[@]}"; do
        if [ "$service" = "$target_service" ]; then
            service_found=true
            break
        fi
    done
    
    if [ "$service_found" = false ]; then
        echo "❌ Service '$target_service' not found or missing required files."
        echo ""
        echo "Available: ${available_services[*]}"
        show_usage
        exit 1
    fi
    
    # Process the specified service
    if process_service "$target_service"; then
        echo "🎉 All done! You can now run your $target_service with the updated configuration."
    else
        exit 1
    fi
}

# Run main function with all arguments
main "$@"